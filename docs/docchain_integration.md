# DocChain 集成指南

本文档描述了如何在DeerFlow项目中集成和使用DocChain搜索功能以及方法调用链分析功能。

## 概述

- **DocChain**: 一个集成文件向量化、存储、搜索为一体的平台。DeerFlow现在支持通过DocChain search工具来搜索已上传到DocChain的文档内容。
- **方法调用链分析**: 通过AST解析服务分析Java代码中指定方法的调用关系，帮助理解代码结构和依赖关系。

## 配置

### 1. DocChain配置

在`conf.yaml`文件中添加DocChain配置：

```yaml
# DocChain configuration for document search
DOCCHAIN:
  base_url: "https://your-docchain-instance.com"
  api_key: "your_docchain_api_key"
```

### 2. 方法调用链分析配置

方法调用链分析工具使用固定的AST解析服务地址：`http://10.10.176.213:9090`
如需修改，可以在代码中调整`MethodCallChainAnalyzer`类的`base_url`参数。

### 3. 环境变量（可选）

您也可以通过环境变量设置DocChain配置：

```bash
export DOCCHAIN_BASE_URL="https://your-docchain-instance.com"
export DOCCHAIN_API_KEY="your_docchain_api_key"
```

## 功能特性

### 支持的Agent

以下工具已集成到相应的Agent中：

- **Researcher**: 
  - DocChain搜索：获取已上传的文档信息
  - 方法调用链分析：分析代码结构和调用关系
- **Coder**: 
  - DocChain搜索：查找代码相关的文档
  - 方法调用链分析：分析方法调用关系，辅助代码理解

### DocChain搜索功能

1. **Topic列表**: 如果不提供topic_id，工具会列出所有可用的topics
2. **文档搜索**: 在指定的topic中搜索相关内容
3. **多种内容类型**: 支持搜索文本、表格、图像等不同类型的内容
4. **文件完整读取**: 通过doc_id读取文档的完整内容

### DocChain文件读取功能

1. **完整内容获取**: 根据doc_id获取文档的完整内容
2. **多种格式支持**: 支持markdown等多种读取格式
3. **大文件处理**: 支持大文件的分页读取
4. **代码文件完整分析**: 特别适用于获取完整的代码文件内容

### 方法调用链分析功能

1. **调用者分析**: 分析哪些方法调用了指定的方法
2. **被调用者分析**: 分析指定方法调用了哪些其他方法
3. **调用链路径**: 显示完整的方法调用路径
4. **层级控制**: 可以控制分析的深度层级

## 使用方法

### DocChain搜索工具

#### 工具参数

- `query` (必需): 搜索查询内容
- `topic_id` (可选): DocChain Topic ID，默认为"1977"
- `size` (可选): 返回结果数量，默认为5

#### 使用示例

```python
from src.tools.docchain_search import docchain_search_tool

# 在指定topic中搜索
result = docchain_search_tool.invoke({
    "query": "API接口文档",
    "topic_id": "1998",
    "size": 3
})
print(result)
```

### DocChain文件读取工具

#### 工具参数

- `doc_id` (必需): DocChain文档ID，必须从docchain_search_tool的搜索结果中获取

#### 使用示例

```python
from src.tools.docchain_search import file_read_by_docchain

# 读取完整文档内容
result = file_read_by_docchain.invoke({
    "doc_id": "131019"
})
print(result)
```

#### 工作流程

1. 首先使用`docchain_search_tool`搜索相关文档
2. 从搜索结果中获取感兴趣文档的`doc_id`
3. 使用`file_read_by_docchain`读取完整内容进行详细分析

### 方法调用链分析工具

#### 工具参数

- `package_path` (必需): Java包路径，例如 "com.ztesoft.zmq.controller"
- `class_name` (必需): Java类名，例如 "ExpertDiagnosisAction"
- `method_name` (必需): Java方法名，例如 "createDiagnosis"
- `level` (可选): 调用链分析层级深度，默认为10，范围1-20

#### 使用示例

```python
from src.tools.method_call_chain import java_method_call_chain

# 分析方法调用链
result = java_method_call_chain.invoke({
    "package_path": "com.ztesoft.zmq.controller",
    "class_name": "ExpertDiagnosisAction", 
    "method_name": "createDiagnosis",
    "level": 5
})
print(result)
```

### Agent使用

在研究和编码过程中，Agent会自动根据需要调用这些工具：

- **Researcher**: 
  - DocChain搜索：查找已上传的文档资料
  - DocChain文件读取：获取完整的文档或代码文件内容
  - 分析代码调用关系以理解业务逻辑
- **Coder**: 
  - DocChain搜索：查找代码相关的文档或技术资料
  - DocChain文件读取：获取完整的代码文件进行详细分析
  - 分析方法调用链以理解代码结构

## 工作流集成

这些工具已经自动集成到DeerFlow的工作流中：

1. **研究阶段**: Researcher可以使用DocChain搜索、文件读取和方法调用链分析来获取相关信息
2. **编码阶段**: Coder可以使用这些工具来查找文档、读取完整代码文件和分析代码
3. **报告生成**: 搜索、文件读取和分析结果会被包含在最终报告中

## 故障排除

### DocChain搜索常见问题

1. **配置问题**: 确保`conf.yaml`中的DOCCHAIN配置正确
2. **网络连接**: 确保能够访问DocChain服务
3. **API密钥**: 确保API密钥有效且具有搜索权限

### 方法调用链分析常见问题

1. **网络连接**: 确保能够访问AST解析服务 (http://10.10.176.213:9090)
2. **参数格式**: 确保包路径、类名、方法名格式正确
3. **服务可用性**: 确保AST解析服务正常运行

### 错误处理

工具会返回详细的错误信息：

- 配置不完整时会提示检查配置
- 网络错误时会显示具体的错误信息
- 参数验证失败时会提示正确的参数格式
- 服务调用失败时会返回失败原因

### 调试

启用调试日志可以查看更详细的信息：

```python
import logging
logging.getLogger("src.tools.docchain_search").setLevel(logging.DEBUG)
logging.getLogger("src.tools.method_call_chain").setLevel(logging.DEBUG)
```

## 测试

您可以使用提供的测试脚本来验证工具集成：

```bash
# 测试DocChain搜索
python test_docchain.py

# 测试方法调用链分析
python test_method_call_chain.py
```

## 技术实现

### DocChain搜索

#### 核心组件

1. **DocChainSearcher类**: 负责与DocChain API通信
2. **docchain_search_tool**: LangChain工具包装器，用于搜索文档
3. **file_read_by_docchain**: LangChain工具包装器，用于读取完整文档内容
4. **配置加载**: 从YAML文件加载配置

#### 依赖关系

- `httpx`: HTTP客户端（项目已包含）
- `langchain_core`: 工具框架
- `src.config.loader`: 配置加载器

### 方法调用链分析

#### 核心组件

1. **MethodCallChainAnalyzer类**: 负责与AST解析服务通信
2. **java_method_call_chain**: LangChain工具包装器
3. **结果格式化**: 将分析结果格式化为可读的Markdown

#### 依赖关系

- `httpx`: HTTP客户端（项目已包含）
- `langchain_core`: 工具框架
- `json`: JSON数据处理

### 集成点

- `src/tools/docchain_search.py`: DocChain搜索和文件读取工具实现
- `src/tools/method_call_chain.py`: 方法调用链分析工具实现
- `src/graph/nodes.py`: Agent节点集成
- `src/config/configuration.py`: 配置定义 