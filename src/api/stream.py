from typing import Optional, List, Dict
from pydantic import BaseModel

class ChatRequest(BaseModel):
    content: str
    thread_id: str
    interrupt_feedback: Optional[str] = None
    resources: Optional[List[str]] = None
    auto_accepted_plan: bool = False
    enable_background_investigation: bool = True
    max_plan_iterations: int = 1
    max_step_num: int = 3
    max_search_results: int = 3
    mcp_settings: Optional[Dict] = None
    project: Optional[str] = None
    topics: Optional[Dict[str, Dict[str, str]]] = None  # project -> {codeTopicId, docTopicId}