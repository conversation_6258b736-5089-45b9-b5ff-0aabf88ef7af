# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import base64
import locale
import logging
import os
import sys
from typing import Annotated, List, Optional, cast
from uuid import uuid4

from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response, StreamingResponse
from langchain_core.messages import AIMessageChunk, BaseMessage, ToolMessage
from langgraph.types import Command
from pydantic import BaseModel, Field

from src.config.tools import SELECTED_RAG_PROVIDER
from src.graph.builder import build_graph_with_memory
from src.podcast.graph.builder import build_graph as build_podcast_graph
from src.ppt.graph.builder import build_graph as build_ppt_graph
from src.prose.graph.builder import build_graph as build_prose_graph
from src.rag.builder import build_retriever
from src.rag.retriever import Resource
from src.server.chat_request import (ChatMessage, ChatRequest,
                                     GeneratePodcastRequest,
                                     GeneratePPTRequest, GenerateProseRequest,
                                     TTSRequest)
from src.server.mcp_request import (MCPServerMetadataRequest,
                                    MCPServerMetadataResponse)
from src.server.mcp_utils import load_mcp_tools
from src.server.rag_request import (RAGConfigResponse, RAGResourceRequest,
                                    RAGResourcesResponse)
from src.tools.tts import VolcengineTTS
from src.utils import apply_all_patches
from src.utils.docchain_client import DocChainClient
from src.utils.encoding_utils import (create_safe_sse_event,
                                      init_encoding_environment)
from src.utils.zcm_client import ZCMClient

# 初始化编码环境
init_encoding_environment()

# 设置编码环境
os.environ["PYTHONIOENCODING"] = "utf-8"
if sys.platform.startswith("win"):
    # Windows 系统特殊处理
    try:
        locale.setlocale(locale.LC_ALL, "en_US.UTF-8")
    except locale.Error:
        try:
            locale.setlocale(locale.LC_ALL, "C.UTF-8")
        except locale.Error:
            pass  # 如果都失败了，使用默认设置

# 应用LangChain补丁，修复token usage计算问题
apply_all_patches()

logger = logging.getLogger(__name__)

# 创建 FastAPI 应用，配置 JSON 编码
app = FastAPI(
    title="DeerFlow API",
    description="API for Deer",
    version="0.1.0",
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

docchain_client = DocChainClient()
zcm_client = ZCMClient()

graph = build_graph_with_memory()


@app.post("/api/chat/stream")
async def chat_stream(request: ChatRequest):
    thread_id = request.thread_id
    if thread_id == "__default__":
        thread_id = str(uuid4())
    return StreamingResponse(
        _astream_workflow_generator(
            request.model_dump()["messages"],
            thread_id,
            request.api_key,
            request.base_url,
            request.model,
            request.resources,
            request.max_plan_iterations,
            request.max_step_num,
            request.max_search_results,
            request.auto_accepted_plan,
            request.interrupt_feedback,
            request.mcp_settings,
            request.enable_background_investigation,
            request.project,
            request.topics,
            request.docchain_base_url,
            request.docchain_api_key,
            request.scenario_config,
            request.scenario_code,
            request.scenario_name,
        ),
        media_type="text/event-stream",
    )


async def _astream_workflow_generator(
    messages: List[ChatMessage],
    thread_id: str,
    api_key: str,
    base_url: str,
    model: str,
    resources: List[Resource],
    max_plan_iterations: int,
    max_step_num: int,
    max_search_results: int,
    auto_accepted_plan: bool,
    interrupt_feedback: str,
    mcp_settings: dict,
    enable_background_investigation,
    project: str,
    topics: dict,
    docchain_base_url: str,
    docchain_api_key: str,
    scenario_config: dict,
    scenario_code: str,
    scenario_name: str,
):
    # 项目和topics配置通过config传递给工具，无需设置全局状态
    try:
        input_ = {
            "messages": messages,
            "plan_iterations": 0,
            "final_report": "",
            "current_plan": None,
            "observations": [],
            "auto_accepted_plan": auto_accepted_plan,
            "enable_background_investigation": enable_background_investigation,
        }
        if not auto_accepted_plan and interrupt_feedback:
            resume_msg = f"[{interrupt_feedback}]"
            # add the last message to the resume message
            if messages:
                resume_msg += f" {messages[-1]['content']}"
            # 保留原有状态，只添加resume消息
            input_ = Command(
                resume=resume_msg,
                update={
                    "auto_accepted_plan": auto_accepted_plan,
                    "enable_background_investigation": enable_background_investigation,
                }
            )
        async for agent, _, event_data in graph.astream(
            input_,
            config={
                "thread_id": thread_id,
                "api_key": api_key,
                "base_url": base_url,
                "model": model,
                "resources": resources,
                "max_plan_iterations": max_plan_iterations,
                "max_step_num": max_step_num,
                "max_search_results": max_search_results,
                "mcp_settings": mcp_settings,
                "project": project,
                "topics": topics,
                "docchain_base_url": docchain_base_url,
                "docchain_api_key": docchain_api_key,
                "scenario_config": scenario_config,
                "scenario_code": scenario_code,
                "scenario_name": scenario_name,
            },
            stream_mode=["messages", "updates"],
            subgraphs=True,
        ):
            logger.debug(
                f"Stream event - agent: {agent}, event_data type: {type(event_data)}"
            )
            try:
                if isinstance(event_data, dict):
                    if "__interrupt__" in event_data:
                        yield create_safe_sse_event(
                            "interrupt",
                            {
                                "thread_id": thread_id,
                                "id": event_data["__interrupt__"][0].ns[0],
                                "role": "assistant",
                                "content": event_data["__interrupt__"][0].value,
                                "finish_reason": "interrupt",
                                "options": [
                                    {"text": "Edit plan", "value": "edit_plan"},
                                    {"text": "Start research", "value": "accepted"},
                                ],
                            },
                        )
                    continue
                message_chunk, message_metadata = cast(
                    tuple[BaseMessage, dict[str, any]], event_data
                )
                event_stream_message: dict[str, any] = {
                    "thread_id": thread_id,
                    "agent": agent[0].split(":")[0],
                    "id": message_chunk.id,
                    "role": "assistant",
                    "content": message_chunk.content,
                }
                if message_chunk.response_metadata.get("finish_reason"):
                    event_stream_message["finish_reason"] = (
                        message_chunk.response_metadata.get("finish_reason")
                    )
                if isinstance(message_chunk, ToolMessage):
                    # Tool Message - Return the result of the tool call
                    event_stream_message["tool_call_id"] = message_chunk.tool_call_id
                    yield create_safe_sse_event(
                        "tool_call_result", event_stream_message
                    )
                elif isinstance(message_chunk, AIMessageChunk):
                    # AI Message - Raw message tokens
                    if message_chunk.tool_calls:
                        # AI Message - Tool Call
                        event_stream_message["tool_calls"] = message_chunk.tool_calls
                        event_stream_message["tool_call_chunks"] = (
                            message_chunk.tool_call_chunks
                        )
                        yield create_safe_sse_event("tool_calls", event_stream_message)
                    elif message_chunk.tool_call_chunks:
                        # AI Message - Tool Call Chunks
                        event_stream_message["tool_call_chunks"] = (
                            message_chunk.tool_call_chunks
                        )
                        yield create_safe_sse_event(
                            "tool_call_chunks", event_stream_message
                        )
                    else:
                        # AI Message - Raw message tokens
                        yield create_safe_sse_event(
                            "message_chunk", event_stream_message
                        )
            except Exception as e:
                logger.error(f"Error processing event: {str(e)}")
                # 发送错误事件给前端，但继续处理其他事件
                yield create_safe_sse_event(
                    "error",
                    {
                        "id": "error_" + str(hash(str(e)))[-8:],
                        "thread_id": thread_id,
                        "agent": "coordinator",
                        "role": "assistant",
                        "error": str(e),
                        "error_type": "event_processing_error",
                    },
                )
    except Exception as e:
        logger.error(f"Error in workflow execution: {str(e)}")
        # 发送错误事件给前端
        error_message = str(e)
        yield create_safe_sse_event(
            "error",
            {
                "id": "error_" + str(hash(error_message))[-8:],
                "thread_id": thread_id,
                "agent": "coordinator",
                "role": "assistant",
                "error": error_message,
                "error_type": "workflow_error",
            },
        )


@app.post("/api/tts")
async def text_to_speech(request: TTSRequest):
    """Convert text to speech using volcengine TTS API."""
    try:
        app_id = os.getenv("VOLCENGINE_TTS_APPID", "")
        if not app_id:
            raise HTTPException(
                status_code=400, detail="VOLCENGINE_TTS_APPID is not set"
            )
        access_token = os.getenv("VOLCENGINE_TTS_ACCESS_TOKEN", "")
        if not access_token:
            raise HTTPException(
                status_code=400, detail="VOLCENGINE_TTS_ACCESS_TOKEN is not set"
            )
        cluster = os.getenv("VOLCENGINE_TTS_CLUSTER", "volcano_tts")
        voice_type = os.getenv("VOLCENGINE_TTS_VOICE_TYPE", "BV700_V2_streaming")

        tts_client = VolcengineTTS(
            appid=app_id,
            access_token=access_token,
            cluster=cluster,
            voice_type=voice_type,
        )
        # Call the TTS API
        result = tts_client.text_to_speech(
            text=request.text[:1024],
            encoding=request.encoding,
            speed_ratio=request.speed_ratio,
            volume_ratio=request.volume_ratio,
            pitch_ratio=request.pitch_ratio,
            text_type=request.text_type,
            with_frontend=request.with_frontend,
            frontend_type=request.frontend_type,
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=str(result["error"]))

        # Decode the base64 audio data
        audio_data = base64.b64decode(result["audio_data"])

        # Return the audio file
        return Response(
            content=audio_data,
            media_type=f"audio/{request.encoding}",
            headers={
                "Content-Disposition": (
                    f"attachment; filename=tts_output.{request.encoding}"
                )
            },
        )
    except Exception as e:
        logger.exception(f"Error in TTS endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/podcast/generate")
async def generate_podcast(request: GeneratePodcastRequest):
    try:
        report_content = request.content
        print(report_content)
        workflow = build_podcast_graph()
        final_state = workflow.invoke({"input": report_content})
        audio_bytes = final_state["output"]
        return Response(content=audio_bytes, media_type="audio/mp3")
    except Exception as e:
        logger.exception(f"Error occurred during podcast generation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/ppt/generate")
async def generate_ppt(request: GeneratePPTRequest):
    try:
        report_content = request.content
        print(report_content)
        workflow = build_ppt_graph()
        final_state = workflow.invoke({"input": report_content})
        generated_file_path = final_state["generated_file_path"]
        with open(generated_file_path, "rb") as f:
            ppt_bytes = f.read()
        return Response(
            content=ppt_bytes,
            media_type=(
                "application/vnd.openxmlformats-officedocument"
                ".presentationml.presentation"
            ),
        )
    except Exception as e:
        logger.exception(f"Error occurred during ppt generation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/prose/generate")
async def generate_prose(request: GenerateProseRequest):
    try:
        logger.info(f"Generating prose for prompt: {request.prompt}")
        workflow = build_prose_graph()
        events = workflow.astream(
            {
                "content": request.prompt,
                "option": request.option,
                "command": request.command,
            },
            stream_mode="messages",
            subgraphs=True,
        )
        return StreamingResponse(
            (f"data: {event[0].content}\n\n" async for _, event in events),
            media_type="text/event-stream",
        )
    except Exception as e:
        logger.exception(f"Error occurred during prose generation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/mcp/server/metadata", response_model=MCPServerMetadataResponse)
async def mcp_server_metadata(request: MCPServerMetadataRequest):
    """Get information about an MCP server."""
    try:
        # Set default timeout with a longer value for this endpoint
        timeout = 300  # Default to 300 seconds for this endpoint

        # Use custom timeout from request if provided
        if request.timeout_seconds is not None:
            timeout = request.timeout_seconds

        # Load tools from the MCP server using the utility function
        tools = await load_mcp_tools(
            server_type=request.transport,
            command=request.command,
            args=request.args,
            url=request.url,
            env=request.env,
            timeout_seconds=timeout,
        )

        # Create the response with tools
        response = MCPServerMetadataResponse(
            transport=request.transport,
            command=request.command,
            args=request.args,
            url=request.url,
            env=request.env,
            tools=tools,
        )

        return response
    except Exception as e:
        if not isinstance(e, HTTPException):
            logger.exception(f"Error in MCP server metadata endpoint: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
        raise


@app.get("/api/rag/config", response_model=RAGConfigResponse)
async def rag_config():
    """Get the config of the RAG."""
    return RAGConfigResponse(provider=SELECTED_RAG_PROVIDER)


@app.get("/api/rag/resources", response_model=RAGResourcesResponse)
async def rag_resources(request: Annotated[RAGResourceRequest, Query()]):
    """Get the resources of the RAG."""
    retriever = build_retriever()
    if retriever:
        return RAGResourcesResponse(resources=retriever.list_resources(request.query))
    return RAGResourcesResponse(resources=[])


@app.get("/api/projects")
async def get_projects():
    """Get all available project configurations."""
    try:
        from src.config.projects import get_project_info, list_projects

        projects = list_projects()
        return {"projects": projects, "default": get_project_info("default")}
    except Exception as e:
        logger.exception(f"Error getting projects: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/projects/{project_id}")
async def get_project_info_api(project_id: str):
    """Get information about a specific project."""
    try:
        from src.config.projects import get_project_info

        return get_project_info(project_id)
    except Exception as e:
        logger.exception(f"Error getting project info: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


class TestConnectionRequest(BaseModel):
    """Test connection request model."""

    api_key: str = Field(..., description="API key for the LLM")
    base_url: str = Field(..., description="Base URL for the LLM")
    model: str = Field(..., description="LLM model to use")


class TestConnectionResponse(BaseModel):
    """Test connection response model."""

    success: bool = Field(..., description="Whether the connection test was successful")
    message: str = Field(..., description="Response message")
    error: Optional[str] = Field(None, description="Error message if the test failed")


@app.post("/api/test-connection", response_model=TestConnectionResponse)
async def test_connection(request: TestConnectionRequest):
    """Test connection to LLM service with provided credentials."""
    try:
        from src.llms.llm_manager import _llm_manager, create_llm_config

        # 创建测试配置
        test_config = create_llm_config(
            api_key=request.api_key, base_url=request.base_url, model=request.model
        )

        # 验证配置
        test_config.validate()

        # 创建LLM实例进行测试
        llm = _llm_manager.get_llm("basic", test_config)

        # 发送测试消息
        await llm.ainvoke("Hello")
        logger.info(f"Test connection successful for model {request.model}")
        return TestConnectionResponse(
            success=True, message="连接测试成功！模型响应正常。", error=None
        )

    except ValueError as e:
        # 用户配置错误
        logger.warning(f"Test connection validation failed: {str(e)}")
        return TestConnectionResponse(
            success=False, message="连接测试失败", error=str(e)
        )
    except Exception as e:
        # 其他错误
        logger.exception(f"Test connection error: {str(e)}")
        error_msg = str(e).lower()

        if (
            "api_key" in error_msg
            or "authentication" in error_msg
            or "unauthorized" in error_msg
        ):
            error_message = "API Key验证失败，请检查您输入的API Key是否正确。"
        elif (
            "connection" in error_msg
            or "timeout" in error_msg
            or "network" in error_msg
        ):
            error_message = (
                "无法连接到API服务，请检查您输入的Base URL是否正确，或检查网络连接。"
            )
        elif "model" in error_msg and (
            "not found" in error_msg or "not available" in error_msg
        ):
            error_message = "指定的模型不可用，请检查模型名称是否正确。"
        elif "quota" in error_msg or "limit" in error_msg:
            error_message = "API配额已用完或达到限制，请检查您的账户余额。"
        else:
            error_message = f"连接测试失败：{str(e)}"

        return TestConnectionResponse(
            success=False, message="连接测试失败", error=error_message
        )


@app.get("/api/resolve_docchain_url")
async def resolve_docchain_url_endpoint(
    doc_id: str,
    docchain_base_url: Optional[str] = Query(None),
    docchain_api_key: Optional[str] = Query(None),
):
    """
    Resolves a DocChain identifier (doc_id) to its full accessible URL.
    """
    logger.info(f"Received request to resolve DocID: {doc_id}")
    if not doc_id:
        raise HTTPException(status_code=400, detail="doc_id parameter is required.")

    try:
        source_id = docchain_client.get_source_id(
            doc_id, docchain_base_url, docchain_api_key
        )

        effective_fallback_base_url = (
            docchain_base_url
            if docchain_base_url
            else docchain_client.DOCCHAIN_BASE_URL
        )

        if not source_id:
            logger.warning(
                f"Could not retrieve source_id for DocID: {doc_id}. "
                "Returning fallback URL."
            )
            # Fallback URL as per requirements, using the determined base URL
            fallback_url = (
                f"{effective_fallback_base_url}/v1/doc/read?"
                f"doc_id={doc_id}&read_format=md"
            )
            return {"resolved_url": fallback_url}

        bid, doc_fullpath = zcm_client.get_doc_fullpath(source_id)
        if not bid or not doc_fullpath:
            logger.info(
                f"Could not retrieve bid or doc_fullpath for Source ID: "
                f"{source_id} (DocID: {doc_id}). Returning fallback URL."
            )
            # Fallback URL as per requirements, using the determined base URL
            fallback_url = (
                f"{effective_fallback_base_url}/v1/doc/read?"
                f"doc_id={doc_id}&read_format=md"
            )
            return {"resolved_url": fallback_url}

        final_url = f"https://docs.iwhalecloud.com/doi/{bid}{doc_fullpath}"
        logger.info(f"Successfully resolved DocID {doc_id} to URL: {final_url}")
        return {"resolved_url": final_url}

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(
            f"An unexpected error occurred while resolving DocID {doc_id}: {e}"
        )
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
