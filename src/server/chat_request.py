# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from typing import List, Optional, Union, Dict, Any

from pydantic import BaseModel, Field

from src.rag.retriever import Resource


class ContentItem(BaseModel):
    type: str = Field(..., description="The type of content (text, image, etc.)")
    text: Optional[str] = Field(None, description="The text content if type is 'text'")
    image_url: Optional[str] = Field(
        None, description="The image URL if type is 'image'"
    )


class ChatMessage(BaseModel):
    role: str = Field(
        ..., description="The role of the message sender (user or assistant)"
    )
    content: Union[str, List[ContentItem]] = Field(
        ...,
        description="The content of the message, either a string or a list of content items",
    )


# 场景配置相关模型 - 提示词配置结构
class PlannerPrompts(BaseModel):
    """Planner节点提示词配置"""
    system_prompt: str = Field("", description="系统提示词", alias="systemPrompt")
    user_prompt_template: str = Field("", description="用户提示词模板", alias="userPromptTemplate")
    custom_prompts: Dict[str, str] = Field(default_factory=dict, description="自定义提示词", alias="customPrompts")
    
    class Config:
        allow_population_by_field_name = True


class ResearcherPrompts(BaseModel):
    """Researcher节点提示词配置"""
    system_prompt: str = Field("", description="系统提示词", alias="systemPrompt")
    search_query_template: str = Field("", description="搜索查询模板", alias="searchQueryTemplate")
    analysis_prompt_template: str = Field("", description="分析提示词模板", alias="analysisPromptTemplate")
    custom_prompts: Dict[str, str] = Field(default_factory=dict, description="自定义提示词", alias="customPrompts")
    
    class Config:
        allow_population_by_field_name = True


class ReporterPrompts(BaseModel):
    """Reporter节点提示词配置"""
    system_prompt: str = Field("", description="系统提示词", alias="systemPrompt")
    report_structure_template: str = Field("", description="报告结构模板", alias="reportStructureTemplate")
    citation_format_template: str = Field("", description="引用格式模板", alias="citationFormatTemplate")
    custom_prompts: Dict[str, str] = Field(default_factory=dict, description="自定义提示词", alias="customPrompts")
    
    class Config:
        allow_population_by_field_name = True


class CoderPrompts(BaseModel):
    """Coder节点提示词配置"""
    system_prompt: str = Field("", description="系统提示词", alias="systemPrompt")
    code_analysis_template: str = Field("", description="代码分析模板", alias="codeAnalysisTemplate")
    implementation_template: str = Field("", description="实现模板", alias="implementationTemplate")
    testing_template: str = Field("", description="测试模板", alias="testingTemplate")
    custom_prompts: Dict[str, str] = Field(default_factory=dict, description="自定义提示词", alias="customPrompts")
    
    class Config:
        allow_population_by_field_name = True


# 工具配置结构
class ToolsConfig(BaseModel):
    """工具配置"""
    enabled: List[str] = Field(default_factory=list, description="启用的工具列表")
    configs: Dict[str, Any] = Field(default_factory=dict, description="工具配置")


# 输出配置结构
class NodeOutputConfig(BaseModel):
    """节点输出配置"""
    format: str = Field("markdown", description="输出格式")
    debug_output: bool = Field(False, description="是否输出调试信息")


class PlannerOutputConfig(NodeOutputConfig):
    """Planner输出配置"""
    include_reasoning: bool = Field(True, description="是否包含推理过程")
    include_steps: bool = Field(True, description="是否包含步骤")


class ResearcherOutputConfig(NodeOutputConfig):
    """Researcher输出配置"""
    include_source_links: bool = Field(True, description="是否包含源链接")
    include_metadata: bool = Field(True, description="是否包含元数据")


class ReporterOutputConfig(NodeOutputConfig):
    """Reporter输出配置"""
    include_table_of_contents: bool = Field(False, description="是否包含目录")
    include_metadata: bool = Field(True, description="是否包含元数据")
    include_image_attachments: bool = Field(True, description="是否包含图片附件")


class CoderOutputConfig(NodeOutputConfig):
    """Coder输出配置"""
    include_code_comments: bool = Field(True, description="是否包含代码注释")
    include_test_cases: bool = Field(False, description="是否包含测试用例")
    include_documentation: bool = Field(True, description="是否包含文档")


# 节点配置基础模型
class ScenarioNodeConfig(BaseModel):
    """节点配置基础模型"""
    tools: ToolsConfig = Field(default_factory=ToolsConfig, description="工具配置")
    scenario_params: Dict[str, Any] = Field(default_factory=dict, description="场景特定参数", alias="scenarioParams")
    
    class Config:
        allow_population_by_field_name = True


class PlannerNodeConfig(ScenarioNodeConfig):
    """Planner节点配置"""
    max_iterations: int = Field(3, description="最大计划迭代次数", alias="maxIterations")
    prompts: PlannerPrompts = Field(default_factory=PlannerPrompts, description="提示词配置")


class ResearcherNodeConfig(ScenarioNodeConfig):
    """Researcher节点配置"""
    max_search_results: int = Field(5, description="最大搜索结果数", alias="maxSearchResults")
    search_depth: int = Field(3, description="搜索深度", alias="searchDepth")
    prompts: ResearcherPrompts = Field(default_factory=ResearcherPrompts, description="提示词配置")
    output: ResearcherOutputConfig = Field(default_factory=ResearcherOutputConfig, description="输出配置")
    
    # 搜索策略
    search_strategy: Dict[str, Any] = Field(default_factory=dict, description="搜索策略配置", alias="searchStrategy")


class ReporterNodeConfig(ScenarioNodeConfig):
    """Reporter节点配置"""
    prompts: ReporterPrompts = Field(default_factory=ReporterPrompts, description="提示词配置")
    output: ReporterOutputConfig = Field(default_factory=ReporterOutputConfig, description="输出配置")
    
    # 报告生成策略
    report_strategy: Dict[str, Any] = Field(default_factory=dict, description="报告生成策略", alias="reportStrategy")


class CoderNodeConfig(ScenarioNodeConfig):
    """Coder节点配置"""
    prompts: CoderPrompts = Field(default_factory=CoderPrompts, description="提示词配置")
    output: CoderOutputConfig = Field(default_factory=CoderOutputConfig, description="输出配置")
    
    # 编程语言配置
    languages: Dict[str, Any] = Field(default_factory=dict, description="编程语言配置")
    
    # 代码分析策略
    analysis_strategy: Dict[str, Any] = Field(default_factory=dict, description="代码分析策略", alias="analysisStrategy")


class ScenarioWorkflowConfig(BaseModel):
    """场景流程控制配置"""
    max_iterations: int = Field(3, description="最大迭代次数", alias="maxIterations")
    enable_parallel_execution: bool = Field(False, description="是否启用并行执行", alias="enableParallelExecution")
    failure_strategy: str = Field("retry", description="失败策略 (abort/continue/retry)", alias="failureStrategy")
    retry_count: int = Field(2, description="重试次数", alias="retryCount")
    
    class Config:
        allow_population_by_field_name = True


class ScenarioGlobalConfig(BaseModel):
    """场景全局配置"""
    project: str = Field("", description="项目名称")
    code_topic_id: str = Field("", description="代码主题ID", alias="codeTopicId")
    doc_topic_id: str = Field("", description="文档主题ID", alias="docTopicId")
    locale: str = Field("zh-CN", description="语言地区")
    debug_mode: bool = Field(False, description="调试模式", alias="debugMode")
    timeout_minutes: int = Field(30, description="全局超时时间（分钟）", alias="timeoutMinutes")
    
    class Config:
        allow_population_by_field_name = True


class ScenarioMetadata(BaseModel):
    """场景元数据"""
    category: str = Field("", description="场景分类")
    tags: List[str] = Field(default_factory=list, description="场景标签")
    difficulty: str = Field("medium", description="难度级别")
    estimated_time: int = Field(30, description="预估执行时间（分钟）", alias="estimatedTime")
    version: str = Field("1.0.0", description="版本号")
    author: Optional[str] = Field(None, description="作者")
    
    class Config:
        allow_population_by_field_name = True


class ScenarioNodesConfig(BaseModel):
    """场景节点配置集合"""
    planner: PlannerNodeConfig = Field(default_factory=PlannerNodeConfig, description="Planner节点配置")
    researcher: ResearcherNodeConfig = Field(default_factory=ResearcherNodeConfig, description="Researcher节点配置")
    reporter: ReporterNodeConfig = Field(default_factory=ReporterNodeConfig, description="Reporter节点配置")
    coder: CoderNodeConfig = Field(default_factory=CoderNodeConfig, description="Coder节点配置")


class ScenarioConfig(BaseModel):
    """完整的场景配置"""
    id: str = Field(..., description="场景ID")
    name: str = Field(..., description="场景名称")
    description: str = Field("", description="场景描述")
    
    # 场景元数据
    metadata: ScenarioMetadata = Field(default_factory=ScenarioMetadata, description="场景元数据")
    
    # 场景级全局配置 - 使用"global"作为前端字段名
    global_config: ScenarioGlobalConfig = Field(default_factory=ScenarioGlobalConfig, description="全局配置", alias="global")
    
    # 节点配置
    nodes: ScenarioNodesConfig = Field(default_factory=ScenarioNodesConfig, description="节点配置")
    
    # 场景特定的流程控制
    workflow: ScenarioWorkflowConfig = Field(default_factory=ScenarioWorkflowConfig, description="流程控制配置")
    
    # 场景变量和上下文
    variables: Dict[str, Any] = Field(default_factory=dict, description="场景变量")
    
    # 是否为默认场景
    is_default: bool = Field(False, description="是否为默认场景", alias="isDefault")
    
    # 是否为预置场景
    is_preset: bool = Field(False, description="是否为预置场景", alias="isPreset")
    
    class Config:
        allow_population_by_field_name = True


class ChatRequest(BaseModel):
    messages: Optional[List[ChatMessage]] = Field(
        [], description="History of messages between the user and the assistant"
    )
    api_key: Optional[str] = Field(None, description="API key for the LLM")
    base_url: Optional[str] = Field(
        "https://lab.iwhalecloud.com/gpt-proxy/v1", description="Base URL for the LLM"
    )
    model: Optional[str] = Field("gpt-4.1", description="LLM model to use")
    resources: Optional[List[Resource]] = Field(
        [], description="Resources to be used for the research"
    )
    debug: Optional[bool] = Field(False, description="Whether to enable debug logging")
    thread_id: Optional[str] = Field(
        "__default__", description="A specific conversation identifier"
    )
    max_plan_iterations: Optional[int] = Field(
        1, description="The maximum number of plan iterations"
    )
    max_step_num: Optional[int] = Field(
        3, description="The maximum number of steps in a plan"
    )
    max_search_results: Optional[int] = Field(
        3, description="The maximum number of search results"
    )
    auto_accepted_plan: Optional[bool] = Field(
        False, description="Whether to automatically accept the plan"
    )
    interrupt_feedback: Optional[str] = Field(
        None, description="Interrupt feedback from the user on the plan"
    )
    mcp_settings: Optional[dict] = Field(
        None, description="MCP settings for the chat request"
    )
    enable_background_investigation: Optional[bool] = Field(
        True, description="Whether to get background investigation before plan"
    )
    project: Optional[str] = Field(
        None, description="Project identifier for multi-project support"
    )
    topics: Optional[dict] = Field(
        None, description="Project-specific topic configurations {project: {codeTopicId, docTopicId}}"
    )
    docchain_base_url: Optional[str] = Field(
        None, description="DocChain service base URL"
    )
    docchain_api_key: Optional[str] = Field(
        None, description="DocChain service API key"
    )
    
    # 新增场景配置字段
    scenario_config: Optional[ScenarioConfig] = Field(
        None, description="用户选择的场景配置，包含节点和提示词配置"
    )
    scenario_code: Optional[str] = Field(
        None, description="场景代码，用于使用预置场景（如 'pd', 'sa'）"
    )
    scenario_name: Optional[str] = Field(
        None, description="场景名称，用于兼容性（如 '产品设计', '系统架构'）"
    )


class TTSRequest(BaseModel):
    text: str = Field(..., description="The text to convert to speech")
    voice_type: Optional[str] = Field(
        "BV700_V2_streaming", description="The voice type to use"
    )
    encoding: Optional[str] = Field("mp3", description="The audio encoding format")
    speed_ratio: Optional[float] = Field(1.0, description="Speech speed ratio")
    volume_ratio: Optional[float] = Field(1.0, description="Speech volume ratio")
    pitch_ratio: Optional[float] = Field(1.0, description="Speech pitch ratio")
    text_type: Optional[str] = Field("plain", description="Text type (plain or ssml)")
    with_frontend: Optional[int] = Field(
        1, description="Whether to use frontend processing"
    )
    frontend_type: Optional[str] = Field("unitTson", description="Frontend type")


class GeneratePodcastRequest(BaseModel):
    content: str = Field(..., description="The content of the podcast")


class GeneratePPTRequest(BaseModel):
    content: str = Field(..., description="The content of the ppt")


class GenerateProseRequest(BaseModel):
    prompt: str = Field(..., description="The content of the prose")
    option: str = Field(..., description="The option of the prose writer")
    command: Optional[str] = Field(
        "", description="The user custom command of the prose writer"
    )
