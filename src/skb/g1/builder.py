"""
 构建 software architect agent的图结构
"""
from langgraph.checkpoint.memory import MemorySaver
from langgraph.constants import START, END
from langgraph.graph import StateGraph

from src.skb.g1.nodes import coordinator_node
from src.skb.g1.types import SAState


def _build_base_graph():
    """"创建图结构"""
    builder = StateGraph(SAState)
    builder.add_edge(START, "coordinator")
    builder.add_node("coordinator", coordinator_node)
    builder.add_edge("coordinator", END)
    return builder

def build_graph_with_memory():
    """Build and return the agent workflow g1 with memory."""
    # use persistent memory to save conversation history
    # TODO: be compatible with SQLite / PostgreSQL
    memory = MemorySaver()

    # build state g1
    builder = _build_base_graph()
    return builder.compile(checkpointer=memory)


def build_graph():
    """Build and return the agent workflow g1 without memory."""
    # build state g1
    builder = _build_base_graph()
    return builder.compile()


graph = build_graph()