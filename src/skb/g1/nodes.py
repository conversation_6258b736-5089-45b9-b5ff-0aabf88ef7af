import json
import locale
import logging
from typing import Literal

from langchain_core.messages import AIMessage
from langchain_core.runnables import RunnableConfig
from langgraph.types import Command

from src.config.agents import AGENT_LLM_MAP
from src.config.configuration import Configuration
from src.graph.nodes import handoff_to_planner
from src.llms.llm import get_llm_by_type
from src.prompts import apply_prompt_template
from src.prompts.planner_model import Plan
from src.skb.g1.types import SAState
from src.utils.json_utils import repair_json_output

logger = logging.getLogger(__name__)

def  coordinator_node(state: SAState, config: RunnableConfig) :
    """Coordinator node that communicate with customers."""
    configurable = Configuration.from_runnable_config(config)
    messages = apply_prompt_template("skb/sa_coordinator", state, configurable)
    response = (
        get_llm_by_type(AGENT_LLM_MAP["sa_coordinator"])
        # .bind_tools([handoff_to_planner])
        .invoke(messages)
    )
    # goto = "__end__"
    return {"final_report": response.content}
    """
    return Command(
            update={"locale": locale, "resources": configurable.resources},
            goto=goto,
    )"""

def planner_node(
    state: SAState, config: RunnableConfig
) -> Command[Literal["human_feedback", "reporter"]]:
    """Planner node that generate the full plan."""
    logger.info("Planner generating full plan")
    configurable = Configuration.from_runnable_config(config)
    plan_iterations = state["plan_iterations"] if state.get("plan_iterations", 0) else 0
    messages = apply_prompt_template("planner", state, configurable)

    if (
        plan_iterations == 0
        and state.get("enable_background_investigation")
        and state.get("background_investigation_results")
    ):
        messages += [
            {
                "role": "user",
                "content": (
                    "background investigation results of user query:\n"
                    + state["background_investigation_results"]
                    + "\n"
                ),
            }
        ]

    if AGENT_LLM_MAP["planner"] == "basic":
        llm = get_llm_by_type(AGENT_LLM_MAP["planner"]).with_structured_output(
            Plan,
            method="json_mode",
        )
    else:
        llm = get_llm_by_type(AGENT_LLM_MAP["planner"])

    # if the plan iterations is greater than the max plan iterations, return the reporter node
    if plan_iterations >= configurable.max_plan_iterations:
        return Command(goto="reporter")

    full_response = ""
    if AGENT_LLM_MAP["planner"] == "basic":
        response = llm.invoke(messages)
        full_response = response.model_dump_json(indent=4, exclude_none=True)
    else:
        response = llm.stream(messages)
        for chunk in response:
            full_response += chunk.content
    logger.debug(f"Current state messages: {state['messages']}")
    logger.info(f"Planner response: {full_response}")

    try:
        curr_plan = json.loads(repair_json_output(full_response))
    except json.JSONDecodeError:
        logger.warning("Planner response is not a valid JSON")
        if plan_iterations > 0:
            return Command(goto="reporter")
        else:
            return Command(goto="__end__")
    if curr_plan.get("has_enough_context"):
        logger.info("Planner response has enough context.")
        new_plan = Plan.model_validate(curr_plan)
        return Command(
            update={
                "messages": [AIMessage(content=full_response, name="planner")],
                "current_plan": new_plan,
            },
            goto="reporter",
        )
    return Command(
        update={
            "messages": [AIMessage(content=full_response, name="planner")],
            "current_plan": full_response,
        },
        goto="human_feedback",
    )


def sa_agent_node(state: SAState, config: RunnableConfig):
    """架构师"""
    pass

def pd_agent_node(state: SAState, config: RunnableConfig):
    """产品经理"""
    pass

def developer_agent_node(state: SAState, config: RunnableConfig):
    pass

def tester_agent_node(state: SAState, config: RunnableConfig):
    pass

def ops_agent_node(state: SAState, config: RunnableConfig):
    pass