# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import httpx
from typing import Annotated, Dict, Optional, Any
from langchain_core.tools import tool
from .decorators import log_io

logger = logging.getLogger(__name__)


class MethodCallChainAnalyzer:
    """代码方法调用链分析器"""

    def __init__(self, base_url: str = "http://*************:9090"):
        """
        初始化方法调用链分析器

        Args:
            base_url: AST解析服务基础URL
        """
        self.base_url = base_url.rstrip("/")

    def _make_request(self, endpoint: str, data: Dict) -> Dict:
        """
        发送HTTP请求到AST解析服务

        Args:
            endpoint: API端点
            data: 请求数据

        Returns:
            Dict: 响应数据

        Raises:
            Exception: 请求失败时抛出异常
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        headers = {"Content-Type": "application/json"}

        try:
            with httpx.Client(timeout=60) as client:  # 增加超时时间，因为代码分析可能比较耗时
                response = client.post(
                    url=url,
                    headers=headers,
                    json=data,
                )

            logger.debug(f"方法调用链分析请求URL: {url}")
            logger.debug(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
            logger.debug(f"响应状态码: {response.status_code}")
            
            response.raise_for_status()

            if response.text:
                try:
                    return response.json()
                except json.JSONDecodeError:
                    return {"text": response.text}
            return {}

        except httpx.RequestError as e:
            logger.error(f"方法调用链分析请求失败: {e}")
            raise Exception(f"方法调用链分析请求失败: {str(e)}")
        except httpx.HTTPStatusError as e:
            logger.error(f"方法调用链分析HTTP错误: {e}")
            logger.error(f"响应内容: {e.response.text}")
            raise Exception(f"方法调用链分析HTTP错误: {str(e)}")

    def get_method_call_chain(self, package_path: str, class_name: str, 
                             method_name: str, level: int = 4) -> Optional[Dict]:
        """
        获取方法调用链

        Args:
            package_path: 包路径，例如 "com.ztesoft.zmq.controller"
            class_name: 类名，例如 "ExpertDiagnosisAction"
            method_name: 方法名，例如 "createDiagnosis"
            level: 调用链层级深度，默认为4

        Returns:
            Optional[Dict]: 调用链分析结果，如果失败则返回None
        """
        try:
            request_data = {
                "packagePath": package_path,
                "className": class_name,
                "methodName": method_name,
                "level": str(level)
            }
            
            response = self._make_request("/ast/parser/common/method/call", request_data)
            
            # 检查响应格式，提取data字段
            if isinstance(response, dict):
                if response.get("code") == "00000" and "data" in response:
                    return response["data"]
                elif response.get("success") is True and "data" in response:
                    return response["data"]
                else:
                    logger.warning(f"API返回非成功状态: {response}")
                    return response
            
            return response
            
        except Exception as e:
            logger.error(f"获取方法调用链失败: {e}")
            return None


@tool
@log_io
def java_method_call_chain(
    package_path: Annotated[str, "Java包路径，例如: com.ztesoft.zmq.controller"],
    class_name: Annotated[str, "Java类名，例如: ExpertDiagnosisAction"],
    method_name: Annotated[str, "Java方法名，例如: createDiagnosis"],
    level: Annotated[int, "调用链分析层级深度，默认为4"] = 4,
) -> str:
    """
    分析Java代码中指定方法的调用链。
    这个工具可以分析指定方法被哪些其他方法调用，以及它调用了哪些其他方法，
    帮助理解代码的调用关系和依赖结构。
    
    参数说明：
    - package_path: Java包的完整路径
    - class_name: 要分析的类名
    - method_name: 要分析的方法名  
    - level: 分析的层级深度，数值越大分析越深入
    """
    try:
        if not package_path or not class_name or not method_name:
            return "错误：package_path、class_name和method_name都是必需参数"
        
        if level < 1 or level > 20:
            return "错误：level参数必须在1到20之间"
            
        analyzer = MethodCallChainAnalyzer()
        
        # 执行方法调用链分析
        result = analyzer.get_method_call_chain(package_path, class_name, method_name, level)
        if not result:
            return f"分析方法调用链失败：{package_path}.{class_name}.{method_name}"
        
        # 格式化分析结果
        formatted_result = []
        formatted_result.append(f"# 方法调用链分析结果")
        formatted_result.append(f"")
        formatted_result.append(f"**目标方法**: {package_path}.{class_name}.{method_name}")
        formatted_result.append(f"**分析层级**: {level}")
        formatted_result.append(f"")
        
        # 检查响应结构并格式化输出
        if isinstance(result, dict):
            # 显示方法内容（如果有）
            if "methodContent" in result:
                method_content = result.get("methodContent", "")
                if method_content:
                    formatted_result.append("## 方法内容")
                    formatted_result.append("```java")
                    formatted_result.append(method_content)
                    formatted_result.append("```")
                    formatted_result.append("")
            
            # 显示方法注解信息
            if "annotations" in result:
                annotations = result.get("annotations", "")
                if annotations:
                    formatted_result.append("## 方法注解")
                    formatted_result.append(f"```java")
                    formatted_result.append(annotations)
                    formatted_result.append("```")
                    formatted_result.append("")
            
            # 显示类注解信息
            if "classAnnotations" in result:
                class_annotations = result.get("classAnnotations", "")
                if class_annotations:
                    formatted_result.append("## 类注解")
                    formatted_result.append(f"```java")
                    formatted_result.append(class_annotations)
                    formatted_result.append("```")
                    formatted_result.append("")
            
            # 处理方法调用列表（invokeList）
            if "invokeList" in result:
                invoke_list = result.get("invokeList", [])
                if invoke_list:
                    formatted_result.append("## 此方法调用的其他方法")
                    for i, invoke_item in enumerate(invoke_list[:20]):  # 限制显示数量
                        if isinstance(invoke_item, dict):
                            method_name = invoke_item.get("methodName", "")
                            class_name = invoke_item.get("className", "")
                            package_path = invoke_item.get("packagePath", "")
                            method_content = invoke_item.get("methodContent", "")
                            
                            formatted_result.append(f"### 调用 {i+1}: {package_path}.{class_name}.{method_name}")
                            
                            # 显示方法内容的前几行作为预览
                            if method_content:
                                lines = method_content.split('\n')
                                preview_lines = lines[:5]  # 显示前5行
                                formatted_result.append("**方法预览**:")
                                formatted_result.append("```java")
                                formatted_result.append('\n'.join(preview_lines))
                                if len(lines) > 5:
                                    formatted_result.append("... (更多内容)")
                                formatted_result.append("```")
                            formatted_result.append("")
                        else:
                            formatted_result.append(f"{i+1}. {str(invoke_item)}")
                    formatted_result.append("")
            
            # 处理输入参数信息
            if "inputParamList" in result:
                input_params = result.get("inputParamList", [])
                if input_params:
                    formatted_result.append("## 输入参数")
                    for param in input_params:
                        if isinstance(param, dict):
                            param_name = param.get("name", "")
                            param_type = param.get("declaration", "")
                            formatted_result.append(f"- **{param_name}**: {param_type}")
                    formatted_result.append("")
            
            # 处理输出参数信息
            if "outputParam" in result:
                output_param = result.get("outputParam", {})
                if output_param:
                    param_type = output_param.get("declaration", "")
                    formatted_result.append("## 返回类型")
                    formatted_result.append(f"- **返回类型**: {param_type}")
                    formatted_result.append("")
            
            # 如果有引用的类详情信息
            if "refClassDetailList" in result:
                ref_classes = result.get("refClassDetailList", [])
                if ref_classes:
                    formatted_result.append("## 相关类信息")
                    for ref_class in ref_classes[:5]:  # 限制显示数量
                        if isinstance(ref_class, dict):
                            class_name = ref_class.get("className", "")
                            package_path = ref_class.get("packagePath", "")
                            formatted_result.append(f"- {package_path}.{class_name}")
                    formatted_result.append("")
            
            # 如果有错误信息
            if "error" in result:
                formatted_result.append("## 错误信息")
                formatted_result.append(f"错误: {result.get('error', '未知错误')}")
                formatted_result.append("")
            
            # 如果没有识别的关键字段，显示原始数据
            if not any(key in result for key in ["methodContent", "invokeList", "inputParamList", "outputParam", "error"]):
                formatted_result.append("## 原始分析结果")
                formatted_result.append("```json")
                formatted_result.append(json.dumps(result, indent=2, ensure_ascii=False))
                formatted_result.append("```")
        else:
            # 如果结果不是字典，直接显示
            formatted_result.append("## 分析结果")
            formatted_result.append(str(result))
        
        return "\n".join(formatted_result)
        
    except Exception as e:
        error_msg = f"方法调用链分析工具执行失败: {str(e)}"
        logger.error(error_msg)
        return error_msg