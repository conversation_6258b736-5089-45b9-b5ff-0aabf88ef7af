# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import httpx
from typing import Annotated, List, Dict, Optional, Any
from langchain_core.tools import tool
from langchain_core.runnables import RunnableConfig
from .decorators import log_io
from src.config.loader import load_yaml_config
from src.config.projects import get_project_topics
from src.config.configuration import Configuration

logger = logging.getLogger(__name__)

class DocChainSearcher:
    """DocChain搜索客户端"""

    def __init__(self, base_url: str, api_key: str):
        """
        初始化DocChain搜索客户端

        Args:
            base_url: DocChain服务基础URL
            api_key: DocChain服务API密钥
        """
        self.base_url = base_url.rstrip("/")
        self.api_key = api_key

    def _make_request(self, method: str, endpoint: str, data: Any = None,
                     params: Dict = None) -> Dict:
        """
        发送HTTP请求到DocChain服务

        Args:
            method: HTTP方法 (GET, POST, PUT, DELETE)
            endpoint: API端点
            data: 请求数据
            params: URL参数

        Returns:
            Dict: 响应数据

        Raises:
            Exception: 请求失败时抛出异常
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        headers = {"X-Api-Key": self.api_key} if self.api_key else {}

        try:
            if isinstance(data, dict) or isinstance(data, list):
                headers["Content-Type"] = "application/json"
                json_data = data
            else:
                json_data = None

            # 对于文件读取请求，使用更长的超时时间
            timeout = 60 if "doc/read" in endpoint else 30

            with httpx.Client(timeout=timeout) as client:
                response = client.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=json_data,
                    params=params,
                )

            logger.debug(f"DocChain请求URL: {url}")
            if params:
                logger.debug(f"DocChain请求参数: {params}")
            logger.debug(f"DocChain响应状态码: {response.status_code}")
            
            response.raise_for_status()

            if response.text:
                # 对于文件读取接口，先尝试直接返回文本内容
                if "doc/read" in endpoint:
                    try:
                        # 尝试解析为JSON
                        json_response = response.json()
                        return json_response
                    except json.JSONDecodeError:
                        # 如果不是JSON，直接返回文本内容
                        return {"text": response.text}
                else:
                    # 其他接口正常处理JSON
                    try:
                        return response.json()
                    except json.JSONDecodeError:
                        return {"text": response.text}
            return {}

        except httpx.RequestError as e:
            logger.error(f"DocChain请求失败: {e}")
            raise Exception(f"DocChain请求失败: {str(e)}")
        except httpx.HTTPStatusError as e:
            logger.error(f"DocChain HTTP错误: {e}")
            logger.error(f"响应内容: {e.response.text}")
            raise Exception(f"DocChain HTTP错误: {str(e)}")

    def search(self, query: str, topic_id: str, size: int = 5) -> Optional[Dict]:
        """
        在DocChain中搜索内容

        Args:
            query: 搜索查询
            topic_id: Topic ID
            size: 返回结果数量

        Returns:
            Optional[Dict]: 搜索响应，如果失败则返回None
        """
        try:
            request_data = {
                "query": query,
                "topic_id": topic_id,
                "size": size,
                "with_context": True,
                "score": 0.1,
                "ranking_mode": "rrf",
                "rrf_k": 60
            }
            
            response = self._make_request("POST", "/v1/search", data=request_data)
            
            # 处理搜索结果
            search_results = {
                "text": [],
                "image": [],
                "table": []
            }
            
            for item in response.get("text", []):
                search_results["text"].append({
                    "score": item.get("score", 0),
                    "data": item.get("data", "")
                })
                
            for item in response.get("image", []):
                search_results["image"].append({
                    "score": item.get("score", 0),
                    "data": item.get("data", "")
                })
                
            for item in response.get("table", []):
                search_results["table"].append({
                    "score": item.get("score", 0),
                    "data": item.get("data", "")
                })
            
            return search_results
            
        except Exception as e:
            logger.error(f"DocChain搜索失败: {e}")
            return None

    def list_topics(self) -> List[Dict]:
        """
        获取所有Topic列表

        Returns:
            List[Dict]: Topic信息列表
        """
        try:
            response = self._make_request("GET", "/llmdoc/v1/topic/list")
            topics = []
            for topic_data in response:
                topics.append({
                    "id": topic_data["id"],
                    "name": topic_data["name"],
                    "state": topic_data["state"],
                    "create_date": topic_data["create_date"]
                })
            return topics
        except Exception as e:
            logger.error(f"获取Topic列表失败: {e}")
            return []

    def read_file(self, doc_id: str, read_format: str = "md", page: int = 1, size: int = 50000) -> Optional[str]:
        """
        读取DocChain中指定文档的完整内容

        Args:
            doc_id: 文档ID
            read_format: 读取格式，默认为"md"
            page: 页码，默认为1
            size: 每页大小，默认为50000

        Returns:
            Optional[str]: 文档内容，如果失败则返回None
        """
        try:
            params = {
                "doc_id": doc_id,
                "read_format": read_format,
                "page": page,
                "size": size
            }
            
            response = self._make_request("GET", "/v1/doc/read", params=params)
            
            # 如果响应是字典且包含text字段，返回text内容
            if isinstance(response, dict) and "text" in response:
                return response["text"]
            
            # 如果响应是字典但没有text字段，可能直接就是内容
            if isinstance(response, dict):
                # 尝试JSON序列化返回
                return json.dumps(response, ensure_ascii=False, indent=2)
            
            # 如果响应是字符串，直接返回
            if isinstance(response, str):
                return response
            
            logger.warning(f"意外的响应格式: {type(response)}")
            return str(response)
            
        except Exception as e:
            logger.error(f"DocChain文件读取失败: {e}")
            return None

    def search_by_filename(self, keyword: str, topic_id: str, page: int = 1, size: int = 10) -> List[Dict]:
        """
        根据文件名关键词搜索文档，返回文档ID和文档名称列表

        Args:
            keyword: 文件名关键词
            topic_id: Topic ID
            page: 页码，默认为1
            size: 每页大小，默认为10

        Returns:
            List[Dict]: 包含id和docName的文档列表
        """
        try:
            params = {
                "page": page,
                "size": size,
                "topic_id": topic_id,
                "keyword": keyword,
                "state": 3
            }
            
            response = self._make_request("GET", "/v1/doc/list/pagination", params=params)
            
            # 处理返回结果，只返回id和docName(即path)
            result_list = []
            if response.get("success") and response.get("data") and response["data"].get("list"):
                for item in response["data"]["list"]:
                    result_list.append({
                        "id": str(item.get("id", "")),
                        "docName": item.get("path", "")
                    })
            
            return result_list
            
        except Exception as e:
            logger.error(f"DocChain按文件名搜索失败: {e}")
            return []


def _get_docchain_config(config: Optional[RunnableConfig] = None):
    """获取DocChain配置，优先从前端配置获取，如果没有则使用配置文件"""
    try:
        # 优先使用前端传递的配置
        if config:
            configurable = Configuration.from_runnable_config(config)
            if configurable.docchain_base_url and configurable.docchain_api_key:
                logger.info(f"使用前端DocChain配置: {configurable.docchain_base_url}")
                return configurable.docchain_base_url, configurable.docchain_api_key
        
        # 回退到配置文件
        yaml_config = load_yaml_config("conf.yaml")
        docchain_config = yaml_config.get("DOCCHAIN", {})
        base_url = docchain_config.get("base_url", "")
        api_key = docchain_config.get("api_key", "")
        
        if not base_url or not api_key:
            logger.warning("DocChain配置不完整，请检查前端设置或conf.yaml中的DOCCHAIN配置")
            return None, None
            
        logger.info(f"使用配置文件DocChain配置: {base_url}")
        return base_url, api_key
    except Exception as e:
        logger.error(f"加载DocChain配置失败: {e}")
        return None, None


def _get_topic_id_from_config(config: Optional[RunnableConfig], topic_type: str) -> str:
    """
    从配置中获取topic ID
    
    Args:
        config: LangGraph运行配置
        topic_type: topic类型，'code' 或 'doc'
    
    Returns:
        str: topic ID
    """
    if not config:
        # 使用默认值
        return "1977" if topic_type == "code" else "1998"
    
    try:
        configurable = Configuration.from_runnable_config(config)
        
        # 如果有项目配置
        if configurable.project:
            # 优先使用前端传递的topics配置
            if (configurable.topics and 
                configurable.project in configurable.topics):
                frontend_topics = configurable.topics[configurable.project]
                if topic_type == "code" and "codeTopicId" in frontend_topics:
                    return frontend_topics["codeTopicId"]
                elif topic_type == "doc" and "docTopicId" in frontend_topics:
                    return frontend_topics["docTopicId"]
            
            # 如果没有前端配置，使用默认的项目配置
            project_topics = get_project_topics(configurable.project)
            topic_key = f"{topic_type}_topic"
            if topic_key in project_topics:
                return project_topics[topic_key]
        
        # 如果都没有，使用默认值
        return "1977" if topic_type == "code" else "1998"
        
    except Exception as e:
        logger.error(f"获取topic配置失败: {e}")
        return "1977" if topic_type == "code" else "1998"


def docchain_search_tool(
    query: str,
    topic_id: str,
    size: int = 5,
    config: Optional[RunnableConfig] = None,
) -> str:
    """
    DocChain搜索基础方法。
    这个方法可以搜索已上传到DocChain的文档内容，包括代码、文档、表格等各种类型的信息。
    """
    try:
        base_url, api_key = _get_docchain_config(config)
        if not base_url or not api_key:
            return "DocChain配置未正确设置，请检查前端设置或conf.yaml文件中的DOCCHAIN配置"
        
        searcher = DocChainSearcher(base_url, api_key)
        
        # 执行搜索
        results = searcher.search(query, topic_id, size)
        if not results:
            return f"在Topic {topic_id} 中搜索 '{query}' 失败"
        
        # 格式化搜索结果
        formatted_results = []
        
        # 处理文本结果
        if results.get("text"):
            formatted_results.append("## 文本搜索结果")
            for i, item in enumerate(results["text"][:size]):
                score = item.get("score", 0)
                data_info = item.get("data", {})
                doc_id = data_info.get("doc_id", "")
                heading_chain = data_info.get("heading_chain", "")
                content = data_info.get("content", "")
                
                formatted_results.append(f"### 结果 {i+1} (相关度: {score:.3f})")
                formatted_results.append(f"**DocID**: {doc_id}")
                if heading_chain:
                    formatted_results.append(f"**章节**: {heading_chain}")
                formatted_results.append(f"**内容**: {content}")
                
                # 处理上下文信息
                if data_info.get("context") is not None:
                    context = data_info.get("context")
                    formatted_results.append("**相关上下文**:")
                    for j, context_item in enumerate(context[:3]):  # 限制上下文数量
                        ctx_doc_id = context_item.get("doc_id", "")
                        ctx_heading = context_item.get("heading_chain", "")
                        ctx_content = context_item.get("content", "")
                        formatted_results.append(f"  - DocID: {ctx_doc_id} | 章节: {ctx_heading} | 内容: {ctx_content[:1000]}...")

                formatted_results.append("")
        
        # 处理表格结果
        if results.get("table"):
            formatted_results.append("## 表格搜索结果")
            for i, item in enumerate(results["table"][:3]):  # 限制表格数量
                score = item.get("score", 0)
                data_info = item.get("data", {})
                doc_id = data_info.get("doc_id", "")
                content = data_info.get("content", "")
                formatted_results.append(f"### 表格 {i+1} (相关度: {score:.3f})")
                formatted_results.append(f"**DocID**: {doc_id}")
                formatted_results.append(f"**内容**: {content}")
                formatted_results.append("")
        
        # 处理图像结果
        if results.get("image"):
            formatted_results.append("## 图像搜索结果")
            for i, item in enumerate(results["image"][:3]):  # 限制图像数量
                score = item.get("score", 0)
                data_info = item.get("data", {})
                doc_id = data_info.get("doc_id", "")
                content = data_info.get("content", "")
                formatted_results.append(f"### 图像 {i+1} (相关度: {score:.3f})")
                formatted_results.append(f"**DocID**: {doc_id}")
                formatted_results.append(f"**内容**: {content}")
                formatted_results.append("")
        
        if not formatted_results:
            return f"在Topic {topic_id} 中没有找到与 '{query}' 相关的内容"
        
        result_text = "\n".join(formatted_results)
        return f"DocChain搜索结果 (查询: '{query}', Topic: {topic_id}):\n\n{result_text}"
        
    except Exception as e:
        error_msg = f"DocChain搜索失败: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
@log_io
def search_code_by_docchain(
    query: Annotated[str, "搜索查询内容"],
    topic_id: Annotated[str, "需要搜索的topic_id"] = None,
    size: Annotated[int, "返回结果数量"] = 5,
    config: RunnableConfig = None,
) -> str:
    """
    使用DocChain搜索代码相关信息的工具。
    适用于搜索代码文件、Java类、方法实现、配置文件、API接口等代码元素。
    """
    try:
        # 如果没有提供topic_id，从配置中获取
        if topic_id is None:
            topic_id = _get_topic_id_from_config(config, "code")
        
        logger.info(f"[DocChain代码搜索] 查询: '{query}', Topic ID: '{topic_id}'")
        return docchain_search_tool(query, topic_id, size, config)
    except Exception as e:
        error_msg = f"DocChain代码搜索出错: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
@log_io
def search_doc_by_docchain(
    query: Annotated[str, "搜索查询内容"],
    topic_id: Annotated[str, "需要搜索的topic_id"] = None,
    size: Annotated[int, "返回结果数量"] = 5,
    config: RunnableConfig = None,
) -> str:
    """
    使用DocChain搜索文档相关信息的工具。
    适用于搜索技术文档、业务规范、操作手册、API文档等文档内容。
    """
    try:
        # 如果没有提供topic_id，从配置中获取
        if topic_id is None:
            topic_id = _get_topic_id_from_config(config, "doc")
        
        logger.info(f"[DocChain文档搜索] 查询: '{query}', Topic ID: '{topic_id}'")
        return docchain_search_tool(query, topic_id, size, config)
    except Exception as e:
        error_msg = f"DocChain文档搜索出错: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
@log_io
def file_read_by_docchain(
    doc_ids: Annotated[List[str], "DocChain文档ID列表，从search_code_by_docchain或search_doc_by_docchain的搜索结果中获取"],
    page: Annotated[int, "页码，默认为1"] = 1,
    size: Annotated[int, "每页大小，默认为80000"] = 80000,
    config: RunnableConfig = None,
) -> str:
    """
    读取DocChain中指定文档的完整内容，支持分页读取和批量读取多个文件。
    这个工具可以获取文档的完整内容，包括完整的代码文件、文档等。
    必须先使用search_code_by_docchain或search_doc_by_docchain搜索获取doc_id，然后使用此工具读取完整内容。
    
    参数说明：
    - doc_ids: 文档ID列表，必须从search_code_by_docchain或search_doc_by_docchain的搜索结果中获取
    - page: 页码，默认为1
    - size: 每页大小，默认为80000字符
    """
    try:
        if not doc_ids:
            return "错误：doc_ids是必需参数，请先使用search_code_by_docchain或search_doc_by_docchain搜索获取doc_id"
        
        if not isinstance(doc_ids, list):
            return "错误：doc_ids必须是列表格式，例如：['doc_id1', 'doc_id2']"
        
        base_url, api_key = _get_docchain_config(config)
        if not base_url or not api_key:
            return "DocChain配置未正确设置，请检查前端设置或conf.yaml文件中的DOCCHAIN配置"
        
        searcher = DocChainSearcher(base_url, api_key)
        
        # 批量读取文件内容
        all_results = []
        
        for doc_id in doc_ids:
            try:
                # 读取文件内容，使用传入的分页参数
                content = searcher.read_file(doc_id, page=page, size=size)
                if not content:
                    all_results.append(f"## 文档读取失败\n**文档ID**: {doc_id}\n**错误**: 无法读取文档内容\n")
                    continue
                
                # 格式化单个文档输出
                formatted_result = []
                formatted_result.append(f"## DocChain文档内容")
                formatted_result.append(f"")
                formatted_result.append(f"**文档ID**: {doc_id}")
                formatted_result.append(f"**页码**: {page}")
                formatted_result.append(f"**每页大小**: {size}")
                formatted_result.append(f"**内容长度**: {len(content)} 字符")
                formatted_result.append(f"")
                formatted_result.append("### 文档内容")
                formatted_result.append("")
                
                # 如果内容太长，进行适当截取并提示
                if len(content) > 80000:
                    formatted_result.append("```")
                    formatted_result.append(content[:80000])
                    formatted_result.append("```")
                    formatted_result.append("")
                    formatted_result.append(f"注意：文档内容已截取前80000字符显示。完整内容共{len(content)}字符。")
                else:
                    formatted_result.append("```")
                    formatted_result.append(content)
                    formatted_result.append("```")
                
                all_results.append("\n".join(formatted_result))
                
            except Exception as e:
                error_msg = f"## 文档读取失败\n**文档ID**: {doc_id}\n**错误**: {str(e)}\n"
                all_results.append(error_msg)
                logger.error(f"读取文档{doc_id}失败: {e}")
        
        if not all_results:
            return f"所有文档读取失败：doc_ids={doc_ids}"
        
        # 组合所有结果
        final_result = f"# DocChain批量文档读取结果\n\n**读取文档数量**: {len(doc_ids)}\n**成功读取**: {len([r for r in all_results if '文档读取失败' not in r])}\n\n"
        final_result += "\n\n---\n\n".join(all_results)
        
        return final_result
        
    except Exception as e:
        error_msg = f"DocChain文件读取工具执行失败: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
@log_io
def search_file_by_name(
    keyword: Annotated[str, "文件名关键词，例如：'TimeUtil'、'UserController.java'等"],
    topic_id: Annotated[str, "需要搜索的topic_id"] = None,
    page: Annotated[int, "页码，默认为1"] = 1,
    size: Annotated[int, "每页大小，默认为10"] = 10,
    config: RunnableConfig = None,
) -> str:
    """
    根据文件名搜索DocChain中的文档，返回文档ID和文档名称列表。
    这个工具可以帮助你根据文件名找到具体的文档ID，然后可以使用file_read_by_docchain读取完整内容。
    
    使用方法：
    - 传入文件名称，文件名称可以从search_code_by_docchain、java_method_call_chain结果中获取
    - 也可以自行合理推测，对于Java代码就是"类名.java"文件
    - 工具会返回文档ID和文档名称列表，根据文件名判断出具体需要什么文件
    - 使用返回的ID可以通过file_read_by_docchain读取全部内容
    
    参数说明：
    - keyword: 文件名关键词，支持模糊匹配
    - topic_id: Topic ID，如果不提供会根据配置自动获取
    - page: 页码，默认为1
    - size: 每页大小，默认为10
    """
    try:
        # 如果没有提供topic_id，从配置中获取代码仓库的topic_id
        if topic_id is None:
            topic_id = _get_topic_id_from_config(config, "code")
        
        base_url, api_key = _get_docchain_config(config)
        if not base_url or not api_key:
            return "DocChain配置未正确设置，请检查前端设置或conf.yaml文件中的DOCCHAIN配置"
        
        searcher = DocChainSearcher(base_url, api_key)
        
        # 根据文件名搜索文档
        results = searcher.search_by_filename(keyword, topic_id, page, size)
        
        if not results:
            return f"在Topic {topic_id} 中没有找到包含关键词 '{keyword}' 的文件"
        
        # 格式化搜索结果
        formatted_results = []
        formatted_results.append(f"# DocChain按文件名搜索结果")
        formatted_results.append(f"")
        formatted_results.append(f"**搜索关键词**: {keyword}")
        formatted_results.append(f"**Topic ID**: {topic_id}")
        formatted_results.append(f"**页码**: {page}")
        formatted_results.append(f"**找到文件数量**: {len(results)}")
        formatted_results.append("")
        formatted_results.append("## 文件列表")
        formatted_results.append("")
        
        for i, item in enumerate(results, 1):
            doc_id = item.get("id", "")
            doc_name = item.get("docName", "")
            formatted_results.append(f"### 文件 {i}")
            formatted_results.append(f"**文档ID**: {doc_id}")
            formatted_results.append(f"**文件名**: {doc_name}")
            formatted_results.append("")
        
        formatted_results.append("## 使用说明")
        formatted_results.append("选择需要的文档ID，然后使用 `file_read_by_docchain` 工具读取完整内容：")
        formatted_results.append("```")
        formatted_results.append(f"file_read_by_docchain(doc_ids=[\"文档ID\"])")
        formatted_results.append("```")
        
        result_text = "\n".join(formatted_results)
        
        logger.info(f"[DocChain文件名搜索] 关键词: '{keyword}', Topic ID: '{topic_id}', 找到 {len(results)} 个文件")
        return result_text
        
    except Exception as e:
        error_msg = f"DocChain按文件名搜索出错: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
@log_io
def search_by_docchain_llm(
    query: Annotated[str, "要询问DocChain大模型的问题或查询内容"],
    topic_ids: Annotated[str, "需要搜索的topic_ids，可以是单个或多个，用逗号分隔，如'1977'或'1977,1998'"] = None,
    config: RunnableConfig = None,
) -> str:
    """
    使用DocChain大模型对话接口进行智能搜索和问答的工具。
    这是一个强大的智能搜索工具，可以与DocChain平台的大模型进行对话，获取精准的代码定位和技术信息。
    
    特点：
    - 支持自然语言问答，可以直接提问获取答案
    - 大模型会自动搜索相关文档和代码
    - 提供精准的代码定位信息（类名、方法名、文件路径等）
    - 支持复杂查询，如API接口实现位置、代码逻辑分析等
    
    使用场景：
    - 快速定位API接口的实现类和方法
    - 查询特定功能的代码实现
    - 理解复杂业务逻辑
    - 获取代码架构和设计信息
    
    使用示例：
    - "wfm/itf/appointment/om/appointmentQuery/v1 这个接口在哪个类中，方法的全限定名是什么"
    - "用户登录的实现逻辑是怎样的"
    - "OrderService类有哪些主要方法"
    
    参数说明：
    - query: 要询问的问题，支持自然语言
    - topic_ids: Topic ID列表，用逗号分隔，如果不指定会使用默认配置
    """
    try:
        base_url, api_key = _get_docchain_config(config)
        if not base_url or not api_key:
            return "DocChain配置未正确设置，请检查前端设置或conf.yaml文件中的DOCCHAIN配置"
        
        # 如果没有提供topic_ids，从配置中获取
        if topic_ids is None:
            # 默认同时使用代码和文档的topic
            code_topic = _get_topic_id_from_config(config, "code")
            doc_topic = _get_topic_id_from_config(config, "doc")
            topic_ids = f"{code_topic},{doc_topic}"
        
        # 构建请求URL
        chat_url = f"{base_url.rstrip('/')}/openai/v1/chat/completions"
        
        # 构建请求数据
        request_data = {
            "model": topic_ids,  # 使用topic_ids作为model参数
            "messages": [
                {
                    "role": "user",
                    "content": query
                }
            ],
            "stream": False,
            "temperature": 0.1,  # 设置较低的温度以获得更一致的结果
            "max_tokens": 4000   # 设置合适的token限制
        }
        
        # 构建请求头
        headers = {
            "Content-Type": "application/json",
            "X-Api-Key": api_key
        }
        
        # 发送请求
        try:
            with httpx.Client(timeout=60) as client:
                response = client.post(
                    chat_url,
                    json=request_data,
                    headers=headers
                )
            
            logger.debug(f"DocChain LLM请求URL: {chat_url}")
            logger.debug(f"DocChain LLM请求数据: {request_data}")
            logger.debug(f"DocChain LLM响应状态码: {response.status_code}")
            
            response.raise_for_status()
            
            # 解析响应
            response_data = response.json()
            
            
            # 提取AI回复内容
            if "choices" in response_data and len(response_data["choices"]) > 0:
                ai_response = response_data["choices"][0].get("message", {}).get("content", "")
                
                if ai_response:
                    # 格式化返回结果
                    formatted_result = f"# DocChain大模型智能搜索结果\n\n"
                    formatted_result += f"**查询问题**: {query}\n\n"
                    formatted_result += f"**使用Topic**: {topic_ids}\n\n"
                    formatted_result += f"## AI回答\n\n{ai_response}\n\n"
                    
                    logger.info(f"[DocChain大模型搜索] 查询: '{query}', Topic: '{topic_ids}', 响应长度: {len(ai_response)}")
                    return formatted_result
                else:
                    return f"DocChain大模型返回空内容，查询: '{query}', Topic: {topic_ids}"
            else:
                return f"DocChain大模型响应格式异常，查询: '{query}', Topic: {topic_ids}"
                
        except httpx.RequestError as e:
            logger.error(f"DocChain大模型请求失败: {e}")
            return f"DocChain大模型请求失败: {str(e)}"
        except httpx.HTTPStatusError as e:
            logger.error(f"DocChain大模型HTTP错误: {e}")
            logger.error(f"响应内容: {e.response.text}")
            return f"DocChain大模型HTTP错误: {str(e)}"
        except json.JSONDecodeError as e:
            logger.error(f"DocChain大模型响应解析失败: {e}")
            return f"DocChain大模型响应解析失败: {str(e)}"
            
    except Exception as e:
        error_msg = f"DocChain大模型搜索工具执行失败: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
@log_io
def smart_code_analyzer(
    query: Annotated[str, "要分析的代码问题或查询内容，例如：'wfm/itf/appointment/om/appointmentQuery/v1 这个接口的实现位置和调用关系'"],
    topic_id: Annotated[str, "需要搜索的代码库topic_id，如'1977'"] = None,
    config: RunnableConfig = None,
) -> str:
    """
    智能代码分析器 - 代码定位与结构分析工具。
    
    这是一个专注于代码定位和结构分析的工具，可以帮助快速找到接口实现位置和相关代码：
    1. 智能定位代码文件和类的位置
    2. 识别相关方法和调用关系
    3. 提供代码结构的简要分析
    
    特点：
    - 快速定位代码位置
    - 识别类和方法的关系
    - 提供代码结构概览
    - 自动读取相关文件内容
    
    适用场景：
    - 查找API接口实现位置
    - 定位特定类或方法
    - 了解代码文件结构
    - 分析基本调用关系
    
    参数说明：
    - query: 要查询的代码问题，如接口路径、类名或方法名
    - topic_id: 代码库Topic ID，如果不指定会使用默认配置
    """
    try:
        base_url, api_key = _get_docchain_config(config)
        if not base_url or not api_key:
            return "DocChain配置未正确设置，请检查前端设置或conf.yaml文件中的DOCCHAIN配置"
        
        # 如果没有提供topic_id，从配置中获取代码库topic
        if topic_id is None:
            # 仅使用代码库的topic
            topic_id = _get_topic_id_from_config(config, "code")
        
        # 第一步：使用AI智能搜索，要求结构化输出
        logger.info(f"[智能代码分析器] 第1步：AI智能搜索 - 查询: '{query}'")
        
        # 构建结构化提示词，强化JSON格式要求
        structured_query = f"""
        请分析以下代码查询，必须严格按照JSON格式返回分析结果。

        查询内容：{query}

        重要要求：
        1. 必须返回纯JSON格式，不要包含任何markdown代码块标记（如```json）
        2. 不要添加任何解释性文字或描述
        3. 确保JSON格式完全正确，可以被标准JSON解析器解析
        4. 如果分析成功，result字段设置为"true"，失败设置为"false"
        5. 只关注代码定位和结构分析，不要进行过度分析或猜测
        6. 只返回能从代码中直接获取的信息，不要添加主观判断

        返回格式：
        {{
            "result": "true/false",
            "analysis_summary": "简要概述查询的接口或代码位置",
            "files": [
                {{
                    "file_name": "文件名，如：AppointmentController.java",
                    "package_path": "Java包路径，如：com.example.controller",
                    "class_name": "类名，如：AppointmentController",
                    "description": "文件简要描述"
                }}
            ],
            "methods": [
                {{
                    "package_path": "Java包路径",
                    "class_name": "类名",
                    "method_name": "方法名",
                    "description": "方法简要描述",
                    "file_name": "所在文件名"
                }}
            ],
            "key_insights": "关键发现，仅限于代码结构和调用关系"
        }}

        请立即返回JSON，不要包含任何其他内容。重点关注代码定位，不要过度分析业务逻辑或添加猜测性内容。
        """
        
        # 调用DocChain LLM进行结构化查询
        chat_url = f"{base_url.rstrip('/')}/openai/v1/chat/completions"
        request_data = {
            "model": topic_id,  # 仅使用代码库topic
            "messages": [
                {
                    "role": "user",
                    "content": structured_query
                }
            ],
            "stream": False,
            "temperature": 0.1,
            "max_tokens": 4000
        }
        
        # 构建请求头
        headers = {
            "Content-Type": "application/json",
            "X-Api-Key": api_key
        }

        # 发送请求获取AI分析结果
        try:
            with httpx.Client(timeout=60) as client:
                response = client.post(
                    chat_url,
                    json=request_data,
                    headers=headers
                )
            
            response.raise_for_status()
            response_data = response.json()
            
            if "choices" not in response_data or len(response_data["choices"]) == 0:
                return f"AI分析失败：响应格式异常"
            
            ai_response = response_data["choices"][0].get("message", {}).get("content", "")
            if not ai_response:
                return f"AI分析失败：返回空内容"
            
            logger.debug(f"AI原始响应: {ai_response}")
            
            # 清理AI响应：移除docchain-references块
            cleaned_response = ai_response
            
            # 移除```docchain-references开始到```结束的所有内容
            docchain_pattern = r'```docchain-references.*?```'
            cleaned_response = re.sub(docchain_pattern, '', cleaned_response, flags=re.DOTALL)
            
            # 移除多余的空行
            cleaned_response = re.sub(r'\n\s*\n', '\n', cleaned_response).strip()
            
            logger.debug(f"清理后的AI响应: {cleaned_response}")
            
        except Exception as e:
            logger.error(f"AI智能搜索失败: {e}")
            return f"AI智能搜索失败: {str(e)}"
        
        # 第二步：解析AI的JSON响应
        logger.info(f"[智能代码分析器] 第2步：解析AI响应")
        
        try:
            # 尝试提取JSON内容
            import re
            
            # 尝试直接解析
            try:
                analysis_result = json.loads(cleaned_response)
                
                # 确保必要字段存在
                if "result" not in analysis_result:
                    analysis_result["result"] = "true"
                if "analysis_summary" not in analysis_result:
                    analysis_result["analysis_summary"] = "接口分析完成"
                if "files" not in analysis_result:
                    analysis_result["files"] = []
                if "methods" not in analysis_result:
                    analysis_result["methods"] = []
                if "key_insights" not in analysis_result:
                    analysis_result["key_insights"] = ""
                    
            except json.JSONDecodeError:
                # 如果直接解析失败，尝试提取代码块中的JSON
                json_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', cleaned_response, re.DOTALL)
                if json_match:
                    json_content = json_match.group(1)
                    analysis_result = json.loads(json_content)
                else:
                    # 如果还是失败，尝试查找第一个完整的JSON对象
                    json_match = re.search(r'\{.*\}', cleaned_response, re.DOTALL)
                    if json_match:
                        json_content = json_match.group(0)
                        analysis_result = json.loads(json_content)
                    else:
                        # 如果所有尝试都失败，创建一个基本的结果结构
                        analysis_result = {
                            "result": "false",
                            "analysis_summary": "JSON解析失败，无法提取有效内容",
                            "files": [],
                            "methods": [],
                            "key_insights": "解析失败，请检查原始响应"
                        }
                
                # 确保必要字段存在（对于从文本中提取的JSON）
                if "result" not in analysis_result:
                    analysis_result["result"] = "true"
                if "analysis_summary" not in analysis_result:
                    analysis_result["analysis_summary"] = "接口分析完成"
                if "files" not in analysis_result:
                    analysis_result["files"] = []
                if "methods" not in analysis_result:
                    analysis_result["methods"] = []
                if "key_insights" not in analysis_result:
                    analysis_result["key_insights"] = ""
            
            logger.debug(f"解析后的分析结果: {json.dumps(analysis_result, ensure_ascii=False, indent=2)}")
            
        except Exception as e:
            logger.warning(f"JSON解析失败，使用原始AI响应: {e}")
            # 如果JSON解析失败，返回原始AI响应
            formatted_result = f"# 智能代码分析结果\n\n"
            formatted_result += f"**查询问题**: {query}\n\n"
            formatted_result += f"**使用Topic**: {topic_id}\n\n"
            formatted_result += f"## AI分析结果\n\n{cleaned_response}\n\n"
            formatted_result += f"**注意**: AI响应未按JSON格式返回，显示原始内容\n"
            return formatted_result
        
        # 第三步：根据解析结果进行文件检索和内容分析
        logger.info(f"[智能代码分析器] 第3步：文件检索和内容分析")
        
        all_analysis_results = []
        all_analysis_results.append(f"# 智能代码分析完整报告")
        all_analysis_results.append(f"")
        all_analysis_results.append(f"**查询问题**: {query}")
        all_analysis_results.append(f"**使用Topic**: {topic_id}")
        all_analysis_results.append(f"**分析时间**: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        all_analysis_results.append(f"")
        
        # 添加AI分析摘要
        if analysis_result.get("analysis_summary"):
            all_analysis_results.append(f"## AI分析摘要")
            all_analysis_results.append(f"{analysis_result['analysis_summary']}")
            all_analysis_results.append(f"")
        
        # 处理文件分析
        files_info = analysis_result.get("files", [])
        if files_info:
            all_analysis_results.append(f"## 相关文件分析")
            all_analysis_results.append(f"")
            
            for i, file_info in enumerate(files_info[:5]):  # 限制处理文件数量
                file_name = file_info.get("file_name", "")
                if not file_name:
                    continue
                
                all_analysis_results.append(f"### 文件 {i+1}: {file_name}")
                all_analysis_results.append(f"**描述**: {file_info.get('description', '无')}")
                all_analysis_results.append(f"")
                
                # 使用search_file_by_name查找文件
                try:
                    # 使用代码topic搜索文件
                    code_topic = _get_topic_id_from_config(config, "code")
                    searcher = DocChainSearcher(base_url, api_key)
                    file_search_results = searcher.search_by_filename(file_name, code_topic, 1, 5)
                    
                    if file_search_results:
                        # 取第一个匹配的文件
                        doc_id = file_search_results[0].get("id", "")
                        if doc_id:
                            all_analysis_results.append(f"**文件ID**: {doc_id}")
                            
                            # 读取文件内容
                            file_content = searcher.read_file(doc_id, page=1, size=80000)
                            if file_content:
                                all_analysis_results.append(f"**文件内容长度**: {len(file_content)} 字符")
                                
                                # 分析文件结构
                                lines = file_content.split('\n')
                                total_lines = len(lines)
                                
                                # 提取包声明
                                package_line = None
                                import_lines = []
                                class_lines = []
                                
                                for i, line in enumerate(lines):
                                    line_stripped = line.strip()
                                    if line_stripped.startswith('package '):
                                        package_line = line_stripped
                                    elif line_stripped.startswith('import '):
                                        import_lines.append(line_stripped)
                                    elif ('class ' in line_stripped or 'interface ' in line_stripped) and ('public ' in line_stripped or 'class ' in line_stripped):
                                        class_lines.append((i+1, line_stripped))  # 记录行号和内容
                                
                                all_analysis_results.append(f"**文件总行数**: {total_lines}")
                                all_analysis_results.append(f"")
                                
                                # 显示文件结构信息
                                all_analysis_results.append("**文件结构信息**:")
                                if package_line:
                                    all_analysis_results.append(f"- 包声明: `{package_line}`")
                                if import_lines:
                                    all_analysis_results.append(f"- 导入数量: {len(import_lines)}个")
                                    # 显示主要导入（非java.lang的）
                                    main_imports = [imp for imp in import_lines if not 'java.lang.' in imp][:5]
                                    if main_imports:
                                        all_analysis_results.append(f"- 主要导入:")
                                        for imp in main_imports:
                                            all_analysis_results.append(f"  • `{imp}`")
                                        if len(main_imports) > 5:
                                            all_analysis_results.append(f"  • ... (还有{len(import_lines)-5}个导入)")
                                if class_lines:
                                    all_analysis_results.append(f"- 类/接口定义:")
                                    for line_num, class_def in class_lines:
                                        all_analysis_results.append(f"  • 第{line_num}行: `{class_def}`")
                                all_analysis_results.append(f"")
                                
                                # 显示文件内容
                                all_analysis_results.append("**完整文件内容**:")
                                all_analysis_results.append("```java")
                                
                                # 如果内容太长，提供分段显示策略
                                if len(file_content) > 15000:
                                    # 显示前面的重要部分（包声明、导入、类定义）
                                    important_lines = []
                                    in_class = False
                                    brace_count = 0
                                    
                                    for i, line in enumerate(lines):
                                        line_stripped = line.strip()
                                        
                                        # 总是包含包声明和导入
                                        if (line_stripped.startswith('package ') or 
                                            line_stripped.startswith('import ') or
                                            line_stripped.startswith('/**') or
                                            line_stripped.startswith('*') or
                                            line_stripped.startswith('*/')):
                                            important_lines.append(line)
                                        
                                        # 包含类定义和主要方法签名
                                        elif ('class ' in line_stripped or 'interface ' in line_stripped) and ('public ' in line_stripped):
                                            important_lines.append(line)
                                            in_class = True
                                        
                                        # 在类内部，记录方法签名
                                        elif in_class:
                                            if '{' in line:
                                                brace_count += line.count('{')
                                            if '}' in line:
                                                brace_count -= line.count('}')
                                            
                                            # 包含方法签名
                                            if (('public ' in line_stripped or 'private ' in line_stripped or 'protected ' in line_stripped) 
                                                and '(' in line_stripped and not line_stripped.startswith('//')):
                                                important_lines.append(line)
                                            
                                            # 包含注解
                                            elif line_stripped.startswith('@'):
                                                important_lines.append(line)
                                            
                                            # 如果到达类的结束
                                            if brace_count == 0 and '}' in line:
                                                important_lines.append(line)
                                                break
                                        
                                        # 限制行数
                                        if len(important_lines) > 200:
                                            break
                                    
                                    # 显示重要部分
                                    all_analysis_results.append('\n'.join(important_lines))
                                    all_analysis_results.append(f"\n... (文件过长，仅显示关键结构，完整长度: {len(file_content)} 字符，共{total_lines}行)")
                                    
                                else:
                                    # 文件不太长，显示全部内容
                                    all_analysis_results.append(file_content)
                                
                                all_analysis_results.append("```")
                                all_analysis_results.append("")
                            else:
                                all_analysis_results.append("**错误**: 无法读取文件内容")
                                all_analysis_results.append("")
                        else:
                            all_analysis_results.append("**错误**: 未找到对应的文档ID")
                            all_analysis_results.append("")
                    else:
                        all_analysis_results.append(f"**警告**: 未找到文件 '{file_name}'")
                        all_analysis_results.append("")
                        
                except Exception as e:
                    all_analysis_results.append(f"**错误**: 文件检索失败 - {str(e)}")
                    all_analysis_results.append("")
        
        # 第四步：方法调用链分析
        methods_info = analysis_result.get("methods", [])
        if methods_info:
            logger.info(f"[智能代码分析器] 第4步：方法调用链分析")
            
            all_analysis_results.append(f"## 方法调用链分析")
            all_analysis_results.append(f"")
            
            # 导入方法调用链分析器
            from .method_call_chain import MethodCallChainAnalyzer
            
            for i, method_info in enumerate(methods_info[:3]):  # 限制分析的方法数量
                package_path = method_info.get("package_path", "")
                class_name = method_info.get("class_name", "")
                method_name = method_info.get("method_name", "")
                
                if not all([package_path, class_name, method_name]):
                    continue
                
                all_analysis_results.append(f"### 方法 {i+1}: {package_path}.{class_name}.{method_name}")
                all_analysis_results.append(f"**描述**: {method_info.get('description', '无')}")
                all_analysis_results.append(f"")
                
                try:
                    # 执行方法调用链分析
                    analyzer = MethodCallChainAnalyzer()
                    call_chain_result = analyzer.get_method_call_chain(package_path, class_name, method_name, 3)
                    
                    if call_chain_result:
                        all_analysis_results.append("**方法调用链分析结果**:")
                        all_analysis_results.append("```json")
                        all_analysis_results.append(json.dumps(call_chain_result, indent=2, ensure_ascii=False))
                        all_analysis_results.append("```")
                        all_analysis_results.append("")
                    else:
                        all_analysis_results.append("**调用链分析失败**")
                        all_analysis_results.append("")
                        
                except Exception as e:
                    all_analysis_results.append(f"**调用链分析错误**: {str(e)}")
                    all_analysis_results.append("")
        
        # 添加关键发现
        if analysis_result.get("key_insights"):
            all_analysis_results.append(f"## 关键发现")
            all_analysis_results.append(f"{analysis_result['key_insights']}")
            all_analysis_results.append(f"")
        
        # 添加工具使用总结
        all_analysis_results.append(f"## 分析工具使用总结")
        all_analysis_results.append(f"")
        all_analysis_results.append(f"本次分析使用了以下工具的组合：")
        all_analysis_results.append(f"1. **search_by_docchain_llm**: AI智能搜索和代码定位")
        
        tool_count = 1
        if files_info:
            tool_count += 1
            all_analysis_results.append(f"{tool_count}. **search_file_by_name**: 文件名精确搜索")
            tool_count += 1
            all_analysis_results.append(f"{tool_count}. **file_read_by_docchain**: 完整文件内容读取")
        if methods_info:
            tool_count += 1
            all_analysis_results.append(f"{tool_count}. **java_method_call_chain**: 方法调用链分析（层级深度: 3）")
        
        all_analysis_results.append(f"")
        
        # 添加分析统计信息
        all_analysis_results.append(f"## 分析统计信息")
        all_analysis_results.append(f"")
        all_analysis_results.append(f"- **分析的文件数量**: {len(files_info)}")
        all_analysis_results.append(f"- **分析的方法数量**: {len(methods_info)}")
        
        # 统计调用链信息
        total_invoke_methods = 0
        total_ref_classes = 0
        for i, method_info in enumerate(methods_info[:3]):
            package_path = method_info.get("package_path", "")
            class_name = method_info.get("class_name", "")
            method_name = method_info.get("method_name", "")
            
            if all([package_path, class_name, method_name]):
                try:
                    from .method_call_chain import MethodCallChainAnalyzer
                    analyzer = MethodCallChainAnalyzer()
                    call_chain_result = analyzer.get_method_call_chain(package_path, class_name, method_name, 3)
                    
                    if call_chain_result:
                        invoke_list = call_chain_result.get("invokeList", [])
                        total_invoke_methods += len(invoke_list)
                        
                        ref_classes = call_chain_result.get("refClassDetailList", [])
                        total_ref_classes += len(ref_classes)
                except:
                    pass
        
        if total_invoke_methods > 0:
            all_analysis_results.append(f"- **发现的调用方法总数**: {total_invoke_methods}")
        if total_ref_classes > 0:
            all_analysis_results.append(f"- **相关类总数**: {total_ref_classes}")
        
        all_analysis_results.append(f"- **使用的Topic ID**: {topic_id}")
        all_analysis_results.append(f"- **分析报告长度**: {len('\\n'.join(all_analysis_results))} 字符")
        all_analysis_results.append(f"")
        all_analysis_results.append(f"**分析完成时间**: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 添加使用建议
        all_analysis_results.append(f"")
        all_analysis_results.append(f"## 后续分析建议")
        all_analysis_results.append(f"")
        if files_info:
            all_analysis_results.append(f"- 如需查看更多文件内容，可使用 `file_read_by_docchain` 工具")
        if methods_info and total_invoke_methods > 0:
            all_analysis_results.append(f"- 如需分析更深层次的调用关系，可增加 `java_method_call_chain` 的层级参数")
            all_analysis_results.append(f"- 可以针对具体的调用方法进行单独的调用链分析")
        all_analysis_results.append(f"- 可使用 `search_by_docchain_llm` 进行更具体的问题查询")
        
        final_result = "\n".join(all_analysis_results)
        
        logger.info(f"[智能代码分析器] 分析完成，结果长度: {len(final_result)} 字符")
        return final_result
        
    except Exception as e:
        error_msg = f"智能代码分析器执行失败: {str(e)}"
        logger.error(error_msg)
        return error_msg
