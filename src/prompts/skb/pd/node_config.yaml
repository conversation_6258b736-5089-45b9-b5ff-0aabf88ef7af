# PD (Product Design) 场景节点配置
metadata:
  name: "产品设计"
  description: "产品需求分析、设计规划、用户体验优化"
  category: "product_design"
  tags: ["产品设计", "需求分析", "用户体验"]
  version: "1.0.0"
  author: "系统预置"

# 全局配置
global:
  locale: "zh-CN"
  debug_mode: false
  timeout_minutes: 60
  project: ""
  code_topic_id: ""
  doc_topic_id: ""

# 节点配置
nodes:
  planner:
    enabled: true
    max_iterations: 3
    priority: "high"
    timeout_seconds: 300
    tools:
      enabled:
        - "search_by_docchain_llm"
        - "search_doc_by_docchain"
        - "search_code_by_docchain"
      configs: {}
    scenario_params:
      analysis_depth: "deep"
      focus_areas: ["用户需求", "产品功能", "技术可行性"]
    output: {}

  researcher:
    enabled: true
    max_search_results: 8
    search_depth: 4
    priority: "high"
    timeout_seconds: 300
    tools:
      enabled:
        - "search_by_docchain_llm"
        - "search_doc_by_docchain"
        - "search_code_by_docchain"
      configs: {}
    scenario_params:
      research_focus: ["竞品分析", "用户调研", "技术方案"]
      quality_threshold: "high"
    output: {}

  reporter:
    enabled: true
    priority: "medium"
    timeout_seconds: 300
    tools:
      enabled: []
      configs: {}
    scenario_params:
      report_style: "structured"
      include_visuals: true
    output: {}

  coder:
    enabled: true
    priority: "medium"
    timeout_seconds: 300
    tools:
      enabled:
        - "search_code_by_docchain"
        - "file_read_by_docchain"
      configs: {}
    scenario_params:
      code_focus: ["原型实现", "功能演示"]
    output: {}

# 工作流配置
workflow:
  max_iterations: 3
  failure_strategy: "retry"
  retry_count: 2
  enable_parallel_execution: false 