# SA (System Architecture) 场景节点配置
metadata:
  name: "系统架构"
  description: "系统架构设计、技术选型、性能优化"
  category: "system_architecture"
  tags: ["系统架构", "技术选型", "性能优化"]
  version: "1.0.0"
  author: "系统预置"

# 全局配置
global:
  locale: "zh-CN"
  debug_mode: true
  timeout_minutes: 90
  project: ""
  code_topic_id: ""
  doc_topic_id: ""

# 节点配置
nodes:
  planner:
    enabled: true
    max_iterations: 3
    priority: "high"
    timeout_seconds: 300
    tools:
      enabled:
        - "search_by_docchain_llm"
        - "search_doc_by_docchain"
        - "search_code_by_docchain"
        - "java_method_call_chain"
      configs: {}
    scenario_params:
      architecture_focus: ["系统设计", "技术选型", "性能优化"]
      complexity_level: "high"
    output: {}

  researcher:
    enabled: true
    max_search_results: 10
    search_depth: 5
    priority: "high"
    timeout_seconds: 300
    tools:
      enabled:
        - "search_by_docchain_llm"
        - "search_doc_by_docchain"
        - "search_code_by_docchain"
        - "java_method_call_chain"
        - "file_read_by_docchain"
      configs: {}
    scenario_params:
      research_areas: ["架构模式", "技术栈", "最佳实践"]
      analysis_depth: "deep"
    output: {}

  reporter:
    enabled: true
    priority: "medium"
    timeout_seconds: 300
    tools:
      enabled: []
      configs: {}
    scenario_params:
      report_type: "technical"
      include_diagrams: true
    output: {}

  coder:
    enabled: true
    priority: "high"
    timeout_seconds: 300
    tools:
      enabled:
        - "search_code_by_docchain"
        - "file_read_by_docchain"
        - "java_method_call_chain"
        - "python_repl"
      configs: {}
    scenario_params:
      coding_focus: ["架构实现", "代码示例", "最佳实践"]
      languages: ["java", "python", "javascript"]
    output: {}

# 工作流配置
workflow:
  max_iterations: 4
  failure_strategy: "retry"
  retry_count: 3
  enable_parallel_execution: true 