# TEST (Testing) 场景节点配置
metadata:
  name: "测试场景"
  description: "自动化测试用例生成、边缘场景挖掘、测试数据构造"
  category: "testing"
  tags: ["测试用例", "边缘场景", "测试数据", "自动化测试"]
  version: "1.0.0"
  author: "系统预置"

# 全局配置
global:
  locale: "zh-CN"
  debug_mode: true
  timeout_minutes: 120
  project: ""
  code_topic_id: ""
  doc_topic_id: ""

# 节点配置
nodes:
  planner:
    enabled: true
    max_iterations: 3
    priority: "high"
    timeout_seconds: 300
    tools:
      enabled:
        - "search_by_docchain_llm"
        - "search_doc_by_docchain"
        - "search_code_by_docchain"
        - "java_method_call_chain"
        - "file_read_by_docchain"
      configs: {}
    scenario_params:
      test_analysis_depth: "comprehensive"
      focus_areas: ["功能测试", "边缘场景", "性能测试", "安全测试"]
      test_coverage_target: "high"
    output: {}

  researcher:
    enabled: true
    max_search_results: 12
    search_depth: 6
    priority: "high"
    timeout_seconds: 300
    tools:
      enabled:
        - "search_by_docchain_llm"
        - "search_doc_by_docchain"
        - "search_code_by_docchain"
        - "java_method_call_chain"
        - "file_read_by_docchain"
        - "search_file_by_name"
      configs: {}
    scenario_params:
      research_focus: ["代码逻辑分析", "接口参数解析", "异常情况挖掘", "边界值识别"]
      test_case_quality: "high"
      edge_case_coverage: "comprehensive"
    output: {}

  reporter:
    enabled: true
    priority: "high"
    timeout_seconds: 300
    tools:
      enabled: []
      configs: {}
    scenario_params:
      report_style: "detailed_test_report"
      include_test_parameters: true
      include_test_scenarios: true
      include_edge_cases: true
      include_test_data: true
      support_custom_format: true
      output_json_format: true
    output: {}

  coder:
    enabled: true
    priority: "medium"
    timeout_seconds: 300
    tools:
      enabled:
        - "search_code_by_docchain"
        - "file_read_by_docchain"
        - "java_method_call_chain"
        - "python_repl"
      configs: {}
    scenario_params:
      test_framework_focus: ["junit", "testng", "mockito", "postman"]
      automation_level: "high"
    output: {}

# 工作流配置
workflow:
  max_iterations: 4
  failure_strategy: "retry"
  retry_count: 3
  enable_parallel_execution: true 