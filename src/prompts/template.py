# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import dataclasses
import logging
import os
from datetime import datetime

from jinja2 import Environment, FileSystemLoader, Template, select_autoescape
from langgraph.prebuilt.chat_agent_executor import AgentState

from src.config.configuration import Configuration
from src.config.projects import get_project_topics

# Initialize Jinja2 environment
env = Environment(
    loader=FileSystemLoader(
        searchpath=os.path.dirname(__file__),
        encoding="utf-8"
    ),
    autoescape=select_autoescape(),
    trim_blocks=True,
    lstrip_blocks=True,
)

logger = logging.getLogger(__name__)

def get_prompt_template(prompt_name: str) -> str:
    """
    Load and return a prompt template using Jinja2.

    Args:
        prompt_name: Name of the prompt template file (without .md extension)

    Returns:
        The template string with proper variable substitution syntax
    """
    try:
        template = env.get_template(f"{prompt_name}.md")
        return template.render()
    except Exception as e:
        raise ValueError(f"Error loading template {prompt_name}: {e}")


def apply_prompt_template(
    prompt_name: str, state: AgentState, configurable: Configuration = None
) -> list:
    """
    Apply a prompt template with given state variables and scenario configuration.
    
    新的提示词组装逻辑：
    1. 如果前台传入了场景名(scenario_name)，则读取预置场景提示词，组装到base_.md模板中
    2. 如果前台传入了场景配置(scenario_config)，则使用场景提示词 + base_.md模板
    3. 如果没有场景配置，则使用原有的prompt.md模板
    4. 拼接topic信息和mcp信息，渲染提示词并返回
    
    Args:
        prompt_name: Name of the template file
        state: State containing template variables
        configurable: Configuration object with scenario settings
        
    Returns:
        List of messages
    """
    logger.info(f"开始组装提示词: {prompt_name}")
    
    # 步骤1: 创建状态变量副本，添加基础变量
    max_step_num = 3
    if configurable and hasattr(configurable, "max_step_num"):
        max_step_num = configurable.max_step_num
    
    # 获取用户的原始输入（第一条用户消息）
    original_user_input = ""
    if state.get("messages"):
        for message in state.get("messages", []):
            # 支持字典格式的消息
            if isinstance(message, dict) and message.get("role") == "user":
                original_user_input = message.get("content", "")
                break
            # 支持对象格式的消息（如HumanMessage）
            elif hasattr(message, 'content') and (
                (hasattr(message, 'type') and message.type == 'human') or
                (hasattr(message, '__class__') and 'Human' in message.__class__.__name__)
            ):
                original_user_input = message.content
                break
    
    state_vars = {
        "CURRENT_TIME": datetime.now().strftime("%a %b %d %Y %H:%M:%S %z"),
        "userInput": original_user_input,
        "max_step_num": max_step_num,
        **state,
    }

    # 添加基础配置变量
    if configurable:
        try:
            # 尝试将配置对象转换为字典，如果是数据类的话
            config_dict = dataclasses.asdict(configurable)
            state_vars.update(config_dict)
        except (TypeError, AttributeError):
            # 如果不是数据类，尝试直接访问属性
            if hasattr(configurable, '__dict__'):
                state_vars.update(configurable.__dict__)
            else:
                # 手动添加常用的配置属性
                for attr in ['project', 'debug_mode', 'locale', 'scenario_name']:
                    if hasattr(configurable, attr):
                        state_vars[attr] = getattr(configurable, attr)

    # 步骤2: 拼接topic信息
    if configurable and configurable.project:
        # 优先使用前端传递的topics配置
        if hasattr(configurable, 'topics') and configurable.topics and configurable.project in configurable.topics:
            frontend_topics = configurable.topics[configurable.project]
            state_vars["codeTopicId"] = frontend_topics.get("codeTopicId", "")
            state_vars["docTopicId"] = frontend_topics.get("docTopicId", "")
            logger.info(f"使用前端topics配置: code={state_vars['codeTopicId']}, doc={state_vars['docTopicId']}")
        else:
            # 使用默认的项目配置
            try:
                project_topics = get_project_topics(configurable.project)
                state_vars["codeTopicId"] = project_topics.get("code_topic", "")
                state_vars["docTopicId"] = project_topics.get("doc_topic", "")
                logger.info(f"使用默认项目配置: code={state_vars['codeTopicId']}, doc={state_vars['docTopicId']}")
            except:
                state_vars["codeTopicId"] = ""
                state_vars["docTopicId"] = ""

    # 步骤3: 拼接mcp信息
    if configurable and hasattr(configurable, 'mcp_settings') and configurable.mcp_settings:
        mcp_tool_guidance = ""
        for server_name, server_config in configurable.mcp_settings.get("servers", {}).items():
            if prompt_name in server_config.get("add_to_agents", []):
                enabled_tools_with_desc = server_config.get("enabled_tools", [])
                if enabled_tools_with_desc:
                    mcp_tool_guidance += f"\n\n## {server_name}服务器工具\n\n"
                    for tool_detail in enabled_tools_with_desc:
                        tool_name = tool_detail.get("name", "")
                        description = tool_detail.get("description", "")
                        mcp_tool_guidance += f"- **{tool_name}**: {description}\n"
        
        if mcp_tool_guidance:
            state_vars["mcpToolGuidance"] = mcp_tool_guidance

    # 步骤4: 场景配置处理 - 核心逻辑
    scenario_system_prompt = None
    use_base_template = False
    
    if configurable:
        # 4.1 检查是否传入了场景代码(scenario_code) - 优先使用场景代码查找预置场景
        scenario_code = None
        if hasattr(configurable, 'scenario_code') and configurable.scenario_code:
            scenario_code = configurable.scenario_code
            logger.info(f"检测到场景代码: {scenario_code}，尝试查找预置场景")
        elif hasattr(configurable, 'scenario_name') and configurable.scenario_name:
            # 兼容旧的scenario_name参数
            scenario_code = configurable.scenario_name
            logger.info(f"检测到场景名: {scenario_code}，尝试查找预置场景")
            
        if scenario_code:
            try:
                from src.config.preset_scenarios import preset_scenario_manager
                scenario_prompt = preset_scenario_manager.get_node_prompt(scenario_code, prompt_name)
                if scenario_prompt:
                    # 成功找到预置场景
                    scenario_system_prompt = scenario_prompt
                    use_base_template = True
                    
                    # 加载预置场景的配置变量
                    scenario_config = preset_scenario_manager.get_scenario(scenario_code)
                    if scenario_config:
                        # 添加全局配置变量
                        global_config = scenario_config.get("global", {})
                        state_vars.update(global_config)
                        
                        # 添加节点特定配置变量
                        nodes_config = scenario_config.get("nodes", {})
                        node_config = nodes_config.get(prompt_name, {})
                        
                        # 添加节点的scenario_params
                        scenario_params = node_config.get("scenario_params", {})
                        state_vars.update(scenario_params)
                        
                        # 处理工具配置
                        tools_config = node_config.get("tools", {})
                        enabled_tools = tools_config.get("enabled", [])
                        if enabled_tools:
                            tools_list = "\n".join([f"- {tool}" for tool in enabled_tools])
                            state_vars["enabledTools"] = f"## 启用的工具\n\n{tools_list}"
                        else:
                            state_vars["enabledTools"] = ""
                    
            except Exception as e:
                logger.warning(f"加载预置场景 {scenario_code} 失败: {e}，尝试从scenario_config查找")
        
        # 4.2 如果预置场景没找到，尝试从scenario_config查找
        if not scenario_system_prompt and configurable.get_scenario_config():
            logger.info(f"从前端场景配置中查找 {prompt_name} 节点提示词")
            
            # 获取场景配置的系统提示词
            scenario_system_prompt = configurable.get_node_system_prompt(prompt_name)
            
            if scenario_system_prompt:
                use_base_template = True
                
                # 获取节点的所有配置变量
                node_config_vars = configurable.get_node_config_vars(prompt_name)
                state_vars.update(node_config_vars)
                
                # 处理工具配置
                enabled_tools = configurable.get_node_enabled_tools(prompt_name)
                if enabled_tools:
                    tools_list = "\n".join([f"- {tool}" for tool in enabled_tools])
                    state_vars["enabledTools"] = f"## 启用的工具\n\n{tools_list}"
                else:
                    state_vars["enabledTools"] = ""

    # 步骤5: 渲染提示词并返回
    # 5.1 如果有场景提示词，使用场景提示词 + base模板
    if scenario_system_prompt and use_base_template:
        logger.info(f"使用场景提示词组装base_{prompt_name}.md模板")
        try:
            # 首先渲染场景提示词
            scenario_template = Template(scenario_system_prompt)
            rendered_scenario_prompt = scenario_template.render(**state_vars)
            
            # 将渲染后的场景提示词作为变量传递给base模板
            state_vars["scenario_prompt"] = rendered_scenario_prompt
            
            # 尝试使用base模板
            base_template_name = f"base_{prompt_name}.md"
            try:
                template = env.get_template(base_template_name)
                system_prompt = template.render(**state_vars)
                logger.info(f"成功使用base模板 {base_template_name} 组装提示词")
                return [{"role": "system", "content": system_prompt}] + state["messages"]
            except Exception as e:
                logger.warning(f"base模板 {base_template_name} 不存在或渲染失败: {e}，直接使用场景提示词")
                return [{"role": "system", "content": rendered_scenario_prompt}] + state["messages"]
            
        except Exception as e:
            logger.warning(f"渲染场景提示词模板失败: {e}，直接使用原始场景提示词")
            return [{"role": "system", "content": scenario_system_prompt}] + state["messages"]
    
    # 5.2 没有场景配置，使用原有的提示词模板
    logger.info(f"没有场景配置，使用原有的{prompt_name}.md模板")
    
    # 为兼容性添加一些默认变量
    if configurable:
        # 兼容旧的场景扩展逻辑
        scenario_extension = configurable.get_node_prompt_extension(prompt_name)
        state_vars["scenario_extension"] = scenario_extension
        
        # 兼容旧的工具列表格式
        if "enabledTools" not in state_vars:
            enabled_tools = configurable.get_node_enabled_tools(prompt_name)
            if enabled_tools:
                tools_list = "\n".join([f"- {tool}" for tool in enabled_tools])
                state_vars["enabledTools"] = f"## 启用的工具\n\n{tools_list}"
            else:
                state_vars["enabledTools"] = ""
        
        # 如果没有其他工具信息，将MCP工具信息作为enabledTools
        if "mcpToolGuidance" in state_vars and ("enabledTools" not in state_vars or not state_vars["enabledTools"]):
            state_vars["enabledTools"] = state_vars["mcpToolGuidance"]

    try:
        # 5.3 优先尝试使用基础模板
        base_template_name = f"base_{prompt_name}.md"
        try:
            template = env.get_template(base_template_name)
            system_prompt = template.render(**state_vars)
            logger.info(f"使用基础模板 {base_template_name} 渲染成功")
            return [{"role": "system", "content": system_prompt}] + state["messages"]
        except:
            # 5.4 回退到原有模板
            template = env.get_template(f"{prompt_name}.md")
            system_prompt = template.render(**state_vars)
            logger.info(f"回退到原有模板 {prompt_name}.md 渲染成功")
            return [{"role": "system", "content": system_prompt}] + state["messages"]
    except Exception as e:
        raise ValueError(f"渲染模板 {prompt_name} 时发生错误: {e}")

def apply_scenario_template(
    prompt_name: str, state: AgentState, configurable: Configuration = None
) -> list:
    """
    Apply a scenario-aware prompt template.
    
    Args:
        prompt_name: Name of the template file (planner, researcher, reporter, coder)
        state: State containing template variables
        configurable: Configuration object with scenario settings
        
    Returns:
        List of messages with scenario-customized prompt
    """
    # 直接使用apply_prompt_template，它已经包含了场景感知逻辑
    return apply_prompt_template(prompt_name, state, configurable)
