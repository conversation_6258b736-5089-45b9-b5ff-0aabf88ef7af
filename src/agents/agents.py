# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from langgraph.prebuilt import create_react_agent

from src.prompts import apply_prompt_template
from src.prompts.template import apply_scenario_template
from src.llms.llm import get_llm_by_type
from src.llms.llm_manager import get_llm_by_type_v2, create_llm_config
from src.config.agents import AGENT_LLM_MAP


# Create agents using configured LLM types
def create_agent(agent_name: str, agent_type: str, tools: list, prompt_template: str, configurable=None):
    """Factory function to create agents with consistent configuration."""
    try:
        # 尝试使用新的LLM管理器
        if configurable:
            config = create_llm_config(
                api_key=configurable.api_key,
                base_url=configurable.base_url,
                model=configurable.model,
            )
            llm = get_llm_by_type_v2(AGENT_LLM_MAP[agent_type], **config.to_dict())
        else:
            # 如果没有配置，使用原有方法（从环境变量获取）
            llm = get_llm_by_type_v2(AGENT_LLM_MAP[agent_type])
    except Exception:
        # 回退到原有方法，保持向后兼容
        llm = get_llm_by_type(
            AGENT_LLM_MAP[agent_type],
            api_key=configurable.api_key if configurable else None,
            base_url=configurable.base_url if configurable else None,
            model=configurable.model if configurable else None,
        )
    
    return create_react_agent(
        name=agent_name,
        model=llm,
        tools=tools,
        prompt=lambda state: apply_prompt_template(prompt_template, state, configurable),
    )


def create_scenario_agent(agent_name: str, agent_type: str, tools: list, prompt_template: str, configurable=None):
    """
    Factory function to create agents with scenario configuration support.
    
    Args:
        agent_name: Agent名称
        agent_type: Agent类型 (planner, researcher, reporter, coder)
        tools: 工具列表
        prompt_template: 提示词模板名称
        configurable: 配置对象，包含场景配置
    
    Returns:
        配置好的智能体实例
    """
    try:
        # 尝试使用新的LLM管理器
        if configurable:
            config = create_llm_config(
                api_key=configurable.api_key,
                base_url=configurable.base_url,
                model=configurable.model,
            )
            llm = get_llm_by_type_v2(AGENT_LLM_MAP[agent_type], **config.to_dict())
        else:
            # 如果没有配置，使用原有方法（从环境变量获取）
            llm = get_llm_by_type_v2(AGENT_LLM_MAP[agent_type])
    except Exception:
        # 回退到原有方法，保持向后兼容
        llm = get_llm_by_type(
            AGENT_LLM_MAP[agent_type],
            api_key=configurable.api_key if configurable else None,
            base_url=configurable.base_url if configurable else None,
            model=configurable.model if configurable else None,
        )
    
    return create_react_agent(
        name=agent_name,
        model=llm,
        tools=tools,
        prompt=lambda state: apply_scenario_template(prompt_template, state, configurable),
    )
