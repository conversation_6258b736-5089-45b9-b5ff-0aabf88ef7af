# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
from typing import Any, Dict, List, Union

logger = logging.getLogger(__name__)


def safe_encode_string(text: str, encoding: str = 'utf-8', errors: str = 'replace') -> str:
    """
    安全地处理字符串编码，避免编码错误
    
    Args:
        text: 输入文本
        encoding: 目标编码，默认utf-8
        errors: 错误处理方式，默认replace
        
    Returns:
        经过编码处理的安全字符串
    """
    if not isinstance(text, str):
        return str(text)
    
    try:
        # 先尝试正常编码
        return text.encode(encoding, errors=errors).decode(encoding)
    except Exception as e:
        logger.warning(f"字符编码处理失败: {e}")
        # 如果失败，使用更保守的方法
        try:
            return text.encode('ascii', errors='ignore').decode('ascii')
        except Exception:
            # 最后的备选方案
            return repr(text)


def clean_dict_encoding(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    递归清理字典中的编码问题
    
    Args:
        data: 输入字典
        
    Returns:
        编码清理后的字典
    """
    if not isinstance(data, dict):
        return data
    
    cleaned = {}
    for key, value in data.items():
        # 处理键
        if isinstance(key, str):
            clean_key = safe_encode_string(key)
        else:
            clean_key = key
        
        # 处理值
        if isinstance(value, str):
            cleaned[clean_key] = safe_encode_string(value)
        elif isinstance(value, dict):
            cleaned[clean_key] = clean_dict_encoding(value)
        elif isinstance(value, list):
            cleaned[clean_key] = clean_list_encoding(value)
        else:
            cleaned[clean_key] = value
    
    return cleaned


def clean_list_encoding(data: List[Any]) -> List[Any]:
    """
    递归清理列表中的编码问题
    
    Args:
        data: 输入列表
        
    Returns:
        编码清理后的列表
    """
    if not isinstance(data, list):
        return data
    
    cleaned = []
    for item in data:
        if isinstance(item, str):
            cleaned.append(safe_encode_string(item))
        elif isinstance(item, dict):
            cleaned.append(clean_dict_encoding(item))
        elif isinstance(item, list):
            cleaned.append(clean_list_encoding(item))
        else:
            cleaned.append(item)
    
    return cleaned


def clean_message_content(message: Union[Dict[str, Any], Any]) -> Union[Dict[str, Any], Any]:
    """
    清理消息内容的编码问题
    
    Args:
        message: 输入消息
        
    Returns:
        编码清理后的消息
    """
    if isinstance(message, dict):
        cleaned_message = {}
        for key, value in message.items():
            if key == "content" and isinstance(value, str):
                cleaned_message[key] = safe_encode_string(value)
            elif isinstance(value, dict):
                cleaned_message[key] = clean_dict_encoding(value)
            elif isinstance(value, list):
                cleaned_message[key] = clean_list_encoding(value)
            elif isinstance(value, str):
                cleaned_message[key] = safe_encode_string(value)
            else:
                cleaned_message[key] = value
        return cleaned_message
    elif hasattr(message, 'content') and isinstance(message.content, str):
        # 处理类似LangChain消息对象的情况
        try:
            message.content = safe_encode_string(message.content)
        except Exception as e:
            logger.warning(f"无法修改消息对象的content属性: {e}")
    
    return message


def clean_messages_list(messages: List[Any]) -> List[Any]:
    """
    清理消息列表中的编码问题
    
    Args:
        messages: 消息列表
        
    Returns:
        编码清理后的消息列表
    """
    if not isinstance(messages, list):
        return messages
    
    cleaned_messages = []
    for message in messages:
        cleaned_messages.append(clean_message_content(message))
    
    return cleaned_messages


def safe_json_dumps(data: Any, **kwargs) -> str:
    """
    安全的JSON序列化，处理编码问题
    
    Args:
        data: 要序列化的数据
        **kwargs: json.dumps的其他参数
        
    Returns:
        JSON字符串
    """
    try:
        # 设置默认参数
        kwargs.setdefault('ensure_ascii', False)
        kwargs.setdefault('separators', (',', ':'))
        
        # 先清理数据中的编码问题
        if isinstance(data, dict):
            cleaned_data = clean_dict_encoding(data)
        elif isinstance(data, list):
            cleaned_data = clean_list_encoding(data)
        else:
            cleaned_data = data
        
        # 尝试序列化
        json_str = json.dumps(cleaned_data, **kwargs)
        return safe_encode_string(json_str)
        
    except Exception as e:
        logger.error(f"JSON序列化失败: {e}")
        # 备选方案：使用ensure_ascii=True
        try:
            kwargs['ensure_ascii'] = True
            if isinstance(data, dict):
                cleaned_data = clean_dict_encoding(data)
            elif isinstance(data, list):
                cleaned_data = clean_list_encoding(data)
            else:
                cleaned_data = data
            return json.dumps(cleaned_data, **kwargs)
        except Exception as e2:
            logger.error(f"备选JSON序列化也失败: {e2}")
            return json.dumps({"error": "数据包含无法序列化的内容"}, ensure_ascii=True)


def create_safe_sse_event(event_type: str, data: Dict[str, Any]) -> str:
    """
    创建安全的Server-Sent Event字符串
    
    Args:
        event_type: 事件类型
        data: 事件数据
        
    Returns:
        SSE事件字符串
    """
    try:
        # 清理数据
        cleaned_data = clean_dict_encoding(data)
        
        # 序列化数据
        json_data = safe_json_dumps(cleaned_data)
        
        # 构建SSE事件
        return f"event: {event_type}\ndata: {json_data}\n\n"
        
    except Exception as e:
        logger.error(f"创建SSE事件失败: {e}")
        # 返回错误事件
        error_data = {
            "error": "编码错误",
            "message": "数据包含无法处理的字符"
        }
        return f"event: error\ndata: {json.dumps(error_data, ensure_ascii=True)}\n\n"


def init_encoding_environment():
    """
    初始化编码环境，设置必要的环境变量
    """
    import os
    import sys
    import locale
    
    # 设置编码环境
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    
    if sys.platform.startswith('win'):
        # Windows 系统特殊处理
        try:
            locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
        except locale.Error:
            try:
                locale.setlocale(locale.LC_ALL, 'C.UTF-8')
            except locale.Error:
                logger.warning("无法设置UTF-8 locale，使用默认设置")
    
    logger.info("编码环境已初始化") 