import json
import logging
import requests
from typing import Optional
logger = logging.getLogger(__name__)
class DocChainClient:
    r"""客户端，用于与 DocChain API 进行交互。"""
    DOCCHAIN_BASE_URL = None
    DOCCHAIN_USERNAME = None
    DOCCHAIN_PASSWORD = None
    DOCCHAIN_API_KEY = None
    def __init__(self):
        try:
            self.__class__.DOCCHAIN_BASE_URL = "https://lab.iwhalecloud.com/docchain"
            self.__class__.DOCCHAIN_USERNAME = "admin"
            self.__class__.DOCCHAIN_PASSWORD = "abc@123A"
            self.__class__.DOCCHAIN_API_KEY = "lhb1MOiZJCGuiK3TpHFje0JGz4FDg4HyoU_tw2OGOcg"
            logger.info("DocChainClient 配置加载成功。")
        except AttributeError as e:
            logger.error(
                f"DocChainClient 缺少必要的配置项: {e}. 请检查 config 中 docchain 的设置。"
            )
            self.__class__.DOCCHAIN_BASE_URL = None  # 确保后续检查失败
        self.session = requests.Session()
        # X-Api-Key will be set in get_source_id for dynamic values
        self.session.headers.update({"Content-Type": "application/json"})
        self.authenticated = False
        if all(
            [self.DOCCHAIN_BASE_URL, self.DOCCHAIN_USERNAME, self.DOCCHAIN_PASSWORD, self.DOCCHAIN_API_KEY]
        ):
            self.authenticated = True
        else:
            logger.warning("DocChain 配置不完整或加载失败，无法进行认证。")
    def get_source_id(self, doc_id: str, docchain_base_url: Optional[str] = None, docchain_api_key: Optional[str] = None) -> Optional[str]:
        r"""调用 DocChain API 获取 source_id (文档库 ID)。"""
        effective_base_url = docchain_base_url if docchain_base_url else self.DOCCHAIN_BASE_URL
        effective_api_key = docchain_api_key if docchain_api_key else self.DOCCHAIN_API_KEY

        if not effective_base_url or not effective_api_key:
            logger.error("DocChain Base URL 或 API Key 未配置，无法获取 source_id。")
            return None

        # Dynamically set X-Api-Key for this request
        self.session.headers.update({"X-Api-Key": effective_api_key})

        if not doc_id or not doc_id.isdigit():
            logger.warning(f"无效的 DocChain doc_id 提供给 get_source_id: {doc_id}")
            return None

        meta_url = f"{effective_base_url}/v1/doc_ext/meta_data?doc_id={doc_id}"
        try:
            response = self.session.get(meta_url, timeout=10)
            response.raise_for_status()
            result = response.json()
            if result.get("success"):
                data_payload = result.get("data", {})
                meta_data_str = (
                    data_payload.get("meta_data")
                    if isinstance(data_payload, dict)
                    else None
                )
                if meta_data_str:
                    try:
                        meta_data = json.loads(meta_data_str)
                        source_id = meta_data.get("source_id")
                        if source_id is not None:
                            logger.debug(
                                f"从 DocChain 获取 source_id 成功: {source_id} (doc_id: {doc_id})"
                            )
                            return str(source_id)
                        else:
                            logger.warning(
                                f"DocChain meta_data 中未找到 source_id: {meta_data_str} (doc_id: {doc_id})"
                            )
                    except json.JSONDecodeError as json_err:
                        logger.exception(
                            f"解析 DocChain meta_data JSON 失败 (doc_id: {doc_id}): {json_err}, 内容: {meta_data_str}"
                        )
            else:
                error_msg = result.get("err") or result.get("error_code", "未知错误")
                logger.error(f"DocChain API 调用失败 (doc_id: {doc_id}): {error_msg}")
            return None
        except requests.exceptions.Timeout:
            logger.error(f"DocChain API 请求超时 (doc_id: {doc_id}), URL: {meta_url}")
            return None
        except requests.exceptions.RequestException as e:
            status_code = e.response.status_code if e.response is not None else "N/A"
            if status_code != 404:
                logger.exception(
                    f"DocChain API 请求失败 (doc_id: {doc_id}), Status: {status_code}, URL: {meta_url}: {e}"
                )
            else:
                logger.warning(
                    f"DocChain API 未找到 meta_data (doc_id: {doc_id}): 404 Not Found, URL: {meta_url}"
                )
            return None
        except json.JSONDecodeError as e:
            response_text = (
                response.text
                if "response" in locals() and hasattr(response, "text")
                else "N/A"
            )
            logger.exception(
                f"解析 DocChain API 响应失败 (doc_id: {doc_id}): {e}, URL: {meta_url}, Response: {response_text}"
            )
            return None