"""
LangChain和OpenAI补丁模块

修复以下问题：
1. langchain_openai包中_create_usage_metadata函数的token计算问题
2. OpenAI streaming模块中choice.delta.to_dict()可能失败的问题

@author: 0027013824
"""

import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


def _patched_create_usage_metadata(oai_token_usage: Dict[str, Any]):
    """
    修复后的token usage元数据创建函数。
    
    解决原始函数中当input_tokens和output_tokens都为None时
    None + None导致TypeError的问题。
    
    Args:
        oai_token_usage: OpenAI响应中的token使用信息
        
    Returns:
        UsageMetadata对象: 处理后的usage元数据
    """
    try:
        # 导入必要的类型，避免在模块级别导入可能导致的循环依赖
        from langchain_core.messages.ai import UsageMetadata, InputTokenDetails, OutputTokenDetails
        
        input_tokens = oai_token_usage.get("prompt_tokens")
        output_tokens = oai_token_usage.get("completion_tokens")
        
        # 修复核心问题：确保token值不为None时才进行计算
        if input_tokens is not None and output_tokens is not None:
            total_tokens = oai_token_usage.get("total_tokens", input_tokens + output_tokens)
        else:
            # 如果任一值为None，使用原始total_tokens或默认为0
            total_tokens = oai_token_usage.get("total_tokens")
            if total_tokens is None:
                total_tokens = 0
            # 为None的值设置默认值0
            input_tokens = input_tokens if input_tokens is not None else 0
            output_tokens = output_tokens if output_tokens is not None else 0
        
        # 构建input_token_details
        input_token_details_raw: dict = {
            "audio": (oai_token_usage.get("prompt_tokens_details") or {}).get(
                "audio_tokens"
            ),
            "cache_read": (oai_token_usage.get("prompt_tokens_details") or {}).get(
                "cached_tokens"
            ),
        }
        
        # 构建output_token_details
        output_token_details_raw: dict = {
            "audio": (oai_token_usage.get("completion_tokens_details") or {}).get(
                "audio_tokens"
            ),
            "reasoning": (oai_token_usage.get("completion_tokens_details") or {}).get(
                "reasoning_tokens"
            ),
        }
        
        # 创建并返回UsageMetadata对象
        return UsageMetadata(
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            total_tokens=total_tokens,
            input_token_details=InputTokenDetails(
                **{k: v for k, v in input_token_details_raw.items() if v is not None}
            ),
            output_token_details=OutputTokenDetails(
                **{k: v for k, v in output_token_details_raw.items() if v is not None}
            ),
        )
        
    except Exception as e:
        logger.warning(f"处理token usage元数据时发生错误: {e}")
        # 返回安全的默认UsageMetadata对象
        try:
            from langchain_core.messages.ai import UsageMetadata, InputTokenDetails, OutputTokenDetails
            return UsageMetadata(
                input_tokens=0,
                output_tokens=0,
                total_tokens=0,
                input_token_details=InputTokenDetails(),
                output_token_details=OutputTokenDetails(),
            )
        except Exception:
            # 如果连默认对象都无法创建，返回最基本的字典结构
            logger.error("无法创建UsageMetadata对象，返回基本字典结构")
            return {
                "input_tokens": 0,
                "output_tokens": 0,
                "total_tokens": 0,
            }


def _safe_delta_to_dict(delta_obj):
    """
    安全地将delta对象转换为字典。
    
    修复当大模型响应异常时，choice.delta.to_dict()可能失败的问题。
    
    Args:
        delta_obj: 需要转换的delta对象
        
    Returns:
        dict: 转换后的字典，失败时返回空字典
    """
    try:
        if delta_obj is None:
            return {}
        
        # 尝试调用原始的to_dict方法
        if hasattr(delta_obj, 'to_dict'):
            return delta_obj.to_dict()
        
        # 如果没有to_dict方法，尝试使用model_dump
        if hasattr(delta_obj, 'model_dump'):
            return delta_obj.model_dump()
        
        # 如果是字典类型，直接返回
        if isinstance(delta_obj, dict):
            return delta_obj
        
        # 其他情况尝试转换为字典
        if hasattr(delta_obj, '__dict__'):
            return delta_obj.__dict__
        
        logger.warning(f"无法将delta对象转换为字典，对象类型: {type(delta_obj)}")
        return {}
        
    except Exception as e:
        logger.warning(f"delta.to_dict()调用失败: {e}")
        # 返回一个基本的空字典结构，确保后续处理不会崩溃
        return {}


def _patched_accumulate_chunk(self, chunk):
    """
    修复后的chunk累积函数。
    
    包装原始的_accumulate_chunk方法，添加对choice.delta.to_dict()失败的处理。
    
    Args:
        chunk: 需要累积的chunk对象
        
    Returns:
        ParsedChatCompletionSnapshot: 累积后的完成快照
    """
    try:
        # 调用原始的_accumulate_chunk方法
        return self._original_accumulate_chunk(chunk)
        
    except Exception as e:
        # 如果原始方法失败，尝试使用安全的方式处理
        logger.warning(f"原始_accumulate_chunk方法失败: {e}")
        
        try:
            # 获取当前完成快照
            completion_snapshot = self._ChatCompletionStreamState__current_completion_snapshot
            
            if completion_snapshot is None:
                # 如果没有当前快照，创建一个基本快照
                return self._convert_initial_chunk_into_snapshot_safe(chunk)
            
            # 安全地处理chunk中的choices
            for choice in chunk.choices:
                try:
                    # 确保choices列表足够长
                    while len(completion_snapshot.choices) <= choice.index:
                        completion_snapshot.choices.append(None)
                    
                    # 安全地获取delta字典
                    delta_dict = _safe_delta_to_dict(choice.delta)
                    
                    # 如果delta为空，跳过这个choice的处理
                    if not delta_dict:
                        continue
                    
                    # 更新choice的finish_reason
                    if choice.finish_reason and completion_snapshot.choices[choice.index]:
                        completion_snapshot.choices[choice.index].finish_reason = choice.finish_reason
                    
                except (IndexError, AttributeError) as choice_error:
                    logger.warning(f"处理choice时发生错误: {choice_error}")
                    continue
            
            return completion_snapshot
            
        except Exception as fallback_error:
            logger.error(f"fallback处理也失败: {fallback_error}")
            # 返回一个最基本的快照对象
            return self._create_fallback_snapshot(chunk)


def _convert_initial_chunk_into_snapshot_safe(self, chunk):
    """
    安全地将初始chunk转换为快照。
    """
    try:
        # 尝试调用原始方法
        if hasattr(self, '_convert_initial_chunk_into_snapshot'):
            return self._convert_initial_chunk_into_snapshot(chunk)
        
        # 如果原始方法不存在，创建基本快照
        return self._create_fallback_snapshot(chunk)
        
    except Exception as e:
        logger.warning(f"转换初始chunk为快照失败: {e}")
        return self._create_fallback_snapshot(chunk)


def _create_fallback_snapshot(self, chunk):
    """
    创建一个基本的fallback快照对象。
    """
    try:
        # 导入必要的类型
        from openai.lib.streaming.chat._types import ParsedChatCompletionSnapshot
        
        # 创建一个基本的快照结构
        fallback_data = {
            "id": getattr(chunk, 'id', 'unknown'),
            "object": "chat.completion",
            "created": getattr(chunk, 'created', 0),
            "model": getattr(chunk, 'model', 'unknown'),
            "choices": [],
            "usage": None,
            "system_fingerprint": None,
        }
        
        # 安全地添加choices
        for choice in getattr(chunk, 'choices', []):
            choice_data = {
                "index": getattr(choice, 'index', 0),
                "message": _safe_delta_to_dict(getattr(choice, 'delta', None)),
                "finish_reason": None,
                "logprobs": None,
            }
            fallback_data["choices"].append(choice_data)
        
        return fallback_data
        
    except Exception as e:
        logger.error(f"创建fallback快照失败: {e}")
        # 返回最基本的结构
        return {
            "id": "fallback",
            "object": "chat.completion", 
            "created": 0,
            "model": "unknown",
            "choices": [],
            "usage": None,
            "system_fingerprint": None,
        }


def apply_langchain_openai_patches():
    """
    应用LangChain OpenAI相关的补丁。
    
    该函数会替换langchain_openai.chat_models.base模块中的
    _create_usage_metadata函数，修复token计算的TypeError问题。
    """
    try:
        import langchain_openai.chat_models.base as openai_base
        
        # 保存原始函数的引用（用于调试或回滚）
        if not hasattr(openai_base, '_original_create_usage_metadata'):
            openai_base._original_create_usage_metadata = openai_base._create_usage_metadata
        
        # 替换为修复后的函数
        openai_base._create_usage_metadata = _patched_create_usage_metadata
        
        logger.info("成功应用LangChain OpenAI token usage补丁")
        
    except ImportError:
        logger.warning("未找到langchain_openai模块，跳过补丁应用")
    except Exception as e:
        logger.error(f"应用LangChain补丁失败: {e}")


def apply_openai_streaming_patches():
    """
    应用OpenAI streaming相关的补丁。
    
    该函数会替换openai.lib.streaming.chat._completions模块中的
    相关方法，修复choice.delta.to_dict()可能失败的问题。
    """
    try:
        import openai.lib.streaming.chat._completions as streaming_completions
        
        # 检查是否存在ChatCompletionStreamState类
        if hasattr(streaming_completions, 'ChatCompletionStreamState'):
            stream_state_class = streaming_completions.ChatCompletionStreamState
            
            # 保存原始方法的引用
            if not hasattr(stream_state_class, '_original_accumulate_chunk'):
                stream_state_class._original_accumulate_chunk = stream_state_class._accumulate_chunk
            
            # 添加安全方法到类中
            stream_state_class._convert_initial_chunk_into_snapshot_safe = _convert_initial_chunk_into_snapshot_safe
            stream_state_class._create_fallback_snapshot = _create_fallback_snapshot
            
            # 替换为修复后的方法
            stream_state_class._accumulate_chunk = _patched_accumulate_chunk
            
            logger.info("成功应用OpenAI streaming补丁")
        else:
            logger.warning("未找到ChatCompletionStreamState类，跳过streaming补丁应用")
        
    except ImportError:
        logger.warning("未找到openai.lib.streaming.chat._completions模块，跳过streaming补丁应用")
    except Exception as e:
        logger.error(f"应用OpenAI streaming补丁失败: {e}")


def apply_all_patches():
    """
    应用所有相关补丁。
    """
    logger.info("开始应用所有补丁...")
    apply_langchain_openai_patches()
    apply_openai_streaming_patches()
    logger.info("所有补丁应用完成") 