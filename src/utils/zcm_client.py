import json
import logging
from typing import Optional, Tuple

import requests


logger = logging.getLogger(__name__)


class ZCMClient:
    r"""
    客户端，用于与 ZCM API 进行交互。
    """

    ZCM_BASE_URL = None
    # TODO: 将 ZCM Token 移至配置或更安全的管理方式
    ZCM_AUTH_TOKEN = (
        "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9."
        + "eyJwdXJwb3NlIjoiZXh0ZXJuYWwiLCJwcm9kdWN0IjoiWk1RLUFJXHU2NjdhXHU4MGZkXHU1MmE5XHU2MjRiIiwiYXB"
        + "pcyI6WyIvcG9ydGFsL3pjbS1jbWRiL3JlbW90ZS9leGVjIiwiL3BvcnRhbC96Y20tY21kYi9yZW1vdGUvZXhlYy9iYX"
        + "RjaCIsIi9wb3J0YWwvemNtLWNtZGIvcmVtb3RlL2V4ZWMvYmF0Y2gyIiwiL3BvcnRhbC9aTVFNYW5hZ2VyL21vbml0b"
        + "3IvY29uc3VtZXJTdGF0ZSIsIi9wb3J0YWwvWk1RTWFuYWdlci9tb25pdG9yL2NvbnN1bWVyRGlmZiIsIi9wb3J0YWwv"
        + "Wk1RTWFuYWdlci9tb25pdG9yL3FyeVJhdGUiLCIvcG9ydGFsL1pNUU1hbmFnZXIvbW9uaXRvci9xcnlDb25zdW1lclR"
        + "pbWUiLCIvcG9ydGFsL1pNUU1hbmFnZXIvbW9uaXRvci9xcnlDb25zdW1lckZhaWwiLCIvcG9ydGFsL1pNUU1hbmFnZX"
        + "IvbW9uaXRvci9xcnlDbHVzdGVySW5mbyIsIi9wb3J0YWwvWk1RTWFuYWdlci9sbG0vcHJvbXB0IiwiL3BvcnRhbC9aT"
        + "VFNYW5hZ2VyL2xsbS90b2tlbiIsIi9wb3J0YWwvemNtLWNybXMvY2xvdWQtc2VydmVycy8qL2luZm8iLCIvcG9ydGFs"
        + "L3pjbS1kb2MvZG9jdW1lbnQvZnVsbFBhdGgvaW5mby8qIiwiL3BvcnRhbC96Y20tZG9jL2RvY3VtZW50IiwiL3BvcnR"
        + "hbC96Y20tY3Jtcy9jbG91ZC1zZXJ2ZXJzLyovdXNlcnMiLCIvcG9ydGFsL3pjbS10b29sL3NlcnZlcnMvYWNjb3VudH"
        + "MvZGV0YWlsIl0sImV4cCI6MjA0MTQ4OTUxNH0.Cqa4iRqdD1nN3BU5k3-6LtSnLXpd-XO3yIMABaKdeuIu5rE-vtnF7"
        + "L_7i71nTpFhm2uH8dIQKBSGyizNutYdDA"
    )

    def __init__(self):
        try:
            self.__class__.ZCM_BASE_URL = "https://dev.iwhalecloud.com/portal/zcm-doc"
            logger.info("ZCMClient 配置加载成功。")
            # 可以在这里添加从配置加载 ZCM Token 的逻辑
            # self.__class__.ZCM_AUTH_TOKEN = settings.zcm.auth_token
        except AttributeError as e:
            logger.error(
                f"ZCMClient 缺少必要的配置项: {e}. 请检查 config 中 zcm 的设置。"
            )
            self.__class__.ZCM_BASE_URL = None

    def get_doc_fullpath(self, source_id: str) -> Optional[Tuple[str, str]]:
        r"""
        调用 ZCM API 获取 bid 和 docFullPath。
        """
        if not self.ZCM_BASE_URL:
            logger.error("ZCM Base URL 未配置，无法获取文档路径。")
            return None
        if not source_id or not source_id.isdigit():
            logger.warning(f"无效的 ZCM source_id 提供给 get_doc_fullpath: {source_id}")
            return None

        zcm_url = f"{self.ZCM_BASE_URL}/document/fullPath/info/{source_id}"
        zcm_headers = {"Authorization": self.ZCM_AUTH_TOKEN}

        try:
            response = requests.get(zcm_url, headers=zcm_headers, timeout=15)
            response.raise_for_status()
            result = response.json()

            if isinstance(result, dict) and "bid" in result and "docFullPath" in result:
                bid = result.get("bid")
                doc_fullpath = result.get("docFullPath")
                if bid is not None and doc_fullpath is not None:
                    logger.debug(
                        f"从 ZCM 获取路径成功: bid={bid}, fullpath={doc_fullpath} (source_id: {source_id})"
                    )
                    return str(bid), str(doc_fullpath)
                else:
                    logger.warning(
                        f"ZCM 响应中 bid 或 docFullPath 为空: {result} (source_id: {source_id})"
                    )
            else:
                logger.warning(
                    f"ZCM API 响应格式不符合预期或缺少字段: {result} (source_id: {source_id}), URL: {zcm_url}"
                )
            return None
        except requests.exceptions.Timeout:
            logger.error(f"ZCM API 请求超时 (source_id: {source_id}), URL: {zcm_url}")
            return None
        except requests.exceptions.RequestException as e:
            status_code = e.response.status_code if e.response is not None else "N/A"
            if status_code == 404:
                logger.warning(
                    f"ZCM API 未找到文档 (source_id: {source_id}): 404 Not Found, URL: {zcm_url}"
                )
            else:
                logger.exception(
                    f"ZCM API 请求失败 (source_id: {source_id}), Status: {status_code}, URL: {zcm_url}: {e}"
                )
            return None
        except json.JSONDecodeError as e:
            response_text = (
                response.text
                if "response" in locals() and hasattr(response, "text")
                else "N/A"
            )
            logger.exception(
                f"解析 ZCM API 响应失败 (source_id: {source_id}): {e}, URL: {zcm_url}, Response: {response_text}"
            )
            return None
