# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import logging
import os
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, Optional
from langchain_openai import ChatOpenAI
from src.config import load_yaml_config
from src.config.agents import LLMType

logger = logging.getLogger(__name__)


@dataclass
class LLMConfig:
    """LLM配置类，统一管理所有LLM相关配置"""
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    model: Optional[str] = None
    temperature: float = 0.7
    max_tokens: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {}
        for key, value in self.__dict__.items():
            if value is not None:
                result[key] = value
        return result

    def validate(self) -> None:
        """验证配置有效性"""
        if not self.api_key:
            raise ValueError("API Key 不能为空。请在前端设置页面中配置有效的API Key。")
        
        if not self.base_url:
            raise ValueError("Base URL 不能为空。请在前端设置页面中配置有效的Base URL。")


class LLMManager:
    """LLM管理器，统一处理LLM的创建、配置和缓存"""
    
    def __init__(self):
        self._cache: Dict[str, ChatOpenAI] = {}
        self._config_cache: Dict[str, Dict[str, Any]] = {}
        self._default_config = self._load_default_config()
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        try:
            config_path = Path(__file__).parent.parent.parent / "conf.yaml"
            return load_yaml_config(str(config_path.resolve()))
        except Exception as e:
            logger.warning(f"无法加载默认配置: {e}")
            return {}
    
    def _get_env_config(self, llm_type: str) -> Dict[str, Any]:
        """从环境变量获取LLM配置"""
        prefix = f"{llm_type.upper()}_MODEL__"
        config = {}
        for key, value in os.environ.items():
            if key.startswith(prefix):
                config_key = key[len(prefix):].lower()
                config[config_key] = value
        return config
    
    def _merge_configs(self, llm_type: str, user_config: LLMConfig) -> Dict[str, Any]:
        """合并多个配置源的配置"""
        # 1. 默认配置
        type_map = {
            "reasoning": "REASONING_MODEL",
            "basic": "BASIC_MODEL", 
            "vision": "VISION_MODEL",
        }
        
        default_llm_config = {}
        if llm_type in type_map:
            default_llm_config = self._default_config.get(type_map[llm_type], {})
        
        # 2. 环境变量配置
        env_config = self._get_env_config(llm_type)
        
        # 3. 用户传入配置
        user_dict = user_config.to_dict()
        
        # 合并配置（优先级：用户配置 > 环境变量 > 默认配置）
        merged = {**default_llm_config, **env_config, **user_dict}
        
        # 添加默认请求头以处理编码问题
        if "default_headers" not in merged:
            merged["default_headers"] = {}
        
        merged["default_headers"].update({
            "Content-Type": "application/json; charset=utf-8",
            "Accept": "application/json",
            "Accept-Charset": "utf-8"
        })
        
        # 添加请求超时配置
        if "timeout" not in merged:
            merged["timeout"] = 60
            
        return merged
    
    def _create_cache_key(self, llm_type: str, config: LLMConfig) -> str:
        """创建缓存键"""
        key_parts = [
            llm_type,
            config.api_key or "default",
            config.base_url or "default",
            config.model or "default"
        ]
        return "|".join(key_parts)
    
    def _handle_llm_errors(self, error: Exception, llm_type: str) -> ValueError:
        """统一处理LLM相关错误"""
        error_msg = str(error).lower()
        
        if "api_key" in error_msg or "authentication" in error_msg:
            return ValueError("API Key验证失败，请检查您在设置中配置的API Key是否正确。")
        elif "base_url" in error_msg or "connection" in error_msg or "timeout" in error_msg:
            return ValueError("无法连接到API服务，请检查您在设置中配置的Base URL是否正确，或检查网络连接。")
        elif "quota" in error_msg or "limit" in error_msg:
            return ValueError("API配额已用完或达到限制，请检查您的账户余额或等待限制重置。")
        elif "model" in error_msg:
            return ValueError(f"指定的模型不可用，请检查模型名称是否正确。")
        else:
            return ValueError(f"调用{llm_type} LLM服务时发生错误：{str(error)}")
    
    def get_llm(self, llm_type: LLMType, config: LLMConfig) -> ChatOpenAI:
        """获取LLM实例"""
        try:
            # 验证配置
            config.validate()
            
            # 创建缓存键
            cache_key = self._create_cache_key(llm_type, config)
            
            # 检查缓存
            if cache_key in self._cache:
                return self._cache[cache_key]
            
            # 合并配置
            merged_config = self._merge_configs(llm_type, config)
            
            # 创建LLM实例
            llm = ChatOpenAI(**merged_config)
            
            # 缓存实例（仅当使用非敏感信息作为键时）
            if not config.api_key or len(config.api_key) > 10:  # 只缓存较长的API key
                self._cache[cache_key] = llm
            
            logger.info(f"创建了新的{llm_type} LLM实例")
            return llm
            
        except Exception as e:
            raise self._handle_llm_errors(e, llm_type)
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self._cache.clear()
        logger.info("LLM缓存已清空")
    
    def get_cache_info(self) -> Dict[str, int]:
        """获取缓存信息"""
        return {
            "cached_instances": len(self._cache),
            "cache_keys": list(self._cache.keys())
        }


# 全局LLM管理器实例
_llm_manager = LLMManager()


def create_llm_config(
    api_key: Optional[str] = None,
    base_url: Optional[str] = None,
    model: Optional[str] = None,
    **kwargs
) -> LLMConfig:
    """创建LLM配置的便捷函数"""
    return LLMConfig(
        api_key=api_key,
        base_url=base_url,
        model=model,
        **kwargs
    )


def get_llm_by_type_v2(
    llm_type: LLMType,
    api_key: Optional[str] = None,
    base_url: Optional[str] = None,
    model: Optional[str] = None,
    **kwargs
) -> ChatOpenAI:
    """
    获取LLM实例的新版本函数，提供更好的配置管理和错误处理
    """
    config = create_llm_config(
        api_key=api_key,
        base_url=base_url,
        model=model,
        **kwargs
    )
    return _llm_manager.get_llm(llm_type, config)


def clear_llm_cache() -> None:
    """清空LLM缓存"""
    _llm_manager.clear_cache()


def get_llm_cache_info() -> Dict[str, int]:
    """获取LLM缓存信息"""
    return _llm_manager.get_cache_info() 