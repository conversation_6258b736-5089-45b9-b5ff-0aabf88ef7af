# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from pathlib import Path
from typing import Any, Dict
import os
import logging

from langchain_openai import ChatOpenAI

from src.config import load_yaml_config
from src.config.agents import LLMType

logger = logging.getLogger(__name__)

# Cache for LLM instances
_llm_cache: dict[LLMType, ChatOpenAI] = {}


def _get_env_llm_conf(llm_type: str) -> Dict[str, Any]:
    """
    Get LLM configuration from environment variables.
    Environment variables should follow the format: {LLM_TYPE}__{KEY}
    e.g., BASIC_MODEL__api_key, BASIC_MODEL__base_url
    """
    prefix = f"{llm_type.upper()}_MODEL__"
    conf = {}
    for key, value in os.environ.items():
        if key.startswith(prefix):
            conf_key = key[len(prefix) :].lower()
            conf[conf_key] = value
    return conf


def _handle_llm_error(error: Exception, llm_type: str) -> ValueError:
    """统一处理LLM相关错误"""
    error_msg = str(error).lower()
    
    if "api_key" in error_msg or "authentication" in error_msg or "unauthorized" in error_msg:
        return ValueError("API Key验证失败，请检查您在设置中配置的API Key是否正确。")
    elif "base_url" in error_msg or "connection" in error_msg or "timeout" in error_msg or "connect" in error_msg:
        return ValueError("无法连接到API服务，请检查您在设置中配置的Base URL是否正确，或检查网络连接。")
    elif "quota" in error_msg or "limit" in error_msg or "rate" in error_msg:
        return ValueError("API配额已用完或达到限制，请检查您的账户余额或等待限制重置。")
    elif "model" in error_msg and "not" in error_msg:
        return ValueError(f"指定的模型不可用，请检查模型名称是否正确。")
    else:
        return ValueError(f"调用{llm_type} LLM服务时发生错误：{str(error)}")


def _create_llm_use_conf(
    llm_type: LLMType,
    conf: Dict[str, Any],
    api_key: str = None,
    base_url: str = None,
    model: str = None,
) -> ChatOpenAI:
    """创建LLM实例"""
    try:
        llm_type_map = {
            "reasoning": conf.get("REASONING_MODEL", {}),
            "basic": conf.get("BASIC_MODEL", {}),
            "vision": conf.get("VISION_MODEL", {}),
        }
        llm_conf = llm_type_map.get(llm_type)
        if not isinstance(llm_conf, dict):
            raise ValueError(f"Invalid LLM Conf: {llm_type}")
        
        # Get configuration from environment variables
        env_conf = _get_env_llm_conf(llm_type)

        # Merge configurations, with environment variables taking precedence
        merged_conf = {**llm_conf, **env_conf}

        # 用户传入的参数优先级最高
        if api_key:
            merged_conf["api_key"] = api_key
        if base_url:
            merged_conf["base_url"] = base_url
        if model:
            merged_conf["model"] = model

        # 验证必要参数
        if not merged_conf.get("api_key"):
            if api_key is not None:  # 用户尝试传递了api_key但为空
                raise ValueError("API Key 不能为空。请在前端设置页面中配置有效的API Key。")
            else:  # 没有从任何地方获取到api_key
                raise ValueError(f"未找到 {llm_type} 类型LLM的API Key。请在前端设置页面中配置API Key，或通过环境变量设置。")

        return ChatOpenAI(**merged_conf)
        
    except Exception as e:
        # 如果是我们已经处理过的错误，直接抛出
        if isinstance(e, ValueError) and ("API Key" in str(e) or "LLM" in str(e)):
            raise e
        # 否则使用统一的错误处理
        raise _handle_llm_error(e, llm_type)


def get_llm_by_type(
    llm_type: LLMType,
    api_key: str = None,
    base_url: str = None,
    model: str = None,
) -> ChatOpenAI:
    """
    Get LLM instance by type. Returns cached instance if available.
    If an api_key is provided, it will bypass the cache and create a new instance.
    
    注意: 此函数为向后兼容保留，建议使用新的 get_llm_by_type_v2 函数
    """
    try:
        # 如果提供了api_key，则不使用缓存，直接创建新实例
        if api_key or base_url or model:
            conf = load_yaml_config(
                str((Path(__file__).parent.parent.parent / "conf.yaml").resolve())
            )
            return _create_llm_use_conf(llm_type, conf, api_key, base_url, model)
        
        # 使用缓存
        if llm_type in _llm_cache:
            return _llm_cache[llm_type]

        conf = load_yaml_config(
            str((Path(__file__).parent.parent.parent / "conf.yaml").resolve())
        )
        llm = _create_llm_use_conf(llm_type, conf, api_key, base_url, model)
        
        # 只有在没有提供用户特定配置时才缓存
        if not (api_key or base_url or model):
            _llm_cache[llm_type] = llm
            
        return llm
        
    except Exception as e:
        logger.error(f"Error creating LLM instance for type {llm_type}: {str(e)}")
        raise


def clear_cache():
    """清空LLM缓存"""
    global _llm_cache
    _llm_cache.clear()
    logger.info("LLM缓存已清空")


# In the future, we will use reasoning_llm and vl_llm for different purposes
# reasoning_llm = get_llm_by_type("reasoning")
# vl_llm = get_llm_by_type("vision")


if __name__ == "__main__":
    # Initialize LLMs for different purposes - now these will be cached
    basic_llm = get_llm_by_type("basic")
    print(basic_llm.invoke("Hello"))
