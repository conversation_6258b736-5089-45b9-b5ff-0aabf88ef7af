# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import os
from dataclasses import dataclass, field, fields
from typing import Any, Dict, Optional

from langchain_core.runnables import RunnableConfig

from src.rag.retriever import Resource


@dataclass(kw_only=True)
class Configuration:
    """The configurable fields."""

    api_key: str = ""  # API key for the LLM
    base_url: str = ""  # Base URL for the LLM
    model: str = ""  # LLM model to use
    resources: list[Resource] = field(
        default_factory=list
    )  # Resources to be used for the research
    max_plan_iterations: int = 1  # Maximum number of plan iterations
    max_step_num: int = 3  # Maximum number of steps in a plan
    max_search_results: int = 3  # Maximum number of search results
    mcp_settings: dict = None  # MCP settings, including dynamic loaded tools
    # DocChain configuration (from config file)
    docchain_base_url: str = ""  # DocChain service base URL
    docchain_api_key: str = ""  # DocChain service API key
    # Project configuration
    project: str = ""  # Current project identifier
    topics: Optional[Dict] = None  # Frontend topic configurations {project: {codeTopicId, docTopicId}}
    
    # 新增场景配置字段
    scenario_config: Optional[Dict] = None  # 场景配置，从前端传递或预置场景加载
    scenario_code: Optional[str] = None  # 场景代码，用于预置场景查询（如 'pd', 'sa'）
    scenario_name: Optional[str] = None  # 场景名称，用于兼容性（如 '产品设计', '系统架构'）
    
    @classmethod
    def get_project_topics(cls, project: str) -> Dict[str, str]:
        """Get code and doc topic IDs for a specific project."""
        from src.config.projects import get_project_topics
        return get_project_topics(project)

    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> "Configuration":
        """Create a Configuration instance from a RunnableConfig."""
        configurable = (
            config["configurable"] if config and "configurable" in config else {}
        )
        values: dict[str, Any] = {
            f.name: os.environ.get(f.name.upper(), configurable.get(f.name))
            for f in fields(cls)
            if f.init
        }
        return cls(**{k: v for k, v in values.items() if v})
    
    def get_scenario_config(self):
        """获取场景配置"""
        # 如果直接传递了场景配置，优先使用
        if self.scenario_config:
            # 如果是Pydantic模型对象，转换为字典
            if hasattr(self.scenario_config, 'dict'):
                return self.scenario_config.dict()
            # 如果已经是字典，直接返回
            return self.scenario_config
        
        # 优先使用场景代码查找预置场景
        scenario_identifier = (
            self.scenario_code if self.scenario_code else self.scenario_name
        )
        
        if scenario_identifier:
            try:
                from src.config.preset_scenarios import preset_scenario_manager
                preset_scenario = preset_scenario_manager.get_scenario(
                    scenario_identifier
                )
                if preset_scenario:
                    return preset_scenario
            except Exception:
                # 如果预置场景管理器不可用，尝试旧的场景管理器
                try:
                    from src.config.scenarios import scenario_manager
                    preset_scenario = scenario_manager.get_preset_scenario(
                        scenario_identifier
                    )
                    if preset_scenario:
                        return preset_scenario.dict()
                except Exception:
                    pass
        
        # 返回None表示使用默认配置
        return None
    
    def get_node_config(self, node_name: str):
        """获取特定节点的配置
        
        Args:
            node_name: 节点名称 ('planner', 'researcher', 'reporter', 'coder')
            
        Returns:
            Dict: 节点配置字典，如果没有配置则返回空字典
        """
        scenario_config = self.get_scenario_config()
        
        if scenario_config:
            # 检查是否存在nodes配置
            if isinstance(scenario_config, dict) and "nodes" in scenario_config:
                nodes_config = scenario_config["nodes"]
                if isinstance(nodes_config, dict):
                    return nodes_config.get(node_name, {})
        
        return {}
    
    def get_node_system_prompt(self, node_name: str) -> str:
        """获取节点的系统提示词
        
        Args:
            node_name: 节点名称
            
        Returns:
            str: 系统提示词，如果没有配置则返回空字符串
        """
        node_config = self.get_node_config(node_name)
        if (node_config and isinstance(node_config, dict) and
                "prompts" in node_config):
            prompts = node_config["prompts"]
            if isinstance(prompts, dict) and "system_prompt" in prompts:
                return prompts["system_prompt"]
        return ""
    
    def get_node_prompt_extension(self, node_name: str) -> str:
        """获取节点的场景扩展提示词（兼容旧配置）
        
        Args:
            node_name: 节点名称
            
        Returns:
            str: 场景扩展提示词，如果没有配置则返回空字符串
        """
        node_config = self.get_node_config(node_name)
        if (node_config and isinstance(node_config, dict) and
                "prompts" in node_config):
            prompts = node_config["prompts"]
            if isinstance(prompts, dict):
                return prompts.get("scenario_extension", "")
        return ""
    
    def get_node_tools(self, node_name: str) -> Dict[str, Any]:
        """获取节点的工具配置
        
        根据前台场景配置结构获取节点的工具配置：
        - enabled: 启用的工具列表
        - configs: 工具特定配置
        
        Args:
            node_name: 节点名称
            
        Returns:
            Dict[str, Any]: 工具配置字典，包含enabled和configs
        """
        node_config = self.get_node_config(node_name)
        if (node_config and isinstance(node_config, dict) and
                "tools" in node_config):
            tools_config = node_config["tools"]
            
            result = {
                "enabled": tools_config.get("enabled", []),
                "configs": tools_config.get("configs", {})
            }
            
            return result
        
        return {"enabled": [], "configs": {}}
    
    def get_node_enabled_tools(self, node_name: str) -> list[str]:
        """获取节点启用的工具列表
        
        Args:
            node_name: 节点名称
            
        Returns:
            list[str]: 启用的工具名称列表
        """
        tools_config = self.get_node_tools(node_name)
        return tools_config.get("enabled", [])
    
    def get_node_output_config(self, node_name: str) -> Dict[str, Any]:
        """获取节点的输出配置
        
        Args:
            node_name: 节点名称
            
        Returns:
            Dict[str, Any]: 输出配置字典
        """
        node_config = self.get_node_config(node_name)
        if (node_config and isinstance(node_config, dict) and
                "output" in node_config):
            return node_config["output"]
        return {}
    
    def get_global_config(self) -> Dict[str, Any]:
        """获取场景的全局配置
        
        Returns:
            Dict[str, Any]: 全局配置字典
        """
        scenario_config = self.get_scenario_config()
        if (scenario_config and isinstance(scenario_config, dict) and
                "global" in scenario_config):
            return scenario_config["global"]
        return {}
    
    def get_workflow_config(self) -> Dict[str, Any]:
        """获取场景的流程配置
        
        Returns:
            Dict[str, Any]: 流程配置字典
        """
        scenario_config = self.get_scenario_config()
        if (scenario_config and isinstance(scenario_config, dict) and
                "workflow" in scenario_config):
            return scenario_config["workflow"]
        return {}
    
    def get_node_config_vars(self, node_name: str) -> Dict[str, Any]:
        """获取节点的所有配置变量，用于模板替换
        
        Args:
            node_name: 节点名称
            
        Returns:
            Dict[str, Any]: 配置变量字典，包含所有节点相关的配置
        """
        config_vars = {}
        
        # 基础配置信息
        config_vars.update({
            "project": getattr(self, 'project', ''),
            "api_key": getattr(self, 'api_key', ''),
            "base_url": getattr(self, 'base_url', ''),
            "model": getattr(self, 'model', ''),
        })
        
        # 全局配置
        global_config = self.get_global_config()
        if global_config:
            config_vars.update({
                "codeTopicId": global_config.get("codeTopicId", ""),
                "docTopicId": global_config.get("docTopicId", ""),
                "debugMode": global_config.get("debugMode", False),
                "locale": global_config.get("locale", "zh-CN"),
                "timeoutMinutes": global_config.get("timeoutMinutes", 30),
            })
        
        # 如果是从topics配置获取（兼容旧方式）
        if (hasattr(self, 'topics') and self.topics and
                self.project in self.topics):
            frontend_topics = self.topics[self.project]
            config_vars.update({
                "codeTopicId": frontend_topics.get(
                    "codeTopicId", config_vars.get("codeTopicId", "")
                ),
                "docTopicId": frontend_topics.get(
                    "docTopicId", config_vars.get("docTopicId", "")
                ),
            })
        
        # 节点特定配置
        node_config = self.get_node_config(node_name)
        if node_config:
            # 添加工具配置
            tools_config = node_config.get("tools", {})
            enabled_tools = tools_config.get("enabled", [])
            config_vars.update({
                "enabledTools": (
                    "\n".join([f"- {tool}" for tool in enabled_tools])
                    if enabled_tools else "无"
                ),
                "toolConfigs": tools_config.get("configs", {}),
            })
            
            # 添加节点特定的配置参数
            if node_name == "planner":
                config_vars.update({
                    "maxIterations": node_config.get("maxIterations", 3),
                })
            elif node_name == "researcher":
                config_vars.update({
                    "maxSearchResults": node_config.get("maxSearchResults", 10),
                    "searchDepth": node_config.get("searchDepth", 3),
                })
                # 添加搜索策略配置
                search_strategy = node_config.get("searchStrategy", {})
                config_vars.update({
                    "enableParallelSearch": search_strategy.get(
                        "enableParallelSearch", True
                    ),
                    "searchQuality": search_strategy.get("searchQuality", 0.8),
                    "cacheResults": search_strategy.get("cacheResults", True),
                })
            elif node_name == "reporter":
                # 添加输出配置
                output_config = node_config.get("output", {})
                config_vars.update({
                    "outputFormat": output_config.get("format", "markdown"),
                    "includeTableOfContents": output_config.get(
                        "includeTableOfContents", False
                    ),
                    "includeMetadata": output_config.get(
                        "includeMetadata", True
                    ),
                })
                # 添加报告策略配置
                report_strategy = node_config.get("reportStrategy", {})
                config_vars.update({
                    "citationStyle": report_strategy.get(
                        "citationStyle", "custom"
                    ),
                    "maxSectionDepth": report_strategy.get(
                        "maxSectionDepth", 3
                    ),
                    "enableTableGeneration": report_strategy.get(
                        "enableTableGeneration", True
                    ),
                })
            elif node_name == "coder":
                # 添加语言配置
                languages = node_config.get("languages", {})
                supported_langs = languages.get("supportedLanguages", [])
                config_vars.update({
                    "primaryLanguage": languages.get("primary", "auto"),
                    "supportedLanguages": ", ".join(supported_langs),
                    "enableAutoDetection": languages.get(
                        "enableAutoDetection", True
                    ),
                })
        
        return config_vars
