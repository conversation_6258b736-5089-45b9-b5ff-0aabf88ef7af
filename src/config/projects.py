# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import os
from typing import Dict, Optional
from src.config.loader import load_yaml_config

def _get_project_mapping() -> Dict[str, Dict[str, str]]:
    """
    从配置文件中加载项目topic映射配置
    
    Returns:
        项目topic映射配置字典
    """
    # 获取配置文件路径
    config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'conf.yaml')
    config_path = os.path.abspath(config_path)
    
    # 加载配置
    config = load_yaml_config(config_path)
    
    # 获取项目映射配置，如果不存在则返回空字典
    return config.get('PROJECT_TOPIC_MAPPING', {})

def get_project_topics(project: Optional[str] = None) -> Dict[str, str]:
    """
    获取指定项目的topic配置
    
    Args:
        project: 项目标识符
        
    Returns:
        包含code_topic和doc_topic的字典
    """
    project_mapping = _get_project_mapping()
    
    if not project or project not in project_mapping:
        project = "default"
    
    # 如果default项目也不存在，返回空配置
    if project not in project_mapping:
        return {"code_topic": "", "doc_topic": ""}
    
    config = project_mapping[project]
    return {
        "code_topic": config.get("code_topic", ""),
        "doc_topic": config.get("doc_topic", "")
    }

def get_project_info(project: Optional[str] = None) -> Dict[str, str]:
    """
    获取指定项目的完整信息
    
    Args:
        project: 项目标识符
        
    Returns:
        包含项目完整信息的字典
    """
    project_mapping = _get_project_mapping()
    
    if not project or project not in project_mapping:
        project = "default"
    
    # 如果default项目也不存在，返回空配置
    if project not in project_mapping:
        return {"name": "", "description": "", "code_topic": "", "doc_topic": ""}
    
    return project_mapping[project]

def list_projects() -> Dict[str, Dict[str, str]]:
    """
    列出所有可用的项目配置
    
    Returns:
        所有项目的配置信息
    """
    project_mapping = _get_project_mapping()
    return {k: v for k, v in project_mapping.items() if k != "default"} 