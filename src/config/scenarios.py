# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
场景配置管理模块
负责管理预置场景和处理前端传递的场景配置
"""

import os
import json
import yaml
from typing import Dict, Optional, Any
from datetime import datetime

from src.server.chat_request import (
    ScenarioConfig,
    ScenarioNodeConfig,
    PlannerNodeConfig,
    ResearcherNodeConfig,
    ReporterNodeConfig,
    CoderNodeConfig,
    ScenarioMetadata,
    ScenarioGlobalConfig,
    ScenarioNodesConfig,
    ScenarioWorkflowConfig,
)


class ScenarioManager:
    """场景管理器，负责预置场景和配置处理"""

    def __init__(self):
        self.preset_scenarios: Dict[str, ScenarioConfig] = {}
        self._load_preset_scenarios()

    def _load_preset_scenarios(self):
        """加载预置场景配置"""
        # 预置场景配置目录
        presets_dir = os.path.join(os.path.dirname(__file__), "scenarios")

        # 如果目录不存在则创建默认预置场景
        if not os.path.exists(presets_dir):
            os.makedirs(presets_dir, exist_ok=True)
            self._create_default_preset_scenarios()
            return

        # 加载目录中的所有预置场景
        for filename in os.listdir(presets_dir):
            if filename.endswith((".yaml", ".yml", ".json")):
                filepath = os.path.join(presets_dir, filename)
                try:
                    with open(filepath, "r", encoding="utf-8") as f:
                        if filename.endswith(".json"):
                            data = json.load(f)
                        else:
                            data = yaml.safe_load(f)

                    scenario = ScenarioConfig(**data)
                    scenario.is_preset = True
                    self.preset_scenarios[scenario.name] = scenario
                except Exception as e:
                    print(f"加载预置场景失败 {filepath}: {e}")

    def _create_default_preset_scenarios(self):
        """创建默认预置场景"""
        default_scenarios = [
            self._create_requirement_analysis_scenario(),
            self._create_code_review_scenario(),
            self._create_technical_research_scenario(),
            self._create_problem_solving_scenario(),
            self._create_documentation_scenario(),
        ]

        # 保存到文件
        presets_dir = os.path.join(os.path.dirname(__file__), "scenarios")
        for scenario in default_scenarios:
            filename = f"{scenario.name.replace(' ', '_').lower()}.yaml"
            filepath = os.path.join(presets_dir, filename)

            try:
                with open(filepath, "w", encoding="utf-8") as f:
                    yaml.dump(
                        scenario.dict(), f, default_flow_style=False, allow_unicode=True
                    )

                self.preset_scenarios[scenario.name] = scenario
            except Exception as e:
                print(f"保存预置场景失败 {filepath}: {e}")

    def _create_requirement_analysis_scenario(self) -> ScenarioConfig:
        """创建需求分析场景"""
        return ScenarioConfig(
            id="preset_requirement_analysis",
            name="需求分析",
            description="分析和理解业务需求，制定技术方案",
            metadata=ScenarioMetadata(
                category="requirement_analysis",
                tags=["需求分析", "技术方案", "业务理解"],
                difficulty="medium",
                estimated_time=45,
                version="1.0.0",
                author="系统预置",
            ),
            global_config=ScenarioGlobalConfig(
                locale="zh-CN", debug_mode=False, timeout_minutes=60
            ),
            nodes=ScenarioNodesConfig(
                planner=PlannerNodeConfig(
                    max_iterations=3,
                    prompts={
                        "scenario_extension": """
                                            # 需求分析专用指导

                                            作为需求分析专家，您需要特别关注以下方面：

                                            ## 分析重点
                                            1. **需求明确性**：识别模糊或不完整的需求描述
                                            2. **业务价值**：理解需求背后的业务目标和价值
                                            3. **技术可行性**：评估技术实现的可行性和复杂度
                                            4. **依赖关系**：分析需求之间的依赖和冲突
                                            5. **验收标准**：明确需求的验收条件和测试标准

                                            ## 输出要求
                                            - 结构化的需求分析报告
                                            - 技术方案建议
                                            - 风险和挑战识别
                                            - 实施优先级建议

                                            请确保分析深入且实用，为后续技术实施提供清晰指导。
                                            """
                    },
                    tools={
                        "enabled": [
                            "search_by_docchain_llm",
                            "search_code_by_docchain",
                            "search_doc_by_docchain",
                        ]
                    },
                ),
                researcher=ResearcherNodeConfig(
                    max_search_results=8,
                    search_depth=4,
                    prompts={
                        "scenario_extension": """
# 需求分析研究重点

在进行需求分析研究时，请重点关注：

## 研究方向
1. **同类需求案例**：搜索类似需求的实现方案和经验
2. **技术文档**：查找相关的技术文档和最佳实践
3. **代码实现**：分析现有代码中的相关实现
4. **业务流程**：理解当前的业务流程和系统架构

## 输出重点
- 详细的技术调研报告
- 可行的实现方案
- 潜在风险和解决方案
- 参考案例和最佳实践
"""
                    },
                ),
            ),
            workflow=ScenarioWorkflowConfig(
                max_iterations=3, failure_strategy="retry", retry_count=2
            ),
            is_preset=True,
        )

    def _create_code_review_scenario(self) -> ScenarioConfig:
        """创建代码审查场景"""
        return ScenarioConfig(
            id="preset_code_review",
            name="代码审查",
            description="代码质量检查、安全审计、性能优化建议",
            metadata=ScenarioMetadata(
                category="code_review",
                tags=["代码审查", "质量检查", "安全审计"],
                difficulty="medium",
                estimated_time=30,
                version="1.0.0",
                author="系统预置",
            ),
            global_config=ScenarioGlobalConfig(
                locale="zh-CN",
                debug_mode=True,  # 代码审查场景开启调试模式
                timeout_minutes=45,
            ),
            nodes=ScenarioNodesConfig(
                planner=PlannerNodeConfig(
                    max_iterations=2,
                    prompts={
                        "scenario_extension": """
                            # 代码审查专用指导

                            作为代码审查专家，请重点关注：

                            ## 审查维度
                            1. **代码质量**：可读性、可维护性、复用性
                            2. **性能优化**：算法效率、资源使用、性能瓶颈
                            3. **安全漏洞**：SQL注入、XSS、权限控制等
                            4. **最佳实践**：编码规范、设计模式、架构原则
                            5. **测试覆盖**：单元测试、集成测试的完整性

                            ## 输出要求
                            - 结构化的审查报告
                            - 具体的改进建议
                            - 优先级排序的问题列表
                            - 代码示例和最佳实践建议
                            """
                    },
                    tools={
                        "enabled": [
                            "search_code_by_docchain",
                            "file_read_by_docchain",
                            # "java_method_call_chain",
                        ]
                    },
                ),
                coder=CoderNodeConfig(
                    prompts={
                        "scenario_extension": """
                            # 代码审查分析重点

                            在进行代码分析时，请特别关注：

                            ## 分析重点
                            1. **静态代码分析**：代码结构、复杂度、潜在bug
                            2. **安全性分析**：漏洞检测、安全最佳实践
                            3. **性能分析**：性能瓶颈、优化建议
                            4. **架构分析**：设计模式、架构合理性

                            ## 工具使用
                            - 优先使用java_method_call_chain分析方法调用关系
                            - 使用file_read_by_docchain进行深度代码阅读
                            - 结合search_code_by_docchain查找相关代码模式
                            """
                    },
                    languages={
                        "primary": "java",
                        "supportedLanguages": ["java", "python", "javascript"],
                    },
                ),
            ),
            is_preset=True,
        )

    def _create_technical_research_scenario(self) -> ScenarioConfig:
        """创建技术调研场景"""
        return ScenarioConfig(
            id="preset_technical_research",
            name="技术调研",
            description="技术选型、框架对比、最佳实践研究",
            metadata=ScenarioMetadata(
                category="technical_research",
                tags=["技术调研", "框架对比", "选型"],
                difficulty="complex",
                estimated_time=60,
                version="1.0.0",
                author="系统预置",
            ),
            nodes=ScenarioNodesConfig(
                researcher=ResearcherNodeConfig(
                    max_search_results=10,
                    search_depth=5,
                    prompts={
                        "scenario_extension": """
                            # 技术调研专用指导

                            作为技术调研专家，请进行全面的技术分析：

                            ## 调研框架
                            1. **技术对比**：多种技术方案的优缺点对比
                            2. **实践案例**：真实项目中的使用经验和踩坑记录
                            3. **生态系统**：社区活跃度、文档完善度、学习资源
                            4. **未来发展**：技术发展趋势和长期可维护性
                            5. **成本分析**：学习成本、开发成本、维护成本

                            ## 输出要求
                            - 详细的技术对比报告
                            - 推荐方案及理由
                            - 实施路线图
                            - 风险评估和缓解策略
                            """
                    },
                )
            ),
            is_preset=True,
        )

    def _create_problem_solving_scenario(self) -> ScenarioConfig:
        """创建问题解决场景"""
        return ScenarioConfig(
            id="preset_problem_solving",
            name="问题解决",
            description="故障排查、性能优化、bug修复",
            metadata=ScenarioMetadata(
                category="problem_solving",
                tags=["故障排查", "性能优化", "bug修复"],
                difficulty="complex",
                estimated_time=90,
                version="1.0.0",
                author="系统预置",
            ),
            nodes=ScenarioNodesConfig(
                planner=PlannerNodeConfig(
                    prompts={
                        "scenario_extension": """
                                # 问题解决专用指导

                                作为问题解决专家，请采用系统化的问题分析方法：

                                ## 问题分析流程
                                1. **问题定义**：明确问题现象、影响范围、紧急程度
                                2. **根因分析**：系统化地找出问题的根本原因
                                3. **解决方案**：提出多种可能的解决方案
                                4. **风险评估**：评估每种方案的风险和成本
                                5. **实施计划**：制定详细的实施步骤和验证方法

                                ## 分析方法
                                - 5W1H分析法
                                - 鱼骨图分析
                                - 根因分析树
                                - 故障树分析
                                """
                    }
                ),
                researcher=ResearcherNodeConfig(
                    prompts={
                        "scenario_extension": """
                                            # 问题调研重点

                                            在问题调研时，请重点收集：

                                            ## 调研内容
                                            1. **错误日志**：详细的错误信息和堆栈跟踪
                                            2. **相关代码**：问题相关的代码片段和逻辑
                                            3. **系统状态**：问题发生时的系统状态和环境
                                            4. **历史记录**：类似问题的历史解决方案
                                            5. **最佳实践**：相关的故障排查最佳实践

                                            ## 工具使用优先级
                                            1. search_by_docchain_llm - 智能问答获取精确信息
                                            2. java_method_call_chain - 分析方法调用链
                                            3. file_read_by_docchain - 深度代码分析
                                            """
                    }
                ),
            ),
            is_preset=True,
        )

    def _create_documentation_scenario(self) -> ScenarioConfig:
        """创建文档生成场景"""
        return ScenarioConfig(
            id="preset_documentation",
            name="文档生成",
            description="技术文档、API文档、使用指南生成",
            metadata=ScenarioMetadata(
                category="documentation",
                tags=["文档生成", "API文档", "技术文档"],
                difficulty="simple",
                estimated_time=20,
                version="1.0.0",
                author="系统预置",
            ),
            nodes=ScenarioNodesConfig(
                reporter=ReporterNodeConfig(
                    prompts={
                        "scenario_extension": """
                                    # 文档生成专用指导

                                    作为文档生成专家，请注重文档的结构化和实用性：

                                    ## 文档标准
                                    1. **结构清晰**：使用标准的文档结构和层次
                                    2. **内容完整**：包含所有必要的信息和示例
                                    3. **易于理解**：使用简洁明了的语言和图表
                                    4. **可操作性**：提供具体的操作步骤和代码示例
                                    5. **维护性**：考虑文档的更新和维护便利性

                                    ## 输出格式
                                    - 使用Markdown格式
                                    - 包含目录和索引
                                    - 添加代码高亮和示例
                                    - 提供清晰的图表和流程图
                                    """
                    }
                )
            ),
            is_preset=True,
        )

    def get_preset_scenario(self, scenario_name: str) -> Optional[ScenarioConfig]:
        """获取预置场景配置"""
        return self.preset_scenarios.get(scenario_name)

    def list_preset_scenarios(self) -> Dict[str, ScenarioConfig]:
        """获取所有预置场景"""
        return self.preset_scenarios.copy()

    def merge_scenario_config(
        self, base_config: ScenarioConfig, frontend_config: Optional[ScenarioConfig]
    ) -> ScenarioConfig:
        """合并场景配置，前端配置优先级更高"""
        if not frontend_config:
            return base_config

        # 深度合并配置
        merged_config = base_config.copy(deep=True)

        # 合并基础信息
        if frontend_config.name:
            merged_config.name = frontend_config.name
        if frontend_config.description:
            merged_config.description = frontend_config.description

        # 合并全局配置
        if frontend_config.global_config:
            for field, value in frontend_config.global_config.dict().items():
                if value:  # 只覆盖非空值
                    setattr(merged_config.global_config, field, value)

        # 合并节点配置
        if frontend_config.nodes:
            self._merge_node_configs(merged_config.nodes, frontend_config.nodes)

        # 合并工作流配置
        if frontend_config.workflow:
            for field, value in frontend_config.workflow.dict().items():
                if value is not None:
                    setattr(merged_config.workflow, field, value)

        # 合并变量
        if frontend_config.variables:
            merged_config.variables.update(frontend_config.variables)

        return merged_config

    def _merge_node_configs(
        self, base_nodes: ScenarioNodesConfig, frontend_nodes: ScenarioNodesConfig
    ):
        """合并节点配置"""
        for node_name in ["planner", "researcher", "reporter", "coder"]:
            base_node = getattr(base_nodes, node_name)
            frontend_node = getattr(frontend_nodes, node_name, None)

            if frontend_node:
                # 合并提示词
                if frontend_node.prompts:
                    base_node.prompts.update(frontend_node.prompts)

                # 合并工具配置
                if frontend_node.tools:
                    base_node.tools.update(frontend_node.tools)

                # 合并其他字段
                for field, value in frontend_node.dict().items():
                    if field not in ["prompts", "tools"] and value is not None:
                        setattr(base_node, field, value)


# 创建全局场景管理器实例
scenario_manager = ScenarioManager()
