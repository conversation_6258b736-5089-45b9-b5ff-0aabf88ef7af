# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
预置场景管理器
支持文件夹式的预置场景配置，每个场景一个目录，包含配置文件和提示词文件
"""

import os
import yaml
import logging
from typing import Dict, Optional, Any
from pathlib import Path

logger = logging.getLogger(__name__)


class PresetScenarioManager:
    """预置场景管理器"""
    
    def __init__(self, base_path: str = None):
        """
        初始化预置场景管理器
        
        Args:
            base_path: 预置场景根目录，默认为 src/prompts/skb
        """
        if base_path is None:
            base_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "prompts", "skb")
        
        self.base_path = Path(base_path)
        self.scenarios: Dict[str, Dict[str, Any]] = {}
        self._load_preset_scenarios()
    
    def _load_preset_scenarios(self):
        """加载所有预置场景"""
        if not self.base_path.exists():
            logger.warning(f"预置场景目录不存在: {self.base_path}")
            return
        
        # 遍历所有子目录
        for scenario_dir in self.base_path.iterdir():
            if scenario_dir.is_dir():
                scenario_name = scenario_dir.name
                try:
                    scenario_config = self._load_scenario_config(scenario_dir)
                    if scenario_config:
                        self.scenarios[scenario_name] = scenario_config
                        logger.info(f"加载预置场景: {scenario_name}")
                except Exception as e:
                    logger.error(f"加载预置场景失败 {scenario_name}: {e}")
    
    def _load_scenario_config(self, scenario_dir: Path) -> Optional[Dict[str, Any]]:
        """
        加载单个场景的配置
        
        Args:
            scenario_dir: 场景目录路径
            
        Returns:
            Dict: 场景配置，包含节点配置和提示词
        """
        # 查找配置文件
        config_file = scenario_dir / "node_config.yaml"
        if not config_file.exists():
            logger.warning(f"配置文件不存在: {config_file}")
            return None
        
        # 加载配置文件
        with open(config_file, "r", encoding="utf-8") as f:
            config = yaml.safe_load(f)
        
        # 加载各节点的提示词文件
        node_prompts = {}
        for node_name in ["planner", "researcher", "reporter", "coder"]:
            prompt_file = scenario_dir / f"{node_name}.md"
            if prompt_file.exists():
                with open(prompt_file, "r", encoding="utf-8") as f:
                    node_prompts[node_name] = f.read().strip()
        
        # 将提示词合并到配置中
        if "nodes" not in config:
            config["nodes"] = {}
        
        for node_name, prompt_content in node_prompts.items():
            if node_name not in config["nodes"]:
                config["nodes"][node_name] = {}
            if "prompts" not in config["nodes"][node_name]:
                config["nodes"][node_name]["prompts"] = {}
            
            config["nodes"][node_name]["prompts"]["system_prompt"] = prompt_content
        
        # 添加场景元信息
        config["scenario_name"] = scenario_dir.name
        config["is_preset"] = True
        
        return config
    
    def get_scenario(self, scenario_identifier: str) -> Optional[Dict[str, Any]]:
        """
        获取指定名称或代码的预置场景配置
        
        Args:
            scenario_identifier: 场景名称或场景代码
            
        Returns:
            Dict: 场景配置，如果不存在则返回None
        """
        # 先尝试按名称查找（兼容旧逻辑）
        if scenario_identifier in self.scenarios:
            return self.scenarios[scenario_identifier]
        
        # 按代码查找（新逻辑）
        return self.get_scenario_by_code(scenario_identifier)
    
    def get_scenario_by_code(self, scenario_code: str) -> Optional[Dict[str, Any]]:
        """
        根据场景代码获取预置场景配置
        
        Args:
            scenario_code: 场景代码，如 'pd', 'sa'
            
        Returns:
            Dict: 场景配置，如果不存在则返回None
        """
        return self.scenarios.get(scenario_code)
    
    def get_scenario_by_name(self, scenario_name: str) -> Optional[Dict[str, Any]]:
        """
        根据场景显示名称获取预置场景配置（用于向后兼容）
        
        Args:
            scenario_name: 场景显示名称，如 '产品设计', '系统架构'
            
        Returns:
            Dict: 场景配置，如果不存在则返回None
        """
        # 遍历所有场景，查找匹配的显示名称
        for scenario_code, scenario_config in self.scenarios.items():
            metadata = scenario_config.get("metadata", {})
            if metadata.get("name") == scenario_name:
                return scenario_config
        return None
    
    def list_scenarios(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有预置场景列表
        
        Returns:
            Dict: 所有预置场景配置
        """
        return self.scenarios.copy()
    
    def get_node_prompt(self, scenario_name: str, node_name: str) -> Optional[str]:
        """
        获取指定场景和节点的提示词
        
        Args:
            scenario_name: 场景名称
            node_name: 节点名称
            
        Returns:
            str: 提示词内容，如果不存在则返回None
        """
        scenario = self.get_scenario(scenario_name)
        if not scenario:
            return None
        
        nodes = scenario.get("nodes", {})
        node_config = nodes.get(node_name, {})
        prompts = node_config.get("prompts", {})
        return prompts.get("system_prompt")


# 全局实例
preset_scenario_manager = PresetScenarioManager() 