# [!NOTE]
# Read the `docs/configuration_guide.md` carefully, and update the configurations to match your specific settings and requirements.
# - Replace `api_key` with your own credentials
# - Replace `base_url` and `model` name if you want to use a custom model

BASIC_MODEL:
  base_url: https://lab.iwhalecloud.com/gpt-proxy/v1
  model: "gpt-4o-mini"
  api_key: ailab_q8k/rwO8LuNzoPXI0vmpu5uTTOb8WCJZ0spGVOK2hqzJ7hmxBOTpjr+IPcHJHUNsQi4i00WLDCLu/9mnZSxiX4nWUXRMHSt4h39HaqsGjGlNro4m6k1FBqE=

# DocChain configuration for document search
DOCCHAIN:
  base_url: "https://lab.iwhalecloud.com/docchain"
  api_key: "lhb1MOiZJCGuiK3TpHFje0JGz4FDg4HyoU_tw2OGOcg"

# Project topic mapping configuration
# Add your project configurations here following the same pattern
PROJECT_TOPIC_MAPPING:
  your_project:
    name: "Your Project Name"
    description: "Your project description"
    code_topic: "your_code_topic_id"
    doc_topic: "your_doc_topic_id"
  default:
    name: "默认项目"
    description: "默认项目配置"
    code_topic: "default_code_topic"
    doc_topic: "default_doc_topic"
