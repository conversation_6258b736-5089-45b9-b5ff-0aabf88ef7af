#!/usr/bin/env python3
"""
测试增强后的DocChain工具
包括搜索和文件读取功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.tools.docchain_search import docchain_search_tool, file_read_by_docchain

def test_docchain_enhanced():
    """测试增强后的DocChain工具"""
    print("测试增强后的DocChain工具...")
    
    # 测试1: DocChain搜索工具
    print("\n=== 测试1: DocChain搜索 ===")
    try:
        result = docchain_search_tool.invoke({
            "query": "API接口",
            "topic_id": "1998",
            "size": 3
        })
        print("搜索结果:")
        print(result)
    except Exception as e:
        print(f"搜索错误: {e}")
    
    # 测试2: DocChain文件读取工具
    print("\n=== 测试2: Doc<PERSON>hain文件读取 ===")
    try:
        result = file_read_by_docchain.invoke({
            "doc_id": "131019"
        })
        print("文件读取结果:")
        print(result[:500] + "..." if len(str(result)) > 500 else result)
    except Exception as e:
        print(f"文件读取错误: {e}")
    
    # 测试3: 参数验证
    print("\n=== 测试3: 参数验证 ===")
    try:
        result = file_read_by_docchain.invoke({
            "doc_id": ""
        })
        print("参数验证结果:")
        print(result)
    except Exception as e:
        print(f"参数验证错误: {e}")

if __name__ == "__main__":
    test_docchain_enhanced() 