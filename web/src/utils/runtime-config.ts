import getConfig from 'next/config';

const { publicRuntimeConfig, serverRuntimeConfig } = getConfig() ?? {};

/**
 * 获取运行时配置
 * 客户端和服务端都可以使用的公共配置
 * @returns 运行时配置对象
 */
export function getRuntimeConfig() {
  return {
    NEXT_PUBLIC_API_URL: publicRuntimeConfig?.NEXT_PUBLIC_API_URL ?? process.env.NEXT_PUBLIC_API_URL,
    NEXT_PUBLIC_STATIC_WEBSITE_ONLY: publicRuntimeConfig?.NEXT_PUBLIC_STATIC_WEBSITE_ONLY === 'true' || process.env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY === 'true',
  };
}

// 只有服务端可以使用的配置
export function getServerRuntimeConfig() {
  if (typeof window !== 'undefined') {
    throw new Error('getServerRuntimeConfig should only be called on the server side');
  }
  
  return {
    GITHUB_OAUTH_TOKEN: serverRuntimeConfig?.GITHUB_OAUTH_TOKEN ?? process.env.GITHUB_OAUTH_TOKEN,
    AMPLITUDE_API_KEY: serverRuntimeConfig?.AMPLITUDE_API_KEY ?? process.env.AMPLITUDE_API_KEY,
    NODE_ENV: process.env.NODE_ENV,
  };
}

// 获取 API URL 的便捷函数
export function getApiUrl(): string {
  const config = getRuntimeConfig();
  return config.NEXT_PUBLIC_API_URL ?? "http://localhost:8000/api/";
}