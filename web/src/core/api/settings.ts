// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { resolveServiceURL } from "./resolve-service-url";

export interface TestConnectionRequest {
  api_key: string;
  base_url: string;
  model: string;
}

export interface TestConnectionResponse {
  success: boolean;
  message: string;
  error?: string;
}

export async function testConnection(request: TestConnectionRequest): Promise<TestConnectionResponse> {
  const response = await fetch(resolveServiceURL("test-connection"), {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
}