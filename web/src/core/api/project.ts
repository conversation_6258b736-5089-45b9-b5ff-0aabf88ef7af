// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { resolveServiceURL } from "./resolve-service-url";

// 项目信息类型定义
export interface ProjectInfo {
  name: string;
  description: string;
  code_topic: string;
  doc_topic: string;
}

export interface ProjectListResponse {
  projects: string[];
  default: ProjectInfo;
}

/**
 * 获取所有项目列表
 */
export async function getProjects(): Promise<ProjectListResponse> {
  const response = await fetch(resolveServiceURL("projects"));
  if (!response.ok) {
    throw new Error(`Failed to fetch projects: ${response.statusText}`);
  }
  return response.json();
}

/**
 * 获取指定项目的信息
 */
export async function getProjectInfo(projectId: string): Promise<ProjectInfo> {
  const response = await fetch(resolveServiceURL(`projects/${projectId}`));
  if (!response.ok) {
    throw new Error(`Failed to fetch project info for ${projectId}: ${response.statusText}`);
  }
  return response.json();
} 