// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { useSettingsStore } from "../store";

// 内置工具定义
const BUILT_IN_TOOLS = {
  // 网络和搜索工具
  "web_search": {
    type: "built-in",
    category: "search",
    displayName: "网络搜索",
    description: "使用搜索引擎搜索网络内容",
    icon: "🔍"
  },
  "crawl_tool": {
    type: "built-in", 
    category: "search",
    displayName: "网页抓取",
    description: "抓取指定网页的内容"
  },
  
  // DocChain工具
  "search_code_by_docchain": {
    type: "built-in",
    category: "docchain",
    displayName: "DocChain代码搜索",
    description: "使用DocChain搜索代码相关信息"
  },
  "search_doc_by_docchain": {
    type: "built-in",
    category: "docchain", 
    displayName: "DocChain文档搜索",
    description: "使用DocChain搜索文档相关信息"
  },
  "file_read_by_docchain": {
    type: "built-in",
    category: "docchain",
    displayName: "DocChain文件读取",
    description: "读取DocChain中指定文档的完整内容"
  },
  "search_file_by_name": {
    type: "built-in",
    category: "docchain",
    displayName: "DocChain文件名搜索",
    description: "根据文件名搜索DocChain中的文档"
  },
  "search_by_docchain_llm": {
    type: "built-in",
    category: "docchain",
    displayName: "DocChain智能问答",
    description: "使用DocChain大模型进行智能搜索和问答"
  },
  "smart_code_analyzer": {
    type: "built-in",
    category: "docchain",
    displayName: "智能代码分析器",
    description: "智能代码分析器 - 代码定位与结构分析工具"
  },
  
  // 开发工具
  "python_repl_tool": {
    type: "built-in",
    category: "development",
    displayName: "Python执行器",
    description: "执行Python代码并返回结果"
  },
  "java_method_call_chain": {
    type: "built-in",
    category: "development",
    displayName: "Java调用链分析",
    description: "分析Java代码中指定方法的调用链"
  }
} as const;

export type ToolType = "built-in" | "mcp";
export type ToolCategory = "search" | "docchain" | "development" | "mcp";

export interface ToolInfo {
  type: ToolType;
  category: ToolCategory;
  displayName: string;
  description: string;
  serverName?: string; // 仅MCP工具有此字段
}

/**
 * 获取工具信息（包括内置工具和MCP工具）
 */
export function getToolInfo(toolName: string): ToolInfo | null {
  // 首先检查是否为内置工具
  if (toolName in BUILT_IN_TOOLS) {
    return BUILT_IN_TOOLS[toolName as keyof typeof BUILT_IN_TOOLS];
  }
  
  // 然后检查MCP工具
  const mcpTool = findMCPTool(toolName);
  if (mcpTool) {
    return {
      type: "mcp",
      category: "mcp",
      displayName: toolName,
      description: mcpTool.description,
      serverName: findMCPServerName(toolName)
    };
  }
  
  return null;
}

/**
 * 查找MCP工具（原有函数）
 */
export function findMCPTool(name: string) {
  const mcpServers = useSettingsStore.getState().mcp.servers;
  for (const server of mcpServers) {
    for (const tool of server.tools) {
      if (tool.name === name) {
        return tool;
      }
    }
  }
  return null;
}

/**
 * 查找MCP工具所属的服务器名称
 */
export function findMCPServerName(toolName: string): string | undefined {
  const mcpServers = useSettingsStore.getState().mcp.servers;
  for (const server of mcpServers) {
    for (const tool of server.tools) {
      if (tool.name === toolName) {
        return server.name;
      }
    }
  }
  return undefined;
}

/**
 * 检查是否为内置工具
 */
export function isBuiltInTool(toolName: string): boolean {
  return toolName in BUILT_IN_TOOLS;
}

/**
 * 检查是否为MCP工具
 */
export function isMCPTool(toolName: string): boolean {
  return findMCPTool(toolName) !== null;
}

/**
 * 获取工具的显示名称
 */
export function getToolDisplayName(toolName: string): string {
  const toolInfo = getToolInfo(toolName);
  return toolInfo?.displayName ?? toolName;
}