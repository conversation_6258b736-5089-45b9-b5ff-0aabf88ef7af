export function autoFixMarkdown(markdown: string): string {
  let fixed = autoCloseCodeBlocks(markdown);
  fixed = autoCloseTrailingLink(fixed);
  return fixed;
}

function autoCloseCodeBlocks(markdown: string): string {
  let fixed = markdown;
  
  // 计算代码块标记的数量
  const codeBlockMatches = fixed.match(/```/g);
  const codeBlockCount = codeBlockMatches ? codeBlockMatches.length : 0;
  
  // 如果代码块标记数量是奇数，说明有未闭合的代码块
  if (codeBlockCount % 2 !== 0) {
    // 在末尾添加闭合标记
    if (!fixed.endsWith('\n')) {
      fixed += '\n';
    }
    fixed += '```';
  }
  
  // 修复行内代码的问题 - 使用简单的方法避免复杂正则
  const lines = fixed.split('\n');
  const fixedLines = lines.map(line => {
    // 跳过代码块行
    if (line.trim().startsWith('```')) {
      return line;
    }
    
    // 计算每行中单个反引号的数量
    let singleBacktickCount = 0;
    let inBacktick = false;
    
    for (let i = 0; i < line.length; i++) {
      if (line[i] === '`' && (i === 0 || line[i-1] !== '`') && (i === line.length-1 || line[i+1] !== '`')) {
        singleBacktickCount++;
        inBacktick = !inBacktick;
      }
    }
    
    // 如果单个反引号数量是奇数，在行末添加一个
    if (singleBacktickCount % 2 !== 0) {
      return line + '`';
    }
    
    return line;
  });
  
  return fixedLines.join('\n');
}

function autoCloseTrailingLink(markdown: string): string {
  // Fix unclosed Markdown links or images
  let fixedMarkdown: string = markdown;

  // Fix unclosed image syntax ![...](...)
  fixedMarkdown = fixedMarkdown.replace(
    /!\[([^\]]*)\]\(([^)]*)$/g,
    (match: string, altText: string, url: string): string => {
      return `![${altText}](${url})`;
    },
  );

  // Fix unclosed link syntax [...](...)
  fixedMarkdown = fixedMarkdown.replace(
    /\[([^\]]*)\]\(([^)]*)$/g,
    (match: string, linkText: string, url: string): string => {
      return `[${linkText}](${url})`;
    },
  );

  // Fix unclosed image syntax ![...]
  fixedMarkdown = fixedMarkdown.replace(
    /!\[([^\]]*)$/g,
    (match: string, altText: string): string => {
      return `![${altText}]`;
    },
  );

  // Fix unclosed link syntax [...]
  fixedMarkdown = fixedMarkdown.replace(
    /\[([^\]]*)$/g,
    (match: string, linkText: string): string => {
      return `[${linkText}]`;
    },
  );

  // Fix unclosed images or links missing ")"
  fixedMarkdown = fixedMarkdown.replace(
    /!\[([^\]]*)\]\(([^)]*)$/g,
    (match: string, altText: string, url: string): string => {
      return `![${altText}](${url})`;
    },
  );

  fixedMarkdown = fixedMarkdown.replace(
    /\[([^\]]*)\]\(([^)]*)$/g,
    (match: string, linkText: string, url: string): string => {
      return `[${linkText}](${url})`;
    },
  );

  return fixedMarkdown;
}
