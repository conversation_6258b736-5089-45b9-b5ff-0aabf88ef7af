// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

export function deepClone<T>(value: T): T {
  // 优化：对于简单类型，直接返回
  if (value === null || typeof value !== "object") {
    return value;
  }

  // 优化：对于数组
  if (Array.isArray(value)) {
    return value.map(item => deepClone(item)) as T;
  }

  // 优化：对于Date对象
  if (value instanceof Date) {
    return new Date(value.getTime()) as T;
  }

  // 优化：对于普通对象，使用结构化克隆
  const cloned = {} as Record<string, unknown>;
  for (const key in value) {
    if (Object.prototype.hasOwnProperty.call(value, key)) {
      cloned[key] = deepClone((value as Record<string, unknown>)[key]);
    }
  }
  return cloned as T;
}
