// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { nanoid } from 'nanoid';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

import type {
    CoderNodeConfig,
    DebugConfiguration,
    FlowStatus,
    PlannerNodeConfig,
    ReporterNodeConfig,
    ResearcherNodeConfig,
    ScenarioConfig,
    ScenarioLibrary,
    ScenarioTemplate,
    TemplateLibrary,
    UIState,
} from '../../app/chat/components/advanced-debug/types';

// ==================== 默认配置定义 ====================

/**
 * 默认Planner节点配置
 */
const createDefaultPlannerConfig = (): PlannerNodeConfig => ({
  maxIterations: 3,
  prompts: {
    systemPrompt: `你是一个专业的项目规划师，负责分析需求并制定详细的执行计划。请基于用户需求制定可执行的步骤和策略。

# 任务目标
根据用户输入 {userInput} 制定详细的执行计划。

# 分析要求
1. **需求理解** - 准确理解用户的核心需求和期望
2. **任务分解** - 将复杂任务分解为可执行的具体步骤
3. **优先级排序** - 根据重要性和依赖关系安排执行顺序
4. **资源评估** - 评估所需的工具、时间和技能
5. **风险识别** - 识别潜在的问题和风险点

# 输出格式
请按以下结构组织你的计划：

## 1. 需求分析
- 核心目标：[明确表达主要目标]
- 关键需求：[列出具体需求点]
- 约束条件：[识别限制因素]

## 2. 执行计划
- 步骤1：[具体操作]
- 步骤2：[具体操作]
- ...

## 3. 资源配置
- 推荐工具：[基于可用工具 {enabledTools}]
- 预估时间：[时间评估]
- 依赖项：[前置条件]

当前项目上下文：{project}
代码库ID：{codeTopicId}
文档库ID：{docTopicId}`,
  },
  scenarioParams: {},
});

/**
 * 默认Researcher节点配置
 */
const createDefaultResearcherConfig = (): ResearcherNodeConfig => ({
  maxSearchResults: 10,
  searchDepth: 3,
  searchStrategy: {
    enableParallelSearch: true,
    searchQuality: 0.8,
    cacheResults: true,
  },
  prompts: {
    systemPrompt: `你是一个专业的研究分析师，负责收集、分析和整理信息以支持决策制定。

# 核心职责
1. **信息收集** - 使用多种工具收集相关信息
2. **深度分析** - 对收集的信息进行深入分析
3. **信息验证** - 确保信息的准确性和可靠性
4. **结构化整理** - 将分析结果组织成易于理解的格式

# 研究方法
- 多角度搜索：从不同维度收集信息
- 交叉验证：通过多个来源验证关键信息
- 深度挖掘：深入分析重要细节
- 关联分析：识别信息之间的关联关系

# 可用工具
{enabledTools}

# 搜索策略
- 最大搜索结果：{maxSearchResults}
- 搜索深度：{searchDepth}
- 并行搜索：已启用
- 结果缓存：已启用

# 输出要求
请提供结构化的研究报告，包含：
1. 关键发现
2. 详细分析
3. 数据支撑
4. 信息来源

当前项目：{project}
代码库搜索ID：{codeTopicId}
文档库搜索ID：{docTopicId}`,
  },
  tools: {
    enabled: ['search_code_by_docchain', 'search_doc_by_docchain', 'file_read_by_docchain'],
    configs: {},
  },
  output: {
    format: 'markdown',
    debugOutput: false,
  },
  scenarioParams: {},
});

/**
 * 默认Reporter节点配置
 */
const createDefaultReporterConfig = (): ReporterNodeConfig => ({
  prompts: {
    systemPrompt: `你是一个专业的报告撰写专家，负责将研究结果整理成清晰、全面的分析报告。

# 核心任务
基于研究团队提供的信息和分析结果，生成高质量的报告文档。

# 报告标准
1. **结构清晰** - 使用逻辑清晰的章节组织
2. **内容全面** - 涵盖所有重要发现和分析
3. **表达准确** - 使用准确、专业的表达方式
4. **易于理解** - 确保读者能够轻松理解内容
5. **可操作性** - 提供具体的建议和行动指南

# 报告结构模板
## 1. 执行摘要
- 核心发现简述
- 主要结论
- 关键建议

## 2. 详细分析
- 问题背景
- 分析过程
- 关键发现
- 深度洞察

## 3. 结论与建议
- 主要结论
- 行动建议
- 风险提示
- 后续步骤

## 4. 附录
- 数据来源
- 参考文献
- 补充材料

# 引用格式
- 使用标准的引用格式标注信息来源
- 确保所有关键数据都有可追溯的来源
- 区分事实陈述和分析观点

当前项目：{project}`,
  },
  tools: {
    enabled: [],
    configs: {},
  },
  output: {
    format: 'markdown',
    includeTableOfContents: false,
    includeMetadata: true,
    includeImageAttachments: true,
    debugOutput: false,
  },
  reportStrategy: {
    enableAutoStructuring: true,
    includeCitations: true,
    citationStyle: 'custom',
    maxSectionDepth: 3,
    enableTableGeneration: true,
  },
  scenarioParams: {},
});

/**
 * 默认Coder节点配置
 */
const createDefaultCoderConfig = (): CoderNodeConfig => ({
  languages: {
    primary: 'auto',
    supportedLanguages: ['python', 'java', 'javascript', 'typescript'],
    enableAutoDetection: true,
  },
  prompts: {
    systemPrompt: `你是一个资深的软件工程师，专精于代码分析、开发和优化。

# 核心技能
1. **代码理解** - 深入理解现有代码结构和逻辑
2. **问题诊断** - 准确识别代码问题和改进点  
3. **解决方案设计** - 设计高效、可维护的解决方案
4. **代码实现** - 编写高质量、符合规范的代码
5. **测试验证** - 确保代码的正确性和健壮性

# 工作流程
1. **需求分析** - 理解具体的编程需求和约束
2. **代码调研** - 使用工具搜索和分析相关代码
3. **方案设计** - 制定技术实现方案  
4. **代码实现** - 编写清晰、高效的代码
5. **测试验证** - 提供测试方案和验证方法
6. **文档说明** - 提供清晰的实现说明

# 编程规范
- 遵循语言最佳实践和编码规范
- 优先考虑代码的可读性和可维护性
- 处理边界条件和异常情况
- 添加适当的注释和文档

# 可用工具
{enabledTools}

# 支持语言
主要语言：{primaryLanguage}
支持语言：{supportedLanguages}
自动检测：已启用

当前项目：{project}
代码库搜索ID：{codeTopicId}
文档库搜索ID：{docTopicId}`,
  },
  tools: {
    enabled: ['search_code_by_docchain', 'search_doc_by_docchain', 'file_read_by_docchain'],
    configs: {},
  },
  output: {
    format: 'markdown',
    includeCodeComments: true,
    includeTestCases: false,
    includeDocumentation: true,
    debugOutput: false,
  },
  analysisStrategy: {
    enableStaticAnalysis: false,
    performanceAnalysis: false,
    securityCheck: false,
    codeQuality: true,
  },
  scenarioParams: {},
});

/**
 * 创建PD (Product Design) 预置场景配置
 */
const createPDScenario = (): ScenarioConfig => ({
  id: 'preset-pd',
  code: 'pd', // 后端标识
  name: '产品设计',
  description: '产品设计场景，专注于用户需求分析、产品功能设计和技术可行性评估',
  metadata: {
    category: 'product_design',
    tags: ['产品设计', '需求分析', '用户体验'],
    difficulty: 'medium',
    estimatedTime: 60,
    createdAt: new Date(),
    updatedAt: new Date(),
    version: '1.0.0',
    author: '系统预置',
  },
  global: {
    project: '',
    codeTopicId: '',
    docTopicId: '',
    locale: 'zh-CN',
    debugMode: false,
    timeoutMinutes: 60,
  },
  nodes: {
    planner: {
      ...createDefaultPlannerConfig(),
      maxIterations: 3,
      scenarioParams: {
        analysis_depth: 'deep',
        focus_areas: ['用户需求', '产品功能', '技术可行性'],
      },
    },
    researcher: {
      ...createDefaultResearcherConfig(),
      maxSearchResults: 8,
      searchDepth: 4,
      tools: {
        enabled: ['search_by_docchain_llm', 'search_doc_by_docchain', 'search_code_by_docchain'],
        configs: {},
      },
      scenarioParams: {
        research_focus: ['竞品分析', '用户调研', '技术方案'],
        quality_threshold: 'high',
      },
    },
    reporter: {
      ...createDefaultReporterConfig(),
      tools: {
        enabled: [],
        configs: {},
      },
      scenarioParams: {
        report_style: 'structured',
        include_visuals: true,
      },
    },
    coder: {
      ...createDefaultCoderConfig(),
      tools: {
        enabled: ['search_code_by_docchain', 'file_read_by_docchain'],
        configs: {},
      },
      scenarioParams: {
        code_focus: ['原型实现', '功能演示'],
      },
    },
  },
  workflow: {
    maxIterations: 3,
    enableParallelExecution: false,
    failureStrategy: 'retry',
    retryCount: 2,
  },
  variables: {},
  isPreset: true,
});

/**
 * 创建SA (System Architecture) 预置场景配置
 */
const createSAScenario = (): ScenarioConfig => ({
  id: 'preset-sa',
  code: 'sa', // 后端标识
  name: '系统架构',
  description: '系统架构设计场景，专注于系统设计、技术选型和性能优化分析',
  metadata: {
    category: 'system_architecture',
    tags: ['系统架构', '技术选型', '性能优化'],
    difficulty: 'complex',
    estimatedTime: 90,
    createdAt: new Date(),
    updatedAt: new Date(),
    version: '1.0.0',
    author: '系统预置',
  },
  global: {
    project: '',
    codeTopicId: '',
    docTopicId: '',
    locale: 'zh-CN',
    debugMode: true,
    timeoutMinutes: 90,
  },
  nodes: {
    planner: {
      ...createDefaultPlannerConfig(),
      maxIterations: 3,
      scenarioParams: {
        architecture_focus: ['系统设计', '技术选型', '性能优化'],
        complexity_level: 'high',
      },
    },
    researcher: {
      ...createDefaultResearcherConfig(),
      maxSearchResults: 10,
      searchDepth: 5,
      tools: {
        enabled: ['search_by_docchain_llm', 'search_doc_by_docchain', 'search_code_by_docchain', 'java_method_call_chain', 'file_read_by_docchain'],
        configs: {},
      },
      scenarioParams: {
        research_areas: ['架构模式', '技术栈', '最佳实践'],
        analysis_depth: 'deep',
      },
    },
    reporter: {
      ...createDefaultReporterConfig(),
      tools: {
        enabled: [],
        configs: {},
      },
      scenarioParams: {
        report_type: 'technical',
        include_diagrams: true,
      },
    },
    coder: {
      ...createDefaultCoderConfig(),
      tools: {
        enabled: ['search_code_by_docchain', 'file_read_by_docchain', 'java_method_call_chain', 'python_repl'],
        configs: {},
      },
      scenarioParams: {
        coding_focus: ['架构实现', '代码示例', '最佳实践'],
        languages: ['java', 'python', 'javascript'],
      },
    },
  },
  workflow: {
    maxIterations: 4,
    enableParallelExecution: true,
    failureStrategy: 'retry',
    retryCount: 3,
  },
  variables: {},
  isPreset: true,
});

/**
 * 创建TEST (Testing) 预置场景配置
 */
const createTestScenario = (): ScenarioConfig => ({
  id: 'preset-test',
  code: 'test', // 后端标识
  name: '测试场景',
  description: '专业测试场景，专注于测试用例生成、边缘场景挖掘和测试数据构造',
  metadata: {
    category: 'testing',
    tags: ['测试用例', '边缘场景', '测试数据', '自动化测试'],
    difficulty: 'complex',
    estimatedTime: 120,
    createdAt: new Date(),
    updatedAt: new Date(),
    version: '1.0.0',
    author: '系统预置',
  },
  global: {
    project: '',
    codeTopicId: '',
    docTopicId: '',
    locale: 'zh-CN',
    debugMode: true,
    timeoutMinutes: 120,
  },
  nodes: {
    planner: {
      ...createDefaultPlannerConfig(),
      maxIterations: 3,
      scenarioParams: {
        test_analysis_depth: 'comprehensive',
        focus_areas: ['功能测试', '边缘场景', '性能测试', '安全测试'],
        test_coverage_target: 'high',
      },
    },
    researcher: {
      ...createDefaultResearcherConfig(),
      maxSearchResults: 12,
      searchDepth: 6,
      tools: {
        enabled: ['search_by_docchain_llm', 'search_doc_by_docchain', 'search_code_by_docchain', 'java_method_call_chain', 'file_read_by_docchain', 'search_file_by_name'],
        configs: {},
      },
      scenarioParams: {
        research_focus: ['代码逻辑分析', '接口参数解析', '异常情况挖掘', '边界值识别'],
        test_case_quality: 'high',
        edge_case_coverage: 'comprehensive',
      },
    },
    reporter: {
      ...createDefaultReporterConfig(),
      tools: {
        enabled: [],
        configs: {},
      },
      scenarioParams: {
        report_style: 'detailed_test_report',
        include_test_parameters: true,
        include_test_scenarios: true,
        include_edge_cases: true,
        include_test_data: true,
        support_custom_format: true,
        output_json_format: true,
      },
    },
    coder: {
      ...createDefaultCoderConfig(),
      tools: {
        enabled: ['search_code_by_docchain', 'file_read_by_docchain', 'java_method_call_chain', 'python_repl'],
        configs: {},
      },
      scenarioParams: {
        test_framework_focus: ['junit', 'testng', 'mockito', 'postman'],
        automation_level: 'high',
      },
    },
  },
  workflow: {
    maxIterations: 4,
    enableParallelExecution: true,
    failureStrategy: 'retry',
    retryCount: 3,
  },
  variables: {},
  isPreset: true,
});

/**
 * 创建默认场景配置
 */
const createDefaultScenario = (name: string, category: string, code?: string): ScenarioConfig => ({
  id: nanoid(),
  code: code ?? name.toLowerCase().replace(/[^a-z0-9]/g, '_'), // 生成code或使用提供的code
  name,
  description: `基于${name}的自定义场景`,
  metadata: {
    category,
    tags: ['默认', '基础'],
    difficulty: 'medium',
    estimatedTime: 30,
    createdAt: new Date(),
    updatedAt: new Date(),
    version: '1.0.0',
  },
  global: {
    project: '',
    codeTopicId: '',
    docTopicId: '',
    locale: 'zh-CN',
    debugMode: false,
    timeoutMinutes: 30,
  },
  nodes: {
    planner: createDefaultPlannerConfig(),
    researcher: createDefaultResearcherConfig(),
    reporter: createDefaultReporterConfig(),
    coder: createDefaultCoderConfig(),
  },
  workflow: {
    maxIterations: 3,
    enableParallelExecution: false,
    failureStrategy: 'retry',
    retryCount: 2,
  },
  variables: {},
  isPreset: true, // 预置场景，不可删除
});

/**
 * 默认场景库
 */
const createDefaultScenarioLibrary = (): ScenarioLibrary => {
  const defaultScenario = createDefaultScenario('通用分析', 'requirement_analysis', 'general_analysis');
  const pdScenario = createPDScenario();
  const saScenario = createSAScenario();
  const testScenario = createTestScenario();
  
  return {
    activeScenarioId: null, // 默认不选择任何场景
    enabledScenarios: [defaultScenario.id, pdScenario.id, saScenario.id, testScenario.id], // 默认启用所有预置场景
    scenarios: {
      [defaultScenario.id]: defaultScenario,
      [pdScenario.id]: pdScenario,
      [saScenario.id]: saScenario,
      [testScenario.id]: testScenario,
    },
    categories: [
      {
        id: 'requirement_analysis',
        name: '需求分析',
        description: '分析和理解业务需求，制定技术方案',
        scenarios: [defaultScenario.id],
      },
      {
        id: 'product_design',
        name: '产品设计',
        description: '产品需求分析、设计规划、用户体验优化',
        scenarios: [pdScenario.id],
      },
      {
        id: 'system_architecture',
        name: '系统架构',
        description: '系统架构设计、技术选型、性能优化',
        scenarios: [saScenario.id],
      },
      {
        id: 'testing',
        name: '测试场景',
        description: '自动化测试用例生成、边缘场景挖掘、测试数据构造',
        scenarios: [testScenario.id],
      },
    ],
    recentUsed: [defaultScenario.id],
    favorites: [],
  };
};

/**
 * 默认模板库
 */
const createDefaultTemplateLibrary = (): TemplateLibrary => ({
  templates: {},
  categories: [],
  recentUsed: [],
  favorites: [],
  importHistory: [],
});

/**
 * 默认调试配置
 */
const createDefaultDebugConfig = (): DebugConfiguration => ({
  scenarioLibrary: createDefaultScenarioLibrary(),
  templateLibrary: createDefaultTemplateLibrary(),
  globalSettings: {
    defaultScenarioId: null,
    autoSaveInterval: 30,
    enablePreview: true,
    enableValidation: true,
    debugMode: false,
  },
  metadata: {
    version: '2.0.0',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
});

/**
 * 默认UI状态
 */
const createDefaultUIState = (): UIState => ({
  isDialogOpen: false,
  activeTab: 'scenarios',
  editor: {
    isEditing: false,
    currentScenarioId: null,
    hasUnsavedChanges: false,
    validationErrors: [],
  },
  filters: {
    category: null,
    tags: [],
    searchQuery: '',
    sortBy: 'name',
    sortOrder: 'asc',
  },
  selection: {
    selectedScenarios: [],
    selectedTemplates: [],
  },
});

// ==================== Store定义 ====================

interface DebugStore {
  // 核心配置
  config: DebugConfiguration;
  
  // UI状态
  ui: UIState;
  
  // 流程状态
  flowStatus: FlowStatus | null;
  
  // ==================== 场景管理方法 ====================
  
  // 获取当前活跃场景
  getActiveScenario: () => ScenarioConfig | null;
  
  // 设置活跃场景
  setActiveScenario: (scenarioId: string | null) => void;
  
  // 启用/禁用场景
  toggleScenarioEnabled: (scenarioId: string) => void;
  
  // 获取已启用的场景列表
  getEnabledScenarios: () => ScenarioConfig[];
  
  // 创建新场景
  createScenario: (name: string, category: string, basedOn?: string) => string;
  
  // 更新场景配置
  updateScenario: (scenarioId: string, updates: Partial<ScenarioConfig>) => void;
  
  // 删除场景
  deleteScenario: (scenarioId: string) => void;
  
  // 复制场景
  duplicateScenario: (scenarioId: string, newName: string) => string;
  
  // ==================== 节点配置方法 ====================
  
  // 更新Planner节点配置
  updatePlannerConfig: (scenarioId: string, config: Partial<PlannerNodeConfig>) => void;
  
  // 更新Researcher节点配置
  updateResearcherConfig: (scenarioId: string, config: Partial<ResearcherNodeConfig>) => void;
  
  // 更新Reporter节点配置
  updateReporterConfig: (scenarioId: string, config: Partial<ReporterNodeConfig>) => void;
  
  // 更新Coder节点配置
  updateCoderConfig: (scenarioId: string, config: Partial<CoderNodeConfig>) => void;
  
  // ==================== 模板管理方法 ====================
  
  // 将场景保存为模板
  saveScenarioAsTemplate: (scenarioId: string, templateName: string, description?: string) => string;
  
  // 从模板创建场景
  createScenarioFromTemplate: (templateId: string, scenarioName: string) => string;
  
  // 导入模板
  importTemplate: (template: ScenarioTemplate) => void;
  
  // 导出模板
  exportTemplate: (templateId: string) => ScenarioTemplate | null;
  
  // 删除模板
  deleteTemplate: (templateId: string) => void;
  
  // 添加到收藏
  addToFavorites: (type: 'scenario' | 'template', id: string) => void;
  
  // 从收藏移除
  removeFromFavorites: (type: 'scenario' | 'template', id: string) => void;
  
  // ==================== UI控制方法 ====================
  
  // 设置对话框打开状态
  setDialogOpen: (open: boolean) => void;
  
  // 设置活跃标签页
  setActiveTab: (tab: UIState['activeTab']) => void;
  
  // 设置编辑状态
  setEditorState: (state: Partial<UIState['editor']>) => void;
  
  // 设置过滤器
  setFilters: (filters: Partial<UIState['filters']>) => void;
  
  // 设置选择状态
  setSelection: (selection: Partial<UIState['selection']>) => void;
  
  // ==================== 流程控制方法 ====================
  
  // 更新流程状态
  updateFlowStatus: (status: FlowStatus | null) => void;
  
  // ==================== 实用方法 ====================
  
  // 重置到默认状态
  resetToDefaults: () => void;
  
  // 导出完整配置
  exportFullConfig: () => DebugConfiguration;
  
  // 导入完整配置
  importFullConfig: (config: DebugConfiguration) => void;
  
  // 验证配置
  validateConfig: (scenarioId?: string) => string[];
}

// ==================== Store实现 ====================

export const useDebugStore = create<DebugStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      config: createDefaultDebugConfig(),
      ui: createDefaultUIState(),
      flowStatus: null,
      
      // ==================== 场景管理方法实现 ====================
      
      getActiveScenario: () => {
        const { config } = get();
        const activeId = config.scenarioLibrary.activeScenarioId;
        return activeId ? config.scenarioLibrary.scenarios[activeId] ?? null : null;
      },
      
      setActiveScenario: (scenarioId) => {
        set((state) => ({
          config: {
            ...state.config,
            scenarioLibrary: {
              ...state.config.scenarioLibrary,
              activeScenarioId: scenarioId,
            },
          },
        }));
      },
      
      toggleScenarioEnabled: (scenarioId) => {
        set((state) => {
          const enabledScenarios = state.config.scenarioLibrary.enabledScenarios ?? [];
          const isEnabled = enabledScenarios.includes(scenarioId);
          const newEnabledScenarios = isEnabled
            ? enabledScenarios.filter(id => id !== scenarioId)
            : [...enabledScenarios, scenarioId];
          
          return {
            config: {
              ...state.config,
              scenarioLibrary: {
                ...state.config.scenarioLibrary,
                enabledScenarios: newEnabledScenarios,
              },
            },
          };
        });
      },
      
      getEnabledScenarios: () => {
        const state = get();
        const enabledScenarios = state.config.scenarioLibrary.enabledScenarios ?? [];
        return enabledScenarios
          .map(id => state.config.scenarioLibrary.scenarios[id])
          .filter((scenario): scenario is ScenarioConfig => Boolean(scenario));
      },
      
      createScenario: (name, category, basedOn) => {
        const newId = nanoid();
        const baseScenario = basedOn 
          ? get().config.scenarioLibrary.scenarios[basedOn]
          : undefined;
        
        const newScenario: ScenarioConfig = baseScenario 
          ? {
              ...baseScenario,
              id: newId,
              code: name.toLowerCase().replace(/[^a-z0-9]/g, '_'), // 为复制的场景生成新的code
              name,
              metadata: {
                ...baseScenario.metadata,
                createdAt: new Date(),
                updatedAt: new Date(),
              },
              isPreset: false, // 复制的场景不是预置场景
            }
          : {
              // 创建全新的用户场景，而不是使用系统默认场景模板
              id: newId,
              code: name.toLowerCase().replace(/[^a-z0-9]/g, '_'), // 生成code
              name,
              description: `用户创建的${name}场景`,
              metadata: {
                category,
                tags: ['用户创建'],
                difficulty: 'medium',
                estimatedTime: 30,
                createdAt: new Date(),
                updatedAt: new Date(),
                version: '1.0.0',
              },
              global: {
                project: '',
                codeTopicId: '',
                docTopicId: '',
                locale: 'zh-CN',
                debugMode: false,
                timeoutMinutes: 30,
              },
              nodes: {
                planner: createDefaultPlannerConfig(),
                researcher: createDefaultResearcherConfig(),
                reporter: createDefaultReporterConfig(),
                coder: createDefaultCoderConfig(),
              },
              workflow: {
                maxIterations: 3,
                enableParallelExecution: false,
                failureStrategy: 'retry',
                retryCount: 2,
              },
              variables: {},
            };
        
        set((state) => ({
          config: {
            ...state.config,
            scenarioLibrary: {
              ...state.config.scenarioLibrary,
              scenarios: {
                ...state.config.scenarioLibrary.scenarios,
                [newId]: newScenario,
              },
              recentUsed: [newId, ...state.config.scenarioLibrary.recentUsed.slice(0, 9)],
            },
          },
        }));
        
        return newId;
      },
      
      updateScenario: (scenarioId, updates) => {
        set((state) => {
          const scenario = state.config.scenarioLibrary.scenarios[scenarioId];
          if (!scenario) return state;
          
          // 预置场景不允许修改
          if (scenario.isPreset) {
            console.warn('⚠️ 预置场景不允许修改:', scenarioId);
            return state;
          }
          
          return {
            config: {
              ...state.config,
              scenarioLibrary: {
                ...state.config.scenarioLibrary,
                scenarios: {
                  ...state.config.scenarioLibrary.scenarios,
                  [scenarioId]: {
                    ...scenario,
                    ...updates,
                    metadata: {
                      ...scenario.metadata,
                      ...updates.metadata,
                      updatedAt: new Date(),
                    },
                  },
                },
              },
            },
          };
        });
      },
      
      deleteScenario: (scenarioId) => {
        set((state) => {
          const scenario = state.config.scenarioLibrary.scenarios[scenarioId];
          
          // 预置场景不允许删除
          if (scenario?.isPreset) {
            console.warn('⚠️ 预置场景不允许删除:', scenarioId);
            return state;
          }
          
          const { [scenarioId]: deleted, ...restScenarios } = state.config.scenarioLibrary.scenarios;
      // 删除场景，此变量用于结构赋值
      void deleted;
          
          return {
            config: {
              ...state.config,
              scenarioLibrary: {
                ...state.config.scenarioLibrary,
                scenarios: restScenarios,
                activeScenarioId: state.config.scenarioLibrary.activeScenarioId === scenarioId 
                  ? null
                  : state.config.scenarioLibrary.activeScenarioId,
                recentUsed: state.config.scenarioLibrary.recentUsed.filter(id => id !== scenarioId),
                favorites: state.config.scenarioLibrary.favorites.filter(id => id !== scenarioId),
              },
            },
          };
        });
      },
      
      duplicateScenario: (scenarioId, newName) => {
        const scenario = get().config.scenarioLibrary.scenarios[scenarioId];
        if (!scenario) return '';
        
        return get().createScenario(newName, scenario.metadata.category, scenarioId);
      },
      
      // ==================== 节点配置方法实现 ====================
      
      updatePlannerConfig: (scenarioId, config) => {
        set((state) => {
          const scenario = state.config.scenarioLibrary.scenarios[scenarioId];
          if (!scenario) return state;
          
          // 预置场景不允许修改
          if (scenario.isPreset) {
            console.warn('⚠️ 预置场景的Planner配置不允许修改:', scenarioId);
            return state;
          }
          
          return {
            config: {
              ...state.config,
              scenarioLibrary: {
                ...state.config.scenarioLibrary,
                scenarios: {
                  ...state.config.scenarioLibrary.scenarios,
                  [scenarioId]: {
                    ...scenario,
                    nodes: {
                      ...scenario.nodes,
                      planner: {
                        ...scenario.nodes.planner,
                        ...config,
                      },
                    },
                    metadata: {
                      ...scenario.metadata,
                      updatedAt: new Date(),
                    },
                  },
                },
              },
            },
          };
        });
      },
      
      updateResearcherConfig: (scenarioId, config) => {
        set((state) => {
          const scenario = state.config.scenarioLibrary.scenarios[scenarioId];
          if (!scenario) return state;
          
          // 预置场景不允许修改
          if (scenario.isPreset) {
            console.warn('⚠️ 预置场景的Researcher配置不允许修改:', scenarioId);
            return state;
          }
          
          return {
            config: {
              ...state.config,
              scenarioLibrary: {
                ...state.config.scenarioLibrary,
                scenarios: {
                  ...state.config.scenarioLibrary.scenarios,
                  [scenarioId]: {
                    ...scenario,
                    nodes: {
                      ...scenario.nodes,
                      researcher: {
                        ...scenario.nodes.researcher,
                        ...config,
                      },
                    },
                    metadata: {
                      ...scenario.metadata,
                      updatedAt: new Date(),
                    },
                  },
                },
              },
            },
          };
        });
      },
      
      updateReporterConfig: (scenarioId, config) => {
        set((state) => {
          const scenario = state.config.scenarioLibrary.scenarios[scenarioId];
          if (!scenario) return state;
          
          // 预置场景不允许修改
          if (scenario.isPreset) {
            console.warn('⚠️ 预置场景的Reporter配置不允许修改:', scenarioId);
            return state;
          }
          
          // 如果当前场景没有 reporter 配置，则使用默认配置
          const currentReporterConfig = scenario.nodes.reporter || createDefaultReporterConfig();
          
          return {
            config: {
              ...state.config,
              scenarioLibrary: {
                ...state.config.scenarioLibrary,
                scenarios: {
                  ...state.config.scenarioLibrary.scenarios,
                  [scenarioId]: {
                    ...scenario,
                    nodes: {
                      ...scenario.nodes,
                      reporter: {
                        ...currentReporterConfig,
                        ...config,
                      },
                    },
                    metadata: {
                      ...scenario.metadata,
                      updatedAt: new Date(),
                    },
                  },
                },
              },
            },
          };
        });
      },
      
      updateCoderConfig: (scenarioId, config) => {
        set((state) => {
          const scenario = state.config.scenarioLibrary.scenarios[scenarioId];
          if (!scenario) return state;
          
          // 预置场景不允许修改
          if (scenario.isPreset) {
            console.warn('⚠️ 预置场景的Coder配置不允许修改:', scenarioId);
            return state;
          }

          // 如果当前场景没有 coder 配置，则使用默认配置
          const currentCoderConfig = scenario.nodes.coder || createDefaultCoderConfig();
          
          return {
            config: {
              ...state.config,
              scenarioLibrary: {
                ...state.config.scenarioLibrary,
                scenarios: {
                  ...state.config.scenarioLibrary.scenarios,
                  [scenarioId]: {
                    ...scenario,
                    nodes: {
                      ...scenario.nodes,
                      coder: {
                        ...currentCoderConfig,
                        ...config,
                      },
                    },
                    metadata: {
                      ...scenario.metadata,
                      updatedAt: new Date(),
                    },
                  },
                },
              },
            },
          };
        });
      },
      
      // ==================== 模板管理方法实现 ====================
      
      saveScenarioAsTemplate: (scenarioId, templateName, description) => {
        const scenario = get().config.scenarioLibrary.scenarios[scenarioId];
        if (!scenario) return '';
        
        const templateId = nanoid();
        const template: ScenarioTemplate = {
          id: templateId,
          name: templateName,
          description: description ?? `基于场景"${scenario.name}"创建的模板`,
          category: scenario.metadata.category,
          tags: [...scenario.metadata.tags, '用户模板'],
          metadata: {
            createdAt: new Date(),
            updatedAt: new Date(),
            version: '1.0.0',
            downloads: 0,
            rating: 0,
            isOfficial: false,
            isPublic: false,
          },
          template: {
            name: scenario.name,
            description: scenario.description,
            global: scenario.global,
            nodes: scenario.nodes,
            workflow: scenario.workflow,
            variables: scenario.variables,
          },
          usage: {
            totalUses: 0,
            lastUsed: new Date(),
            successRate: 1.0,
          },
        };
        
        set((state) => ({
          config: {
            ...state.config,
            templateLibrary: {
              ...state.config.templateLibrary,
              templates: {
                ...state.config.templateLibrary.templates,
                [templateId]: template,
              },
              recentUsed: [templateId, ...state.config.templateLibrary.recentUsed.slice(0, 9)],
            },
          },
        }));
        
        return templateId;
      },
      
      createScenarioFromTemplate: (templateId, scenarioName) => {
        const template = get().config.templateLibrary.templates[templateId];
        if (!template) return '';
        
        const scenarioId = nanoid();
        const scenario: ScenarioConfig = {
          ...template.template,
          id: scenarioId,
          code: scenarioName.toLowerCase().replace(/[^a-z0-9]/g, '_'),
          name: scenarioName,
          description: template.description,
          metadata: {
            category: template.category,
            tags: [...template.tags, '从模板创建'],
            difficulty: 'medium',
            estimatedTime: 30,
            createdAt: new Date(),
            updatedAt: new Date(),
            version: '1.0.0',
          },
          isTemplate: false,
        };
        
        set((state) => ({
          config: {
            ...state.config,
            scenarioLibrary: {
              ...state.config.scenarioLibrary,
              scenarios: {
                ...state.config.scenarioLibrary.scenarios,
                [scenarioId]: scenario,
              },
              recentUsed: [scenarioId, ...state.config.scenarioLibrary.recentUsed.slice(0, 9)],
            },
            templateLibrary: {
              ...state.config.templateLibrary,
              templates: {
                ...state.config.templateLibrary.templates,
                [templateId]: {
                  ...template,
                  usage: {
                    ...template.usage,
                    totalUses: template.usage.totalUses + 1,
                    lastUsed: new Date(),
                  },
                },
              },
              recentUsed: [templateId, ...state.config.templateLibrary.recentUsed.filter(id => id !== templateId).slice(0, 9)],
            },
          },
        }));
        
        return scenarioId;
      },
      
      importTemplate: (template) => {
        set((state) => ({
          config: {
            ...state.config,
            templateLibrary: {
              ...state.config.templateLibrary,
              templates: {
                ...state.config.templateLibrary.templates,
                [template.id]: template,
              },
              importHistory: [
                {
                  templateId: template.id,
                  importedAt: new Date(),
                  source: 'manual_import',
                },
                ...state.config.templateLibrary.importHistory.slice(0, 49),
              ],
            },
          },
        }));
      },
      
      exportTemplate: (templateId) => {
        return get().config.templateLibrary.templates[templateId] ?? null;
      },
      
      deleteTemplate: (templateId) => {
        set((state) => {
          const { [templateId]: deleted, ...restTemplates } = state.config.templateLibrary.templates;
      // 删除模板，此变量用于结构赋值
      void deleted;
          
          return {
            config: {
              ...state.config,
              templateLibrary: {
                ...state.config.templateLibrary,
                templates: restTemplates,
                recentUsed: state.config.templateLibrary.recentUsed.filter(id => id !== templateId),
                favorites: state.config.templateLibrary.favorites.filter(id => id !== templateId),
              },
            },
          };
        });
      },
      
      addToFavorites: (type, id) => {
        set((state) => {
          if (type === 'scenario') {
            return {
              config: {
                ...state.config,
                scenarioLibrary: {
                  ...state.config.scenarioLibrary,
                  favorites: [...new Set([...state.config.scenarioLibrary.favorites, id])],
                },
              },
            };
          } else {
            return {
              config: {
                ...state.config,
                templateLibrary: {
                  ...state.config.templateLibrary,
                  favorites: [...new Set([...state.config.templateLibrary.favorites, id])],
                },
              },
            };
          }
        });
      },
      
      removeFromFavorites: (type, id) => {
        set((state) => {
          if (type === 'scenario') {
            return {
              config: {
                ...state.config,
                scenarioLibrary: {
                  ...state.config.scenarioLibrary,
                  favorites: state.config.scenarioLibrary.favorites.filter(fav => fav !== id),
                },
              },
            };
          } else {
            return {
              config: {
                ...state.config,
                templateLibrary: {
                  ...state.config.templateLibrary,
                  favorites: state.config.templateLibrary.favorites.filter(fav => fav !== id),
                },
              },
            };
          }
        });
      },
      
      // ==================== UI控制方法实现 ====================
      
      setDialogOpen: (open) => {
        set((state) => ({
          ui: {
            ...state.ui,
            isDialogOpen: open,
          },
        }));
      },
      
      setActiveTab: (tab) => {
        set((state) => ({
          ui: {
            ...state.ui,
            activeTab: tab,
          },
        }));
      },
      
      setEditorState: (editorState) => {
        set((state) => ({
          ui: {
            ...state.ui,
            editor: {
              ...state.ui.editor,
              ...editorState,
            },
          },
        }));
      },
      
      setFilters: (filters) => {
        set((state) => ({
          ui: {
            ...state.ui,
            filters: {
              ...state.ui.filters,
              ...filters,
            },
          },
        }));
      },
      
      setSelection: (selection) => {
        set((state) => ({
          ui: {
            ...state.ui,
            selection: {
              ...state.ui.selection,
              ...selection,
            },
          },
        }));
      },
      
      // ==================== 流程控制方法实现 ====================
      
      updateFlowStatus: (status) => {
        set({ flowStatus: status });
      },
      
      // ==================== 实用方法实现 ====================
      
      resetToDefaults: () => {
        set({
          config: createDefaultDebugConfig(),
          ui: createDefaultUIState(),
          flowStatus: null,
        });
      },
      
      exportFullConfig: () => {
        return get().config;
      },
      
      importFullConfig: (config) => {
        set({ config });
      },
      
      validateConfig: (scenarioId) => {
        const errors: string[] = [];
        const { config } = get();
        
        if (scenarioId) {
          const scenario = config.scenarioLibrary.scenarios[scenarioId];
          if (!scenario) {
            errors.push(`场景 ${scenarioId} 不存在`);
            return errors;
          }
          
          if (!scenario.name.trim()) {
            errors.push('场景名称不能为空');
          }
          
          if (!scenario.metadata.category) {
            errors.push('场景分类不能为空');
          }
          
          if (scenario.workflow.maxIterations < 1) {
            errors.push('最大迭代次数必须大于0');
          }
        }
        
        return errors;
      },
    }),
    {
      name: 'deer-flow-debug-store',
      version: 2,
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        config: state.config,
        ui: {
          ...state.ui,
          isDialogOpen: false,
        },
      }),
      migrate: (persistedState: unknown, version: number) => {        
        // 强制重置到新配置以包含 PD 和 SA 预置场景
        if (version !== 2) {
          const newConfig = createDefaultDebugConfig();
          return {
            config: newConfig,
            ui: createDefaultUIState(),
          };
        }
        
        // 即使是相同版本，也要检查是否缺少必要的预置场景
        const state = persistedState as { config?: { scenarioLibrary?: { scenarios?: Record<string, unknown> } } };
        if (state?.config?.scenarioLibrary?.scenarios) {
          const scenarios = state.config.scenarioLibrary.scenarios;
          const hasPresetPD = Object.values(scenarios).some((s: unknown) => (s as { id?: string }).id === 'preset-pd');
          const hasPresetSA = Object.values(scenarios).some((s: unknown) => (s as { id?: string }).id === 'preset-sa');
          const scenarioCount = Object.keys(scenarios).length;
                    
          if (!hasPresetPD || !hasPresetSA || scenarioCount < 3) {
            const newConfig = createDefaultDebugConfig();
            return {
              config: newConfig,
              ui: createDefaultUIState(),
            };
          }
        }
        
        return persistedState;
      },
    }
  )
); 