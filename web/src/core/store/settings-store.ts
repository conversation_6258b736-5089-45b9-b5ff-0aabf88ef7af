// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { create } from "zustand";

import type { MCPServerMetadata, MCPToolMetadata, SimpleMCPServerMetadata } from "../mcp";

const SETTINGS_KEY = "deerflow.settings";

export type SettingsData = {
  general: {
    apiKey?: string;
    baseUrl?: string;
    model?: string;
    autoAcceptedPlan: boolean;
    enableBackgroundInvestigation: boolean;
    maxPlanIterations: number;
    maxStepNum: number;
    maxSearchResults: number;
  };
  mcp: {
    servers: MCPServerMetadata[];
  };
  project: string;
  topics: {
    // 项目级别的topic配置，key是project名称
    projectTopics: Record<string, {
      codeTopicId: string;
      docTopicId: string;
    }>;
  };
  docChain: {
    baseUrl: string;
    apiKey: string;
  };
};

export type SettingsState = SettingsData & {
  // Actions
  setProjectTopics: (project: string, codeTopicId: string, docTopicId: string) => void;
  getProjectTopics: (project: string) => { codeTopicId: string; docTopicId: string } | null;
};

const DEFAULT_SETTINGS: SettingsData = {
  general: {
    apiKey: "",
    baseUrl: "https://lab.iwhalecloud.com/gpt-proxy/v1",
    model: "gpt-4.1",
    autoAcceptedPlan: false,
    enableBackgroundInvestigation: false,
    maxPlanIterations: 1,
    maxStepNum: 3,
    maxSearchResults: 3,
  },
  mcp: {
    servers: [],
  },
  project: "",
  topics: {
    projectTopics: {},
  },
  docChain: {
    baseUrl: "http://10.10.176.213:7000/llmdoc",
    apiKey: "QuhIKIA5idHejM0YjnWPOyMQuwpApnDYDjQvuDOVk9c",
  },
};

export const useSettingsStore = create<SettingsState>((set, get) => ({
  ...DEFAULT_SETTINGS,
  setProjectTopics: (project: string, codeTopicId: string, docTopicId: string) => {
    set((state) => ({
      ...state,
      topics: {
        ...state.topics,
        projectTopics: {
          ...state.topics.projectTopics,
          [project]: {
            codeTopicId,
            docTopicId,
          },
        },
      },
    }));
    // 自动保存配置
    setTimeout(() => saveSettings(), 0);
  },
  getProjectTopics: (project: string) => {
    const state = get();
    return state.topics.projectTopics[project] ?? null;
  },
}));

export const useSettings = (key: keyof SettingsData) => {
  return useSettingsStore((state) => state[key]);
};

export const changeSettings = (settings: SettingsData) => {
  useSettingsStore.setState({ ...useSettingsStore.getState(), ...settings });
};

export const loadSettings = () => {
  if (typeof window === "undefined") {
    return;
  }
  const json = localStorage.getItem(SETTINGS_KEY);
  if (json) {
    try {
      const settings = JSON.parse(json);
      
      // 确保所有必需的字段都存在，如果不存在则使用默认值
      const mergedSettings = {
        ...DEFAULT_SETTINGS,
        ...settings,
        general: {
          ...DEFAULT_SETTINGS.general,
          ...(settings.general ?? {}),
        },
        mcp: {
          ...DEFAULT_SETTINGS.mcp,
          ...(settings.mcp ?? {}),
        },
        topics: {
          ...DEFAULT_SETTINGS.topics,
          ...(settings.topics ?? {}),
        },
        docChain: {
          ...DEFAULT_SETTINGS.docChain,
          ...(settings.docChain ?? {}),
        },
      };

      // 修复顺序：先取当前状态，再用加载的设置覆盖
      useSettingsStore.setState({ ...useSettingsStore.getState(), ...mergedSettings });
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }
};

export const saveSettings = () => {
  const latestSettings = useSettingsStore.getState();
  const json = JSON.stringify(latestSettings);
  localStorage.setItem(SETTINGS_KEY, json);
};

export const getChatStreamSettings = () => {
  let mcpSettings:
    | {
        servers: Record<
          string,
          MCPServerMetadata & {
            enabled_tools: string[];
            add_to_agents: string[];
          }
        >;
      }
    | undefined = undefined;
  const { mcp, general, topics, docChain } = useSettingsStore.getState();
  const mcpServers = mcp.servers.filter((server) => server.enabled);
  if (mcpServers.length > 0) {
    mcpSettings = {
      servers: mcpServers.reduce((acc, cur) => {
        const { transport, env } = cur;
        let server: SimpleMCPServerMetadata;
        if (transport === "stdio") {
          server = {
            name: cur.name,
            transport,
            env,
            command: cur.command,
            args: cur.args,
          };
        } else {
          server = {
            name: cur.name,
            transport,
            env,
            url: cur.url,
          };
        }

        // 修复enabled_tools过滤逻辑
        let enabledTools: MCPToolMetadata[] = [];
        
        if (cur.enabled_tools?.trim()) {
          // 如果用户配置了enabled_tools，解析逗号分隔的工具名称
          enabledTools = cur.enabled_tools
            .split(',')
            .map(tool => {
              const toolInfo = cur.tools.find(t => t.name === tool);
              return {
                name: tool,
                description: toolInfo?.description ?? `${tool}工具`,
                inputSchema: toolInfo?.inputSchema
              };
            })
            .filter(tool => tool.name.length > 0);
          
          // 验证工具名称是否存在于服务器的工具列表中
          const availableTools = cur.tools.map(tool => tool.name);
          enabledTools = enabledTools.filter(tool => availableTools.includes(tool.name));
          
        } else {
          // 如果没有配置enabled_tools，默认启用所有工具
          enabledTools = cur.tools;
        }

        return {
          ...acc,
          [cur.name]: {
            ...server,
            enabled_tools: enabledTools,
            add_to_agents: cur.add_to_agents ? cur.add_to_agents.split(',').map(agent => agent.trim()) : ["researcher", "planner"],
          },
        };
      }, {}),
    };
  }
  return {
    ...general,
    apiKey: general.apiKey,
    baseUrl: general.baseUrl,
    model: general.model,
    mcpSettings,
    topics,
    docChain,
  };
};

export function setEnableBackgroundInvestigation(value: boolean) {
  useSettingsStore.setState((state) => ({
    general: {
      ...state.general,
      enableBackgroundInvestigation: value,
    },
  }));
  saveSettings();
}

// 新增工具使用统计功能
export const updateToolUsage = (serverName: string, toolName: string) => {
  useSettingsStore.setState((state) => {
    const servers = state.mcp.servers.map((server) => {
      if (server.name === serverName) {
        const updatedServer = { ...server };
        updatedServer.toolStats ??= {};
        
        updatedServer.toolStats[toolName] = {
          lastUsed: Date.now(),
          usageCount: (updatedServer.toolStats[toolName]?.usageCount ?? 0) + 1,
        };
        
        return updatedServer;
      }
      return server;
    });
    
    return {
      ...state,
      mcp: {
        ...state.mcp,
        servers,
      },
    };
  });
  
  // 自动保存配置
  setTimeout(() => saveSettings(), 0);
};

export const getToolUsageStats = (serverName: string, toolName: string) => {
  const state = useSettingsStore.getState();
  const server = state.mcp.servers.find(s => s.name === serverName);
  return server?.toolStats?.[toolName] ?? { usageCount: 0, lastUsed: undefined };
};

export const getAllToolUsageStats = () => {
  const state = useSettingsStore.getState();
  const stats: Record<string, Record<string, { usageCount: number; lastUsed?: number }>> = {};
  
  state.mcp.servers.forEach((server) => {
    if (server.toolStats) {
      const serverStats: Record<string, { usageCount: number; lastUsed?: number }> = {};
      Object.entries(server.toolStats).forEach(([toolName, toolStat]) => {
        serverStats[toolName] = {
          usageCount: toolStat.usageCount ?? 0,
          lastUsed: toolStat.lastUsed,
        };
      });
      stats[server.name] = serverStats;
    }
  });
  
  return stats;
};

loadSettings();
