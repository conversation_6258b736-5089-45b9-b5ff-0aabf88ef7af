// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { nanoid } from "nanoid";
import { toast } from "sonner";
import { create } from "zustand";
import { useShallow } from "zustand/react/shallow";

import type { ScenarioConfig } from "~/app/chat/components/advanced-debug/types";

import { chatStream, generatePodcast } from "../api";
import type { Message, Resource } from "../messages";
import { mergeMessage } from "../messages";
import { parseJSON } from "../utils";

import { getChatStreamSettings } from "./settings-store";

const THREAD_ID = nanoid();

export const useStore = create<{
  responding: boolean;
  threadId: string | undefined;
  messageIds: string[];
  messages: Map<string, Message>;
  researchIds: string[];
  researchPlanIds: Map<string, string>;
  researchReportIds: Map<string, string>;
  researchActivityIds: Map<string, string[]>;
  ongoingResearchId: string | null;
  openResearchId: string | null;

  appendMessage: (message: Message) => void;
  updateMessage: (message: Message) => void;
  updateMessages: (messages: Message[]) => void;
  openResearch: (researchId: string | null) => void;
  closeResearch: () => void;
  setOngoingResearch: (researchId: string | null) => void;
}>((set, _get) => ({
  responding: false,
  threadId: THREAD_ID,
  messageIds: [],
  messages: new Map<string, Message>(),
  researchIds: [],
  researchPlanIds: new Map<string, string>(),
  researchReportIds: new Map<string, string>(),
  researchActivityIds: new Map<string, string[]>(),
  ongoingResearchId: null,
  openResearchId: null,

  appendMessage(message: Message) {
    set((state) => ({
      messageIds: [...state.messageIds, message.id],
      messages: new Map(state.messages).set(message.id, message),
    }));
  },
  updateMessage(message: Message) {
    set((state) => ({
      messages: new Map(state.messages).set(message.id, message),
    }));
  },
  updateMessages(messages: Message[]) {
    set((state) => {
      const newMessages = new Map(state.messages);
      messages.forEach((m) => newMessages.set(m.id, m));
      return { messages: newMessages };
    });
  },
  openResearch(researchId: string | null) {
    set({ openResearchId: researchId });
  },
  closeResearch() {
    set({ openResearchId: null });
  },
  setOngoingResearch(researchId: string | null) {
    set({ ongoingResearchId: researchId });
  },
}));

// 优化：添加消息更新批处理和去重
const messagePendingUpdates = new Map<string, Message>();
let updateTimeoutId: NodeJS.Timeout | null = null;

function batchUpdateMessages() {
  if (messagePendingUpdates.size === 0) return;
  
  const messages = Array.from(messagePendingUpdates.values());
  messagePendingUpdates.clear();
  
  useStore.getState().updateMessages(messages);
}

function scheduleMessageUpdate(message: Message) {
  // 优化：批量处理消息更新，减少频繁的状态更新
  messagePendingUpdates.set(message.id, message);
  
  if (updateTimeoutId) {
    clearTimeout(updateTimeoutId);
  }
  
  // 优化：为工具调用相关的消息使用更短的延迟
  const hasToolCalls = message.toolCalls && message.toolCalls.length > 0;
  const delay = hasToolCalls ? 10 : (message.isStreaming ? 50 : 0);
  updateTimeoutId = setTimeout(batchUpdateMessages, delay);
}

export async function sendMessage(
  content?: string,
  {
    interruptFeedback,
    resources,
    scenarioConfig,
    scenarioName,
  }: {
    interruptFeedback?: string;
    resources?: Array<Resource>;
    scenarioConfig?: ScenarioConfig;
    scenarioName?: string;
  } = {},
  options: { abortSignal?: AbortSignal } = {},
) {
  // 从URL参数中获取project
  const urlParams = new URLSearchParams(window.location.search);
  const project = urlParams.get("project");

  const settings = getChatStreamSettings();
  
  // 验证API key
  if (!settings.apiKey || settings.apiKey.trim() === "") {
    toast.error("请先在设置中配置API Key", {
      description: "点击右上角的设置按钮，在General标签页中设置API Key",
      action: {
        label: "打开设置",
        onClick: () => {
          // 触发设置对话框打开的事件
          document.dispatchEvent(new CustomEvent('open-settings-dialog'));
        },
      },
    });
    return;
  }
  
  if (content != null) {
    appendMessage({
      id: nanoid(),
      threadId: THREAD_ID,
      role: "user",
      content: content,
      contentChunks: [content],
      resources,
    });
  }
  
  // 获取topics配置
  const topics = settings.topics?.projectTopics ?? {};
  
  // 根据场景类型决定发送哪些参数
  let scenarioParams: {
    scenario_code?: string;
    scenario_name?: string;
    scenario_config?: ScenarioConfig;
  } = {};

  if (scenarioConfig) {
    if (scenarioConfig.isPreset) {
      // 预置场景：只发送场景代码，后端已知配置
      scenarioParams = {
        scenario_code: scenarioConfig.code,
        scenario_name: scenarioConfig.name,
      };
    } else {
      // 非预置场景：发送完整配置，后端不知道配置
      scenarioParams = {
        scenario_config: scenarioConfig,
        scenario_name: scenarioConfig.name,
      };
    }
  } else if (scenarioName) {
    // 兼容旧的调用方式
    scenarioParams = {
      scenario_name: scenarioName,
    };
  }
  
  const requestParams = {
    thread_id: THREAD_ID,
    api_key: settings.apiKey,
    base_url: settings.baseUrl,
    model: settings.model,
    interrupt_feedback: interruptFeedback,
    resources,
    auto_accepted_plan: settings.autoAcceptedPlan,
    enable_background_investigation:
      settings.enableBackgroundInvestigation ?? true,
    max_plan_iterations: settings.maxPlanIterations,
    max_step_num: settings.maxStepNum,
    max_search_results: settings.maxSearchResults,
    mcp_settings: settings.mcpSettings,
    project: project ?? undefined,
    topics: Object.keys(topics).length > 0 ? topics : undefined,
    docchain_base_url: settings.docChain?.baseUrl,
    docchain_api_key: settings.docChain?.apiKey,
    ...scenarioParams,
  };
    
  const stream = chatStream(
    content ?? "[REPLAY]",
    requestParams,
    options,
  );

  setResponding(true);
  let messageId: string | undefined;
  try {
    for await (const event of stream) {
      const { type, data } = event;
      
      // 处理错误事件
      if (type === "error") {
        let errorMessage = "生成回复时发生错误，请重试。";
        
        // 解析后端返回的具体错误信息
        if (data.error) {
          const errorText = data.error;
          if (errorText.includes("API Key") || errorText.includes("authentication") || errorText.includes("401")) {
            errorMessage = "API Key验证失败，请检查您在设置中配置的API Key是否正确。";
          } else if (errorText.includes("base_url") || errorText.includes("connection")) {
            errorMessage = "无法连接到API服务，请检查您在设置中配置的Base URL是否正确。";
          } else if (errorText.includes("quota") || errorText.includes("limit")) {
            errorMessage = "API配额已用完或达到速率限制，请稍后重试或检查您的账户余额。";
          } else if (errorText.includes("network") || errorText.includes("timeout")) {
            errorMessage = "网络连接超时，请检查网络连接后重试。";
          } else if (errorText.includes("MCP") || errorText.includes("MultiServerMCPClient") || errorText.includes("TaskGroup")) {
            errorMessage = "MCP工具连接异常，正在使用默认工具继续执行。如果问题持续存在，请检查MCP服务器配置。";
          } else if (errorText.includes("unhandled errors") || errorText.includes("sub-exception")) {
            errorMessage = "执行过程中出现未处理的异常，系统已自动恢复。如果问题持续存在，请联系管理员。";
          } else {
            // 使用后端返回的具体错误信息
            errorMessage = `错误：${errorText}`;
          }
        }
        
        // 对于MCP相关错误，使用警告级别而不是错误级别
        const isMCPError = data.error && (
          data.error.includes("MCP") || 
          data.error.includes("MultiServerMCPClient") || 
          data.error.includes("TaskGroup") ||
          data.error.includes("sub-exception")
        );
        
        if (isMCPError) {
          toast.warning(errorMessage, {
            description: "系统已切换到默认工具继续执行，功能不受影响。",
          });
        } else {
          toast.error(errorMessage, {
            description: "如果问题持续存在，请检查设置或联系管理员。",
            action: {
              label: "打开设置",
              onClick: () => {
                document.dispatchEvent(new CustomEvent('open-settings-dialog'));
              },
            },
          });
        }
        
        // 对于MCP错误，不停止处理，继续等待后续消息
        if (!isMCPError) {
          useStore.getState().setOngoingResearch(null);
          setResponding(false);
          return;
        }
        
        // MCP错误时继续处理，不返回
        continue;
              }
        
        messageId = data.id;
        let message: Message | undefined;
        if (type === "tool_call_result") {
          message = findMessageByToolCallId(data.tool_call_id);
        } else if (!existsMessage(messageId)) {
          message = {
            id: messageId,
            threadId: data.thread_id,
            agent: data.agent,
            role: data.role,
            content: "",
            contentChunks: [],
            isStreaming: true,
            interruptFeedback,
          };
          appendMessage(message);
        }
        message ??= getMessage(messageId);
        if (message) {
          message = mergeMessage(message, event);
          updateMessage(message);
        }
    }
  } catch (error) {
    let errorMessage = "生成回复时发生错误，请重试。";
    
    // 尝试解析具体的错误信息
    if (error instanceof Error) {
      // 检查是否是中止错误
      if (error.name === "AbortError") {
        console.log("Request was aborted by user");
        return; // 用户主动取消，不显示错误
      }
      
      // 尝试从错误消息中提取有用信息
      const errorText = error.message;
      if (errorText.includes("API Key") || errorText.includes("authentication")) {
        errorMessage = "API Key验证失败，请检查您在设置中配置的API Key是否正确。";
      } else if (errorText.includes("base_url") || errorText.includes("connection")) {
        errorMessage = "无法连接到API服务，请检查您在设置中配置的Base URL是否正确。";
      } else if (errorText.includes("quota") || errorText.includes("limit")) {
        errorMessage = "API配额已用完或达到速率限制，请稍后重试或检查您的账户余额。";
      } else if (errorText.includes("network") || errorText.includes("timeout")) {
        errorMessage = "网络连接超时，请检查网络连接后重试。";
      } else if (errorText.trim()) {
        // 如果有具体错误信息，使用它
        errorMessage = `错误：${errorText}`;
      }
    }
    
    toast.error(errorMessage, {
      description: "如果问题持续存在，请检查设置或联系管理员。",
      action: {
        label: "打开设置",
        onClick: () => {
          document.dispatchEvent(new CustomEvent('open-settings-dialog'));
        },
      },
    });
    
    // Update message status.
    if (messageId != null) {
      const message = getMessage(messageId);
      if (message?.isStreaming) {
        message.isStreaming = false;
        useStore.getState().updateMessage(message);
      }
    }
    useStore.getState().setOngoingResearch(null);
  } finally {
    setResponding(false);
  }
}

function setResponding(value: boolean) {
  useStore.setState({ responding: value });
}

function existsMessage(id: string) {
  return useStore.getState().messageIds.includes(id);
}

function getMessage(id: string) {
  return useStore.getState().messages.get(id);
}

function findMessageByToolCallId(toolCallId: string) {
  return Array.from(useStore.getState().messages.values())
    .reverse()
    .find((message) => {
      if (message.toolCalls) {
        return message.toolCalls.some((toolCall) => toolCall.id === toolCallId);
      }
      return false;
    });
}

function appendMessage(message: Message) {
  if (
    message.agent === "coder" ||
    message.agent === "reporter" ||
    message.agent === "researcher"
  ) {
    if (!getOngoingResearchId()) {
      const id = message.id;
      appendResearch(id);
      openResearch(id);
    }
    appendResearchActivity(message);
  }
  useStore.getState().appendMessage(message);
}

function updateMessage(message: Message) {
  if (
    getOngoingResearchId() &&
    message.agent === "reporter" &&
    !message.isStreaming
  ) {
    useStore.getState().setOngoingResearch(null);
  }
  
  // 修复：优化工具调用显示逻辑，确保立即更新
  const requiresImmediateUpdate = (message as Message & { __requiresImmediateUpdate?: boolean }).__requiresImmediateUpdate;
  const hasToolCalls = message.toolCalls && message.toolCalls.length > 0;
  
  // 开发环境调试信息
  if (process.env.NODE_ENV === "development" && hasToolCalls && message.toolCalls) {
    console.log('Tool call message update:', {
      messageId: message.id,
      agent: message.agent,
      toolCalls: message.toolCalls.map(tc => ({ name: tc.name, id: tc.id, hasResult: !!tc.result })),
      requiresImmediateUpdate,
      isStreaming: message.isStreaming
    });
  }
  
  // 优先级1：工具调用相关或标记为需要立即更新的消息 - 立即更新
  if (requiresImmediateUpdate || hasToolCalls) {
    useStore.getState().updateMessage(message);
  }
  // 优先级2：关键节点消息（planner, reporter）或完成状态消息 - 立即更新
  else if (message.agent === "planner" || message.agent === "reporter" || !message.isStreaming) {
    useStore.getState().updateMessage(message);
  }
  // 优先级3：其他流式消息 - 批处理优化性能
  else {
    scheduleMessageUpdate(message);
  }
}

function getOngoingResearchId() {
  return useStore.getState().ongoingResearchId;
}

function appendResearch(researchId: string) {
  let planMessage: Message | undefined;
  const reversedMessageIds = [...useStore.getState().messageIds].reverse();
  for (const messageId of reversedMessageIds) {
    const message = getMessage(messageId);
    if (message?.agent === "planner") {
      planMessage = message;
      break;
    }
  }
  const messageIds = [researchId];
  messageIds.unshift(planMessage!.id);
  useStore.setState({
    ongoingResearchId: researchId,
    researchIds: [...useStore.getState().researchIds, researchId],
    researchPlanIds: new Map(useStore.getState().researchPlanIds).set(
      researchId,
      planMessage!.id,
    ),
    researchActivityIds: new Map(useStore.getState().researchActivityIds).set(
      researchId,
      messageIds,
    ),
  });
}

function appendResearchActivity(message: Message) {
  const researchId = getOngoingResearchId();
  if (researchId) {
    const researchActivityIds = useStore.getState().researchActivityIds;
    const current = researchActivityIds.get(researchId)!;
    if (!current.includes(message.id)) {
      useStore.setState({
        researchActivityIds: new Map(researchActivityIds).set(researchId, [
          ...current,
          message.id,
        ]),
      });
    }
    if (message.agent === "reporter") {
      useStore.setState({
        researchReportIds: new Map(useStore.getState().researchReportIds).set(
          researchId,
          message.id,
        ),
      });
    }
  }
}

export function openResearch(researchId: string | null) {
  useStore.getState().openResearch(researchId);
}

export function closeResearch() {
  useStore.getState().closeResearch();
}

export async function listenToPodcast(researchId: string) {
  const planMessageId = useStore.getState().researchPlanIds.get(researchId);
  const reportMessageId = useStore.getState().researchReportIds.get(researchId);
  if (planMessageId && reportMessageId) {
    const planMessage = getMessage(planMessageId)!;
    const title = parseJSON(planMessage.content, { title: "Untitled" }).title;
    const reportMessage = getMessage(reportMessageId);
    if (reportMessage?.content) {
      appendMessage({
        id: nanoid(),
        threadId: THREAD_ID,
        role: "user",
        content: "Please generate a podcast for the above research.",
        contentChunks: [],
      });
      const podCastMessageId = nanoid();
      const podcastObject = { title, researchId };
      const podcastMessage: Message = {
        id: podCastMessageId,
        threadId: THREAD_ID,
        role: "assistant",
        agent: "podcast",
        content: JSON.stringify(podcastObject),
        contentChunks: [],
        isStreaming: true,
      };
      appendMessage(podcastMessage);
      // Generating podcast...
      let audioUrl: string | undefined;
      try {
        audioUrl = await generatePodcast(reportMessage.content);
      } catch (e) {
        console.error(e);
        useStore.setState((state) => ({
          messages: new Map(useStore.getState().messages).set(
            podCastMessageId,
            {
              ...state.messages.get(podCastMessageId)!,
              content: JSON.stringify({
                ...podcastObject,
                error: e instanceof Error ? e.message : "Unknown error",
              }),
              isStreaming: false,
            },
          ),
        }));
        toast("An error occurred while generating podcast. Please try again.");
        return;
      }
      useStore.setState((state) => ({
        messages: new Map(useStore.getState().messages).set(podCastMessageId, {
          ...state.messages.get(podCastMessageId)!,
          content: JSON.stringify({ ...podcastObject, audioUrl }),
          isStreaming: false,
        }),
      }));
    }
  }
}

export function useResearchMessage(researchId: string) {
  return useStore(
    useShallow((state) => {
      const messageId = state.researchPlanIds.get(researchId);
      return messageId ? state.messages.get(messageId) : undefined;
    }),
  );
}

export function useMessage(messageId: string | null | undefined) {
  return useStore(
    useShallow((state) =>
      messageId ? state.messages.get(messageId) : undefined,
    ),
  );
}

export function useMessageIds() {
  return useStore(useShallow((state) => state.messageIds));
}

export function useLastInterruptMessage() {
  return useStore(
    useShallow((state) => {
      if (state.messageIds.length >= 2) {
        const lastMessage = state.messages.get(
          state.messageIds[state.messageIds.length - 1]!,
        );
        return lastMessage?.finishReason === "interrupt" ? lastMessage : null;
      }
      return null;
    }),
  );
}

export function useLastFeedbackMessageId() {
  const waitingForFeedbackMessageId = useStore(
    useShallow((state) => {
      if (state.messageIds.length >= 2) {
        const lastMessage = state.messages.get(
          state.messageIds[state.messageIds.length - 1]!,
        );
        if (lastMessage && lastMessage.finishReason === "interrupt") {
          return state.messageIds[state.messageIds.length - 2];
        }
      }
      return null;
    }),
  );
  return waitingForFeedbackMessageId;
}

export function useToolCalls() {
  return useStore(
    useShallow((state) => {
      return state.messageIds
        ?.map((id) => getMessage(id)?.toolCalls)
        .filter((toolCalls) => toolCalls != null)
        .flat();
    }),
  );
}
