// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { LoadingOutlined } from "@ant-design/icons";
import { motion } from "framer-motion";
import { Download, Headphones } from "lucide-react";
import { memo, useCallback, useMemo, useRef, useState } from "react";
import { useShallow } from "zustand/react/shallow";

import { LoadingAnimation } from "~/components/deer-flow/loading-animation";
import { Markdown } from "~/components/deer-flow/markdown";
import { RainbowText } from "~/components/deer-flow/rainbow-text";
import { RollingText } from "~/components/deer-flow/rolling-text";
import {
    ScrollContainer,
    type ScrollContainerRef,
} from "~/components/deer-flow/scroll-container";
import { Tooltip } from "~/components/deer-flow/tooltip";
import { Button } from "~/components/ui/button";
import {
    Card,
    CardContent,
    CardFooter,
    CardHeader,
    CardTitle,
} from "~/components/ui/card";
import type { Message, Option } from "~/core/messages";
import {
    closeResearch,
    openResearch,
    useLastFeedbackMessageId,
    useLastInterruptMessage,
    useMessage,
    useResearchMessage,
    useStore,
} from "~/core/store";
import { useDebugStore } from "~/core/store/debug-store";
import { parseJSON } from "~/core/utils";
import { cn } from "~/lib/utils";

import type { ScenarioConfig } from "./advanced-debug/types";

export function MessageListView({
  className,
  onFeedback,
  onSendMessage,
}: {
  className?: string;
  onFeedback?: (feedback: { option: Option }) => void;
  onSendMessage?: (
    message: string,
    options?: { 
      interruptFeedback?: string;
      scenarioConfig?: ScenarioConfig;
      scenarioName?: string;
    },
  ) => void;
}) {
  const scrollContainerRef = useRef<ScrollContainerRef>(null);
  const messageIds = useStore(useShallow((state) => state.messageIds));
  const waitingForFeedbackMessageId = useLastFeedbackMessageId();
  const lastInterruptMessage = useLastInterruptMessage();
  const responding = useStore((state) => state.responding);
  const noOngoingResearch = useStore(
    (state) => state.ongoingResearchId === null,
  );
  const ongoingResearchIsOpen = useStore(
    (state) => state.ongoingResearchId === state.openResearchId,
  );

  const handleToggleResearch = useCallback(() => {
    // HACK: Add a delay to prevent React from batching state updates, which
    // occasionally fails when toggling research.
    const researchId = useStore.getState().ongoingResearchId;
    if (!researchId) {
      return;
    }
    if (useStore.getState().openResearchId === researchId) {
      closeResearch();
    } else {
      openResearch(researchId);
    }
    
    // Fix the issue where auto-scrolling to the bottom
    // occasionally fails when toggling research.
    const timer = setTimeout(() => {
      if (scrollContainerRef.current) {
        scrollContainerRef.current.scrollToBottom();
      }
    }, 500);
    return () => {
      clearTimeout(timer);
    };
  }, []);

  return (
    <ScrollContainer
      className={cn("flex h-full w-full flex-col overflow-hidden", className)}
      scrollShadowColor="var(--app-background)"
      autoScrollToBottom
      ref={scrollContainerRef}
    >
      <ul className="flex flex-col">
        {messageIds.map((messageId) => (
          <MessageListItem
            key={messageId}
            messageId={messageId}
            waitForFeedback={waitingForFeedbackMessageId === messageId}
            interruptMessage={lastInterruptMessage}
            onFeedback={onFeedback}
            onSendMessage={onSendMessage}
            onToggleResearch={handleToggleResearch}
          />
        ))}
        <div className="flex h-8 w-full shrink-0"></div>
      </ul>
      {responding && (noOngoingResearch || !ongoingResearchIsOpen) && (
        <LoadingAnimation className="ml-4" />
      )}
    </ScrollContainer>
  );
}

// 优化：添加memo以防止不必要的重新渲染
const MessageListItem = memo(function MessageListItem({
  className,
  messageId,
  waitForFeedback,
  interruptMessage,
  onFeedback,
  onSendMessage,
  onToggleResearch,
}: {
  className?: string;
  messageId: string;
  waitForFeedback?: boolean;
  onFeedback?: (feedback: { option: Option }) => void;
  interruptMessage?: Message | null;
  onSendMessage?: (
    message: string,
    options?: { 
      interruptFeedback?: string;
      scenarioConfig?: ScenarioConfig;
      scenarioName?: string;
    },
  ) => void;
  onToggleResearch?: () => void;
}) {
  const message = useMessage(messageId);
  
  // 优化：使用更精确的状态选择器
  const isStartOfResearch = useStore((state) => 
    state.researchIds.includes(messageId)
  );
  
  // 优化：预先计算动画属性以避免在渲染中重复创建
  const motionProps = useMemo(() => ({
    className: "mt-10",
    initial: { opacity: 0, y: 24 },
    animate: { opacity: 1, y: 0 },
    style: { transition: "all 0.2s ease-out" },
    transition: {
      duration: 0.2,
      ease: "easeOut" as const,
    },
  }), []);
  
  const content = useMemo(() => {
    if (!message || !(
      message.role === "user" ||
      message.agent === "coordinator" ||
      message.agent === "planner" ||
      message.agent === "podcast" ||
      isStartOfResearch
    )) {
      return null;
    }

    if (message.agent === "planner") {
      return (
        <div className="w-full px-4">
          <PlanCard
            message={message}
            waitForFeedback={waitForFeedback}
            interruptMessage={interruptMessage}
            onFeedback={onFeedback}
            onSendMessage={onSendMessage}
          />
        </div>
      );
    } else if (message.agent === "podcast") {
      return (
        <div className="w-full px-4">
          <PodcastCard message={message} />
        </div>
      );
    } else if (isStartOfResearch) {
      return (
        <div className="w-full px-4">
          <ResearchCard
            researchId={message.id}
            onToggleResearch={onToggleResearch}
          />
        </div>
      );
    } else if (message.content || message.error) {
      return (
        <div
          className={cn(
            "flex w-full px-4",
            message.role === "user" && "justify-end",
            className,
          )}
        >
          <MessageBubble message={message}>
            <div className="flex w-full flex-col text-wrap break-words break-all min-w-0">
              {message.error ? (
                <div className="flex flex-col gap-2">
                  <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                    <span className="font-medium">错误</span>
                  </div>
                  <div className="text-sm text-red-800 dark:text-red-200 bg-red-50 dark:bg-red-950/20 p-3 rounded-lg border border-red-200 dark:border-red-800">
                    {message.error.message}
                  </div>
                  {message.error.type === "workflow_error" && (
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      请检查您的 API Key 和 Base URL 配置是否正确。
                    </div>
                  )}
                </div>
              ) : (
              <Markdown
                className={cn(
                  message.role === "user" &&
                    "prose-invert not-dark:text-secondary dark:text-inherit",
                )}
                animated={false} // 优化：对于普通消息禁用动画以提升性能
              >
                {message?.content}
              </Markdown>
              )}
            </div>
          </MessageBubble>
        </div>
      );
    }
    return null;
  }, [message, isStartOfResearch, waitForFeedback, interruptMessage, onFeedback, onSendMessage, onToggleResearch, className]);

  if (!content) {
    return null;
  }

  return (
    <motion.li key={messageId} {...motionProps}>
      {content}
    </motion.li>
  );
});

// 优化：添加memo以防止不必要的重新渲染
const MessageBubble = memo(function MessageBubble({
  className,
  message,
  children,
}: {
  className?: string;
  message: Message;
  children: React.ReactNode;
}) {
  return (
    <div
      className={cn(
        `group flex w-fit max-w-[85%] flex-col rounded-2xl px-4 py-3 shadow`,
        message.role === "user" && "bg-brand rounded-ee-none",
        message.role === "assistant" && "bg-card rounded-es-none",
        className,
      )}
    >
      {children}
    </div>
  );
});

// 优化：添加memo以防止不必要的重新渲染
const ResearchCard = memo(function ResearchCard({
  className,
  researchId,
  onToggleResearch,
}: {
  className?: string;
  researchId: string;
  onToggleResearch?: () => void;
}) {
  const reportId = useStore((state) => state.researchReportIds.get(researchId));
  const hasReport = reportId !== undefined;
  const reportGenerating = useStore(
    (state) => hasReport && state.messages.get(reportId)!.isStreaming,
  );
  const openResearchId = useStore((state) => state.openResearchId);
  const state = useMemo(() => {
    if (hasReport) {
      return reportGenerating ? "Generating report..." : "Report generated";
    }
    return "Researching...";
  }, [hasReport, reportGenerating]);
  const msg = useResearchMessage(researchId);
  const title = useMemo(() => {
    if (msg) {
      return parseJSON(msg.content ?? "", { title: "" }).title;
    }
    return undefined;
  }, [msg]);
  const handleOpen = useCallback(() => {
    if (openResearchId === researchId) {
      closeResearch();
    } else {
      openResearch(researchId);
    }
    onToggleResearch?.();
  }, [openResearchId, researchId, onToggleResearch]);
  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle>
          <RainbowText animated={state !== "Report generated"}>
            {title !== undefined && title !== "" ? title : "Deep Research"}
          </RainbowText>
        </CardTitle>
      </CardHeader>
      <CardFooter>
        <div className="flex w-full">
          <RollingText className="text-muted-foreground flex-grow text-sm">
            {state}
          </RollingText>
          <Button
            variant={!openResearchId ? "default" : "outline"}
            onClick={handleOpen}
          >
            {researchId !== openResearchId ? "Open" : "Close"}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
});

const GREETINGS = ["Cool", "Sounds great", "Looks good", "Great", "Awesome"];

// 优化：添加memo并优化计算逻辑以防止不必要的重新渲染
const PlanCard = memo(function PlanCard({
  className,
  message,
  interruptMessage,
  onFeedback,
  waitForFeedback,
  onSendMessage,
}: {
  className?: string;
  message: Message;
  interruptMessage?: Message | null;
  onFeedback?: (feedback: { option: Option }) => void;
  onSendMessage?: (
    message: string,
    options?: { 
      interruptFeedback?: string;
      scenarioConfig?: ScenarioConfig;
      scenarioName?: string;
    },
  ) => void;
  waitForFeedback?: boolean;
}) {
  // 获取当前活跃的场景配置
  const activeScenario = useDebugStore((state) => state.getActiveScenario());
  
  // 优化：缓存解析结果以避免重复解析JSON
  const plan = useMemo(() => {
    return parseJSON(message.content ?? "", {}) as {
      title?: string;
      thought?: string;
      steps?: { title?: string; description?: string }[];
    };
  }, [message.content]);
  
  // 清理字符串开头的逗号和空格
  const cleanString = useCallback((str: string | undefined): string => {
    if (!str) return "";
    return str.replace(/^[\s,]+/, '').trim();
  }, []);

  // 处理后的标题和内容，确保类型安全
  const cleanTitle = cleanString(plan.title) || "Deep Research";
  const cleanThought = cleanString(plan.thought);

  const handleAccept = useCallback(async () => {
    if (onSendMessage) {
      // 传递当前活跃的场景配置
      const options = {
        interruptFeedback: "accepted",
        scenarioConfig: activeScenario ?? undefined,
        scenarioName: activeScenario?.code ?? undefined,
      };
      onSendMessage(
        `${GREETINGS[Math.floor(Math.random() * GREETINGS.length)]}! ${Math.random() > 0.5 ? "Let's get started." : "Let's start."}`,
        options,
      );
    }
  }, [onSendMessage, activeScenario]);

  // 优化：预先计算动画属性
  const motionProps = useMemo(() => ({
    className: "flex gap-2 justify-end",
    initial: { opacity: 0, y: 12 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.3, delay: 0.3 },
  }), []);

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle>
          {cleanTitle}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Markdown className="opacity-80" animated={false}> {/* 优化：禁用动画 */}
          {cleanThought}
        </Markdown>
        {plan.steps && (
          <ul className="my-2 flex list-decimal flex-col gap-4 border-l-[2px] pl-8">
            {plan.steps.map((step: { title?: string; description?: string }, i: number) => (
              <li key={`step-${i}`}>
                <h3 className="mb text-lg font-medium">
                  <Markdown animated={false}>{cleanString(step.title)}</Markdown> {/* 优化：禁用动画 */}
                </h3>
                <div className="text-muted-foreground text-sm">
                  <Markdown animated={false}>{cleanString(step.description)}</Markdown> {/* 优化：禁用动画 */}
                </div>
              </li>
            ))}
          </ul>
        )}
      </CardContent>
      <CardFooter>
        {!message.isStreaming && interruptMessage?.options?.length && (
          <motion.div {...motionProps}>
            {interruptMessage?.options.map((option) => (
              <Button
                key={option.value}
                variant={option.value === "accepted" ? "default" : "outline"}
                disabled={!waitForFeedback}
                onClick={() => {
                  if (option.value === "accepted") {
                    void handleAccept();
                  } else {
                    onFeedback?.({
                      option,
                    });
                  }
                }}
              >
                {option.text}
              </Button>
            ))}
          </motion.div>
        )}
      </CardFooter>
    </Card>
  );
});

// 优化：添加memo以防止不必要的重新渲染
const PodcastCard = memo(function PodcastCard({
  className,
  message,
}: {
  className?: string;
  message: Message;
}) {
  // 优化：缓存解析结果
  const data = useMemo(() => {
    return JSON.parse(message.content ?? "");
  }, [message.content]);
  const title = useMemo<string | undefined>(() => data?.title, [data]);
  const audioUrl = useMemo<string | undefined>(() => data?.audioUrl, [data]);
  const isGenerating = useMemo(() => {
    return message.isStreaming;
  }, [message.isStreaming]);
  const hasError = useMemo(() => {
    return data?.error !== undefined;
  }, [data]);
  const [isPlaying, setIsPlaying] = useState(false);
  return (
    <Card className={cn("w-[508px]", className)}>
      <CardHeader>
        <div className="text-muted-foreground flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            {isGenerating ? <LoadingOutlined /> : <Headphones size={16} />}
            {!hasError ? (
              <RainbowText animated={isGenerating}>
                {isGenerating
                  ? "Generating podcast..."
                  : isPlaying
                    ? "Now playing podcast..."
                    : "Podcast"}
              </RainbowText>
            ) : (
              <div className="text-red-500">
                Error when generating podcast. Please try again.
              </div>
            )}
          </div>
          {!hasError && !isGenerating && (
            <div className="flex">
              <Tooltip title="Download podcast">
                <Button variant="ghost" size="icon" asChild>
                  <a
                    href={audioUrl}
                    download={`${(title ?? "podcast").replaceAll(" ", "-")}.mp3`}
                  >
                    <Download size={16} />
                  </a>
                </Button>
              </Tooltip>
            </div>
          )}
        </div>
        <CardTitle>
          <div className="text-lg font-medium">
            <RainbowText animated={isGenerating}>{title}</RainbowText>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {audioUrl ? (
          <audio
            className="w-full"
            src={audioUrl}
            controls
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
          />
        ) : (
          <div className="w-full"></div>
        )}
      </CardContent>
    </Card>
  );
});
