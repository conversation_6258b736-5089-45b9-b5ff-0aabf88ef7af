// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { Check, Copy, Headphones, Pencil, Undo2, X } from "lucide-react";
import { useCallback, useEffect, useState } from "react";

import { ScrollContainer } from "~/components/deer-flow/scroll-container";
import { Tooltip } from "~/components/deer-flow/tooltip";
import { Button } from "~/components/ui/button";
import { Card } from "~/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { useReplay } from "~/core/replay";
import { closeResearch, listenToPodcast, useStore } from "~/core/store";
import { cn } from "~/lib/utils";

import { ResearchActivitiesBlock } from "./research-activities-block";
import { ResearchReportBlock } from "./research-report-block";

export function ResearchBlock({
  className,
  researchId = null,
}: {
  className?: string;
  researchId: string | null;
}) {
  // 1. Group all hooks at the top of the component
  const reportId = useStore((state) =>
    researchId ? state.researchReportIds.get(researchId) : undefined,
  );
  const [activeTab, setActiveTab] = useState("activities");
  const hasReport = useStore((state) =>
    researchId ? state.researchReportIds.has(researchId) : false,
  );
  const reportStreaming = useStore((state) =>
    reportId ? (state.messages.get(reportId)?.isStreaming ?? false) : false,
  );
  const { isReplay } = useReplay();
  const [editing, setEditing] = useState(false);
  const [copied, setCopied] = useState(false);

  // 2. Include all useEffect hooks unconditionally
  useEffect(() => {
    if (hasReport) {
      setActiveTab("report");
    }
  }, [hasReport]);

  // When the research id changes, set the active tab to activities
  useEffect(() => {
    if (!hasReport) {
      setActiveTab("activities");
    }
  }, [hasReport, researchId]);

  // 3. Define all callback functions with useCallback
  const handleGeneratePodcast = useCallback(async () => {
    if (!researchId) {
      return;
    }
    await listenToPodcast(researchId);
  }, [researchId]);

  const handleCopy = useCallback(() => {
    if (!reportId) {
      return;
    }
    const report = useStore.getState().messages.get(reportId);
    if (!report) {
      return;
    }
    void navigator.clipboard.writeText(report.content);
    setCopied(true);
    setTimeout(() => {
      setCopied(false);
    }, 1000);
  }, [reportId]);

  const handleEdit = useCallback(() => {
    setEditing((editing) => !editing);
  }, []);

  // 4. Render with conditional content rather than conditional return
  return (
    <div className={cn("h-full w-full", className)}>
      {researchId === null ? (
        // Empty container when no research ID
        <div />
      ) : (
        <Card className={cn("relative h-full w-full pt-4", className)}>
          <div className="absolute right-4 flex h-9 items-center justify-center">
            {hasReport && !reportStreaming && (
              <>
                <Tooltip title="Generate podcast">
                  <Button
                    className="text-gray-400"
                    size="icon"
                    variant="ghost"
                    disabled={isReplay}
                    onClick={handleGeneratePodcast}
                  >
                    <Headphones />
                  </Button>
                </Tooltip>
                <Tooltip title="Edit">
                  <Button
                    className="text-gray-400"
                    size="icon"
                    variant="ghost"
                    disabled={isReplay}
                    onClick={handleEdit}
                  >
                    {editing ? <Undo2 /> : <Pencil />}
                  </Button>
                </Tooltip>
                <Tooltip title="Copy">
                  <Button
                    className="text-gray-400"
                    size="icon"
                    variant="ghost"
                    onClick={handleCopy}
                  >
                    {copied ? <Check /> : <Copy />}
                  </Button>
                </Tooltip>
              </>
            )}
            <Tooltip title="Close">
              <Button
                className="text-gray-400"
                size="sm"
                variant="ghost"
                onClick={() => {
                  closeResearch();
                }}
              >
                <X />
              </Button>
            </Tooltip>
          </div>
          <Tabs
            className="flex h-full w-full flex-col"
            value={activeTab}
            onValueChange={(value) => setActiveTab(value)}
          >
            <div className="flex w-full justify-center">
              <TabsList className="">
                <TabsTrigger
                  className="px-8"
                  value="report"
                  disabled={!hasReport}
                >
                  Report
                </TabsTrigger>
                <TabsTrigger className="px-8" value="activities">
                  Activities
                </TabsTrigger>
              </TabsList>
            </div>
            <TabsContent
              className="h-full min-h-0 flex-grow px-8"
              value="report"
              forceMount
              hidden={activeTab !== "report"}
            >
              <ScrollContainer
                className="px-5pb-20 h-full"
                scrollShadowColor="var(--card)"
                autoScrollToBottom={!hasReport || reportStreaming}
              >
                {reportId && researchId && (
                  <ResearchReportBlock
                    className="mt-4"
                    researchId={researchId}
                    messageId={reportId}
                    editing={editing}
                  />
                )}
              </ScrollContainer>
            </TabsContent>
            <TabsContent
              className="h-full min-h-0 flex-grow px-8"
              value="activities"
              forceMount
              hidden={activeTab !== "activities"}
            >
              <ScrollContainer
                className="h-full"
                scrollShadowColor="var(--card)"
                autoScrollToBottom={!hasReport || reportStreaming}
              >
                {researchId && (
                  <ResearchActivitiesBlock
                    className="mt-4"
                    researchId={researchId}
                  />
                )}
              </ScrollContainer>
            </TabsContent>
          </Tabs>
        </Card>
      )}
    </div>
  );
}
