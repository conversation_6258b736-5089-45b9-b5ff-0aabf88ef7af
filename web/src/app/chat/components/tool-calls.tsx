// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { ChevronDown, ChevronRight, Code, Globe, Terminal, Wrench, Zap } from "lucide-react";
import { memo, useState } from "react";

import { Markdown } from "~/components/deer-flow/markdown";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "~/components/ui/collapsible";
import type { ToolCallRuntime } from "~/core/messages/types";
import { cn } from "~/lib/utils";

interface ToolCallProps {
  toolCall: ToolCallRuntime;
  className?: string;
}

// 通用的工具调用组件基础结构
const ToolCallBase = memo(function ToolCallBase({
  toolCall,
  className,
  icon,
  title,
  description,
  bgColor = "bg-transparent",
  borderColor = "border-gray-200 dark:border-gray-700",
  textColor = "text-gray-700 dark:text-gray-400",
  titleColor = "text-gray-900 dark:text-gray-200",
}: {
  toolCall: ToolCallRuntime;
  className?: string;
  icon: React.ReactNode;
  title: string;
  description?: string;
  bgColor?: string;
  borderColor?: string;
  textColor?: string;
  titleColor?: string;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const hasResult = !!toolCall.result;

  return (
    <div className={cn(
      "rounded-lg border p-3 transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-800/50", 
      bgColor, 
      borderColor, 
      className
    )}>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger className={cn(
          "flex w-full items-center justify-between text-left transition-colors duration-200",
          hasResult && "hover:opacity-90 cursor-pointer",
          !hasResult && "cursor-default"
        )}>
          <div className="flex items-center gap-2">
            {icon}
            <div className="flex flex-col gap-1">
              <span className={cn("text-sm font-medium", titleColor)}>{title}</span>
              {description && (
                <span className={cn("text-xs break-all whitespace-pre-wrap", textColor)}>{description}</span>
              )}
            </div>
          </div>
          {hasResult && (
            <div className="flex items-center gap-2">
              <span className={cn("text-xs", textColor)}>
                {isOpen ? "收起结果" : "查看结果"}
              </span>
              <div className="transition-transform duration-200">
                {isOpen ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </div>
            </div>
          )}
        </CollapsibleTrigger>
        
        {hasResult && (
          <CollapsibleContent className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-200">
            <div className={cn("text-sm", textColor)}>
              <Markdown animated={false} checkLinkCredibility>
                {toolCall.result}
              </Markdown>
            </div>
          </CollapsibleContent>
        )}
      </Collapsible>
    </div>
  );
});

// Web搜索工具调用组件
export const WebSearchToolCall = memo(function WebSearchToolCall({ 
  toolCall, 
  className 
}: ToolCallProps) {
  const query = toolCall.args?.query as string;
  
  return (
    <ToolCallBase
      toolCall={toolCall}
      className={className}
      icon={<Globe className="h-4 w-4 text-blue-600 dark:text-blue-500" />}
      title="网页搜索"
      description={query ? `搜索: "${query}"` : undefined}
      bgColor="bg-transparent"
      borderColor="border-gray-200 dark:border-gray-700"
      textColor="text-blue-700 dark:text-blue-400"
      titleColor="text-blue-900 dark:text-blue-200"
    />
  );
});

// 爬虫工具调用组件
export const CrawlToolCall = memo(function CrawlToolCall({ 
  toolCall, 
  className 
}: ToolCallProps) {
  const url = toolCall.args?.url as string;
  
  return (
    <ToolCallBase
      toolCall={toolCall}
      className={className}
      icon={<Wrench className="h-4 w-4 text-green-600 dark:text-green-500" />}
      title="网页爬取"
      description={url ? `爬取: ${url}` : undefined}
      bgColor="bg-transparent"
      borderColor="border-gray-200 dark:border-gray-700"
      textColor="text-green-700 dark:text-green-400"
      titleColor="text-green-900 dark:text-green-200"
    />
  );
});

// Python执行工具调用组件
export const PythonToolCall = memo(function PythonToolCall({ 
  toolCall, 
  className 
}: ToolCallProps) {
  const code = toolCall.args?.code as string;
  
  return (
    <ToolCallBase
      toolCall={toolCall}
      className={className}
      icon={<Terminal className="h-4 w-4 text-purple-600 dark:text-purple-500" />}
      title="Python执行"
      description={code ? `代码:\n${code}` : undefined}
      bgColor="bg-transparent"
      borderColor="border-gray-200 dark:border-gray-700"
      textColor="text-purple-700 dark:text-purple-400"
      titleColor="text-purple-900 dark:text-purple-200"
    />
  );
});

// 内置工具调用组件
export const BuiltInToolCall = memo(function BuiltInToolCall({ 
  toolCall, 
  className 
}: ToolCallProps) {
  const getToolIcon = (toolName: string) => {
    switch (toolName) {
      case 'search_by_docchain_llm':
      case 'search_code_by_docchain':
      case 'search_doc_by_docchain':
        return <Globe className="h-4 w-4 text-orange-600 dark:text-orange-500" />;
      case 'file_read_by_docchain':
      case 'search_file_by_name':
        return <Code className="h-4 w-4 text-orange-600 dark:text-orange-500" />;
      default:
        return <Wrench className="h-4 w-4 text-orange-600 dark:text-orange-500" />;
    }
  };

  const getToolDisplayName = (toolName: string) => {
    const nameMap: Record<string, string> = {
      'search_by_docchain_llm': '智能搜索',
      'search_code_by_docchain': '代码搜索',
      'search_doc_by_docchain': '文档搜索', 
      'file_read_by_docchain': '文件读取',
      'search_file_by_name': '文件名搜索',
      'java_method_call_chain': 'Java方法调用链',
    };
    return nameMap[toolName] ?? toolName;
  };

  const formatArgs = (args: Record<string, unknown>) => {
    return Object.entries(args)
      .map(([key, value]) => `${key}: ${String(value)}`)
      .join('\n');
  };

  const description = Object.keys(toolCall.args).length > 0 
    ? `参数:\n${formatArgs(toolCall.args)}` 
    : undefined;
  
  return (
    <ToolCallBase
      toolCall={toolCall}
      className={className}
      icon={getToolIcon(toolCall.name)}
      title={getToolDisplayName(toolCall.name)}
      description={description}
      bgColor="bg-transparent"
      borderColor="border-gray-200 dark:border-gray-700"
      textColor="text-orange-700 dark:text-orange-400"
      titleColor="text-orange-900 dark:text-orange-200"
    />
  );
});

// MCP工具调用组件
export const MCPToolCall = memo(function MCPToolCall({ 
  toolCall, 
  className 
}: ToolCallProps) {
  const formatArgs = (args: Record<string, unknown>) => {
    return Object.entries(args)
      .map(([key, value]) => `${key}: ${String(value)}`)
      .join('\n');
  };

  const description = Object.keys(toolCall.args).length > 0 
    ? `参数:\n${formatArgs(toolCall.args)}` 
    : undefined;
  
  return (
    <ToolCallBase
      toolCall={toolCall}
      className={className}
      icon={<Zap className="h-4 w-4 text-cyan-600 dark:text-cyan-500" />}
      title={`MCP工具: ${toolCall.name}`}
      description={description}
      bgColor="bg-transparent"
      borderColor="border-gray-200 dark:border-gray-700"
      textColor="text-cyan-700 dark:text-cyan-400"
      titleColor="text-cyan-900 dark:text-cyan-200"
    />
  );
}); 