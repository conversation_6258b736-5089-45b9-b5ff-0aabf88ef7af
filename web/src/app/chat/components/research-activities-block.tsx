// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { motion } from "framer-motion";
import { LRUCache } from "lru-cache";
import { Zap } from "lucide-react";
import { memo, useMemo } from "react";

import { LoadingAnimation } from "~/components/deer-flow/loading-animation";
import { Markdown } from "~/components/deer-flow/markdown";
import { isBuiltInTool } from "~/core/mcp/utils";
import { useMessage, useStore } from "~/core/store";
import { cn } from "~/lib/utils";

import {
  BuiltInToolCall,
  CrawlToolCall,
  MCPToolCall,
  PythonToolCall,
  WebSearchToolCall,
} from "./tool-calls";

export const ResearchActivitiesBlock = memo(function ResearchActivitiesBlock({
  className,
  researchId,
}: {
  className?: string;
  researchId: string;
}) {
  const activityIds = useStore((state) =>
    state.researchActivityIds.get(researchId),
  )!;
  const ongoing = useStore((state) => state.ongoingResearchId === researchId);

  // 优化：缓存过滤后的活动ID列表
  const filteredActivityIds = useMemo(() => 
    activityIds.slice(1), // 跳过第一个元素
    [activityIds]
  );

  return (
    <>
      <ul className={cn("flex flex-col py-4", className)}>
        {filteredActivityIds.map((activityId, i) => (
          <ActivityListItemWrapper
            key={activityId}
            activityId={activityId}
            index={i}
            isLast={i === filteredActivityIds.length - 1}
          />
        ))}
      </ul>
      {ongoing && <LoadingAnimation className="mx-4 my-12" />}
    </>
  );
});

// 优化：单独的包装组件，减少重新渲染
const ActivityListItemWrapper = memo(function ActivityListItemWrapper({
  activityId,
  index: _index,
  isLast,
}: {
  activityId: string;
  index: number;
  isLast: boolean;
}) {
  const motionProps = useMemo(() => ({
    initial: { opacity: 0, y: 24 },
    animate: { opacity: 1, y: 0 },
    transition: {
      duration: 0.4,
      ease: "easeOut" as const,
    },
    style: { transition: "all 0.4s ease-out" },
  }), []);

  return (
    <motion.li key={activityId} {...motionProps}>
      <ActivityMessage messageId={activityId} />
      <ActivityListItem messageId={activityId} />
      {!isLast && <hr className="my-8" />}
    </motion.li>
  );
});

// 优化：进一步优化ActivityMessage组件性能
const ActivityMessage = memo(function ActivityMessage({ messageId }: { messageId: string }) {
  const message = useMessage(messageId);
  
  // 优化：更精确的渲染条件判断，特别针对reporter和planner
  const shouldRender = useMemo(() => {
    if (!message?.agent || !message.content) {
      return false;
    }
    
    // 优化：明确排除reporter和planner，它们由其他组件处理
    const excludedAgents = ["reporter", "planner"];
    return !excludedAgents.includes(message.agent);
  }, [message?.agent, message?.content]);

  // 优化：预计算Markdown内容以避免重复渲染
  const markdownContent = useMemo(() => {
    if (!shouldRender || !message?.content) {
      return null;
    }
    
    return (
      <Markdown 
        animated={false} // 优化：禁用动画以提升性能
        checkLinkCredibility
      >
        {message.content}
      </Markdown>
    );
  }, [shouldRender, message?.content]);

  if (!shouldRender) {
    return null;
  }

  return (
    <div className="px-4 py-2">
      {markdownContent}
    </div>
  );
});

// 优化：进一步优化ActivityListItem组件，特别针对工具调用的性能
const ActivityListItem = memo(function ActivityListItem({ messageId }: { messageId: string }) {
  const message = useMessage(messageId);
  
  // 优化：改进工具调用组件的判断逻辑和性能
  const toolCallComponent = useMemo(() => {
    // 修复：确保消息和工具调用都存在
    if (!message?.toolCalls?.length) {
      return null;
    }

    const toolCall = message.toolCalls[0];
    if (!toolCall?.name) {
      return null;
    }

    // 移除console.log以提升性能

    // 根据工具类型选择合适的组件
    switch (toolCall.name) {
      case "web_search":
        return <WebSearchToolCall key={toolCall.id} toolCall={toolCall} />;
      case "crawl_tool":
        return <CrawlToolCall key={toolCall.id} toolCall={toolCall} />;
      case "python_repl_tool":
        return <PythonToolCall key={toolCall.id} toolCall={toolCall} />;
      default:
        // 检查是否为内置工具
        if (isBuiltInTool(toolCall.name)) {
          return <BuiltInToolCall key={toolCall.id} toolCall={toolCall} />;
        }
        // 其他工具（主要是MCP工具）使用MCP组件
        return <MCPToolCall key={toolCall.id} toolCall={toolCall} />;
    }
  }, [message, messageId]);

  // 如果没有工具调用，则不渲染此项
  if (!toolCallComponent) {
    return null;
  }

  return (
    <div className="px-4 py-2">
      {toolCallComponent}
    </div>
  );
});

// 优化：增加页面缓存大小，特别考虑reporter和planner的大型内容
const __pageCache = new LRUCache<string, string>({ max: 200 }); // 增加缓存大小

// 优化：预定义常用的Zap图标组件以避免重复创建
const ZapIcon = memo(function ZapIcon() {
  return <Zap size={14} className="text-blue-500" />;
});

// 移除页面缓存监控以提升性能，在需要时可通过getPageCacheStats()查看状态

// 优化：提供缓存清理功能
export function clearPageCache() {
  __pageCache.clear();
  // 移除console.log以提升性能
}

// 优化：添加内存使用统计
export function getPageCacheStats() {
  return {
    size: __pageCache.size,
    max: __pageCache.max,
    calculatedSize: __pageCache.calculatedSize,
  };
}
