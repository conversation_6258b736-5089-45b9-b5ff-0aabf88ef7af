// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { AnimatePresence, motion } from "framer-motion";
import { ArrowUp, X } from "lucide-react";
import React, { useCallback, useRef } from "react";

import { Detective } from "~/components/deer-flow/icons/detective";
import MessageInput, {
  type MessageInputRef,
} from "~/components/deer-flow/message-input";
import { Tooltip } from "~/components/deer-flow/tooltip";
import { Button } from "~/components/ui/button";
import type { Option, Resource } from "~/core/messages";
import {
  setEnableBackgroundInvestigation,
  useSettingsStore,
} from "~/core/store";
import { useDebugStore } from "~/core/store/debug-store";
import { cn } from "~/lib/utils";

import type { ScenarioConfig } from "./advanced-debug/types";
import { ScenarioSelector } from "./scenario-selector";

export function InputBox({
  className,
  responding,
  feedback,
  onSend,
  onCancel,
  onRemoveFeedback,
}: {
  className?: string;
  size?: "large" | "normal";
  responding?: boolean;
  feedback?: { option: Option } | null;
  onSend?: (
    message: string,
    options?: {
      interruptFeedback?: string;
      resources?: Array<Resource>;
      scenarioConfig?: ScenarioConfig;
      scenarioName?: string;
    },
  ) => void;
  onCancel?: () => void;
  onRemoveFeedback?: () => void;
}) {
  const backgroundInvestigation = useSettingsStore(
    (state) => state.general.enableBackgroundInvestigation,
  );
  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<MessageInputRef>(null);
  const feedbackRef = useRef<HTMLDivElement>(null);
  
  // 使用debug store中的活跃场景，确保与PlanCard一致
  const activeScenario = useDebugStore((state) => state.getActiveScenario());
  const setActiveScenario = useDebugStore((state) => state.setActiveScenario);

  const handleSendMessage = useCallback(
    (message: string, resources: Array<Resource>) => {
      if (responding) {
        onCancel?.();
      } else {
        if (message.trim() === "") {
          return;
        }
        if (onSend) {
          // 使用debug store中的活跃场景
          onSend(message, {
            interruptFeedback: feedback?.option.value,
            resources,
            scenarioConfig: activeScenario ?? undefined,
            scenarioName: activeScenario?.name ?? undefined,
          });
          
          onRemoveFeedback?.();
        }
      }
    },
    [responding, onCancel, onSend, feedback, onRemoveFeedback, activeScenario],
  );

  const handleScenarioSelect = useCallback((scenario: ScenarioConfig) => {
    // 检查是否是清除操作（空对象）
    if (!scenario.id || !scenario.name) {
      setActiveScenario(null);
      return;
    }
    
    // 如果点击已选择的场景，则取消选择；否则选择该场景
    if (activeScenario?.id === scenario.id) {
      setActiveScenario(null);
    } else {
      setActiveScenario(scenario.id);
    }
  }, [activeScenario, setActiveScenario]);

  const removeSelectedScenario = useCallback(() => {
    // 清空选择的场景
    setActiveScenario(null);
  }, [setActiveScenario]);

  return (
    <div
      className={cn(
        "bg-card relative flex h-full w-full flex-col rounded-[24px] border",
        className,
      )}
      ref={containerRef}
    >
      <div className="w-full relative">
        <AnimatePresence>
          {feedback && (
            <motion.div
              ref={feedbackRef}
              className="bg-background border-brand absolute top-0 left-0 mt-3 ml-2 flex items-center justify-center gap-1 rounded-2xl border px-2 py-0.5 z-10"
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0 }}
              transition={{ duration: 0.2, ease: "easeInOut" }}
            >
              <div className="text-brand flex h-full w-full items-center justify-center text-sm opacity-90">
                {feedback.option.text}
              </div>
              <X
                className="cursor-pointer opacity-60"
                size={16}
                onClick={onRemoveFeedback}
              />
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* 场景标签 - 绝对定位在输入框内部顶部 */}
        {activeScenario && (
          <div className="absolute top-2 left-4 right-4 z-10">
            <div className="flex flex-wrap gap-1">
              <div className="inline-flex items-center gap-1 bg-primary/10 text-primary text-xs px-2 py-1 rounded-full backdrop-blur-sm">
                <span>@{activeScenario.name}</span>
                <X
                  className="h-3 w-3 cursor-pointer hover:bg-primary/20 rounded-full"
                  onClick={removeSelectedScenario}
                />
              </div>
            </div>
          </div>
        )}
        
        <MessageInput
          className={cn(
            "h-24 px-4",
            activeScenario ? "pt-8" : "pt-3"
          )}
          ref={inputRef}
          onEnter={handleSendMessage}
        />
      </div>
      <div className="flex items-center px-4 py-2">
        <div className="flex grow gap-2">
          <ScenarioSelector 
            onSelectScenario={handleScenarioSelect}
            selectedScenarios={activeScenario ? [activeScenario] : []}
          />
          <Tooltip
            className="max-w-60"
            title={
              <div>
                <h3 className="mb-2 font-bold">
                  Investigation Mode: {backgroundInvestigation ? "On" : "Off"}
                </h3>
                <p>
                  When enabled, DeerFlow will perform a quick search before
                  planning. This is useful for researches related to ongoing
                  events and news.
                </p>
              </div>
            }
          >
            <Button
              className={cn(
                "rounded-2xl",
                backgroundInvestigation && "!border-brand !text-brand",
              )}
              variant="outline"
              size="lg"
              onClick={() =>
                setEnableBackgroundInvestigation(!backgroundInvestigation)
              }
            >
              <Detective /> Investigation
            </Button>
          </Tooltip>
        </div>
        <div className="flex shrink-0 items-center gap-2">
          <Tooltip title={responding ? "Stop" : "Send"}>
            <Button
              variant="outline"
              size="icon"
              className={cn("h-10 w-10 rounded-full")}
              onClick={() => inputRef.current?.submit()}
            >
              {responding ? (
                <div className="flex h-10 w-10 items-center justify-center">
                  <div className="bg-foreground h-4 w-4 rounded-sm opacity-70" />
                </div>
              ) : (
                <ArrowUp />
              )}
            </Button>
          </Tooltip>
        </div>
      </div>
    </div>
  );
}
