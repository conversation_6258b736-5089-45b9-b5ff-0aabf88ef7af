// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

"use client";

import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import { Badge } from "~/components/ui/badge";
import { getProjectInfo } from "~/core/api/project";
import type { ProjectInfo } from "~/core/api/project";

export function ProjectIndicator() {
  const searchParams = useSearchParams();
  const project = searchParams.get("project");
  const [projectInfo, setProjectInfo] = useState<ProjectInfo | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!project) {
      return;
    }

    let cancelled = false;
    setLoading(true);

    getProjectInfo(project)
      .then((info) => {
        if (!cancelled) {
          setProjectInfo(info);
        }
      })
      .catch((error) => {
        console.error("Failed to fetch project info:", error);
        if (!cancelled) {
          setProjectInfo(null);
        }
      })
      .finally(() => {
        if (!cancelled) {
          setLoading(false);
        }
      });

    return () => {
      cancelled = true;
    };
  }, [project]);

  if (!project) {
    return null;
  }

  // 显示加载状态
  if (loading) {
    return (
      <Badge variant="secondary" className="ml-2">
        加载中...
      </Badge>
    );
  }

  // 使用从 API 获取的项目信息，如果获取失败则显示项目ID的大写形式
  const displayName = projectInfo?.name ?? project.toUpperCase();

  return (
    <Badge variant="secondary" className="ml-2">
      {displayName}
    </Badge>
  );
} 