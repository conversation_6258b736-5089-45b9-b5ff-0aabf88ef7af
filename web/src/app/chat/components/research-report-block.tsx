// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { memo, useCallback, useMemo, useRef } from "react";

import { LoadingAnimation } from "~/components/deer-flow/loading-animation";
import { Markdown } from "~/components/deer-flow/markdown";
import ReportEditor from "~/components/editor";
import { useReplay } from "~/core/replay";
import { useMessage, useStore } from "~/core/store";
import { cn } from "~/lib/utils";

// 优化：添加memo以防止不必要的重新渲染
export const ResearchReportBlock = memo(function ResearchReportBlock({
  className,
  messageId,
  editing,
}: {
  className?: string;
  researchId: string;
  messageId: string;
  editing: boolean;
}) {
  const message = useMessage(messageId);
  const { isReplay } = useReplay();
  
  // 优化：使用useCallback并减少依赖项以防止不必要的重新创建
  const handleMarkdownChange = useCallback(
    (markdown: string) => {
      if (message?.id) {
        // 优化：使用更高效的状态更新方式
        useStore.setState((state) => {
          const newMessages = new Map(state.messages);
          const currentMessage = newMessages.get(message.id);
          if (currentMessage) {
            newMessages.set(message.id, {
              ...currentMessage,
              content: markdown,
            });
          }
          return { messages: newMessages };
        });
      }
    },
    [message?.id], // 只依赖于消息ID
  );
  
  const contentRef = useRef<HTMLDivElement>(null);
  
  // 优化：缓存计算结果，减少重复计算
  const isCompleted = useMemo(() => 
    Boolean(message && !message.isStreaming && message.content),
    [message?.isStreaming, message?.content]
  );
  
  const shouldShowEditor = useMemo(() => 
    !isReplay && isCompleted && editing,
    [isReplay, isCompleted, editing]
  );
  
  const hasContent = useMemo(() => 
    Boolean(message?.content), 
    [message?.content]
  );
  
  const isLargeContent = useMemo(() => 
    hasContent && (message?.content?.length ?? 0) > 50000,
    [hasContent, message?.content?.length]
  );

  // 渲染逻辑：确保始终有一致的组件结构
  if (shouldShowEditor) {
    return (
      <div ref={contentRef} className={cn("w-full pt-4 pb-8", className)}>
        <ReportEditor
          content={message?.content ?? ""}
          onMarkdownChange={handleMarkdownChange}
        />
      </div>
    );
  }

  return (
    <div ref={contentRef} className={cn("w-full pt-4 pb-8", className)}>
      {!message ? (
        <div>Loading...</div>
      ) : !hasContent ? (
        <div>No content available</div>
      ) : (
        <>
          <Markdown 
            animated={false} // 禁用动画以提升性能
            checkLinkCredibility={!isLargeContent} // 大文档时禁用链接检查以提升性能
          >
            {message.content}
          </Markdown>
          {message.isStreaming && <LoadingAnimation className="my-12" />}
        </>
      )}
    </div>
  );
});
