"use client";

import { AtSign, ChevronDown } from "lucide-react";
import { useState } from "react";

import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { useDebugStore } from "~/core/store/debug-store";

import type { ScenarioConfig } from "./advanced-debug/types";
import { SCENARIO_CATEGORIES } from "./advanced-debug/types";

interface ScenarioSelectorProps {
  onSelectScenario: (scenario: ScenarioConfig) => void;
  selectedScenarios?: ScenarioConfig[];
}

export function ScenarioSelector({ onSelectScenario, selectedScenarios = [] }: ScenarioSelectorProps) {
  const { getEnabledScenarios } = useDebugStore();
  const [open, setOpen] = useState(false);
  
  const enabledScenarios = getEnabledScenarios();

  const handleScenarioSelect = (scenario: ScenarioConfig) => {
    onSelectScenario(scenario);
    // 只允许选择一个场景，选择后关闭下拉菜单
    setOpen(false);
  };

  const getCategoryInfo = (categoryId: string) => {
    return SCENARIO_CATEGORIES.find(cat => cat.id === categoryId);
  };

  // if (enabledScenarios.length === 0) {
  //   // 显示调试信息而不是隐藏组件
  //   return (
  //     <div className="flex items-center gap-2">
  //       <div className="text-xs text-red-500 border border-red-200 rounded px-2 py-1">
  //         调试: 无启用场景 (总数: {Object.keys(config.scenarioLibrary.scenarios).length})
  //       </div>
  //       <Button
  //         variant="outline"
  //         size="sm"
  //         className="text-xs h-6"
  //         onClick={() => {
  //           console.log('重置调试存储到默认状态');
  //           useDebugStore.getState().resetToDefaults();
  //           window.location.reload();
  //         }}
  //       >
  //         重置
  //       </Button>
  //     </div>
  //   );
  // }

  return (
    <div onKeyDown={(e) => {
      // 阻止所有键盘事件冒泡，避免干扰消息输入
      if (e.key === 'Enter') {
        e.stopPropagation();
        e.preventDefault();
      }
    }}>
      <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="h-8 gap-1 rounded-full"
          onKeyDown={(e) => {
            // 阻止回车键和空格键的默认行为，避免干扰消息发送
            if (e.key === 'Enter' || e.key === ' ') {
              e.stopPropagation();
              // 如果是回车键，也阻止默认行为
              if (e.key === 'Enter') {
                e.preventDefault();
              }
            }
          }}
        >
          <AtSign className="h-3 w-3" />
          {selectedScenarios.length > 0 ? selectedScenarios[0]?.name : '场景'}
          <ChevronDown className="h-3 w-3" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        align="start" 
        className="w-80"
        onKeyDown={(e) => {
          // 阻止回车键事件冒泡到父组件
          if (e.key === 'Enter') {
            e.stopPropagation();
          }
        }}
      >
        <div className="px-3 py-2 text-sm font-medium text-muted-foreground flex items-center justify-between">
          <span>选择分析场景 ({enabledScenarios.length} 个已启用)</span>
          {selectedScenarios.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 px-2 text-xs"
              onClick={() => {
                // 清除选择：传递空对象作为清除标识
                onSelectScenario({} as ScenarioConfig);
              }}
            >
              清除
            </Button>
          )}
        </div>
        <DropdownMenuSeparator />
        
        {enabledScenarios.map((scenario) => {
          const categoryInfo = getCategoryInfo(scenario.metadata.category);
          const isSelected = selectedScenarios.some(s => s.id === scenario.id);
          
          return (
            <DropdownMenuItem
              key={scenario.id}
              className="p-3 focus:bg-accent cursor-pointer"
              onClick={() => handleScenarioSelect(scenario)}
            >
              <div className="flex flex-col gap-2 w-full">
                <div className="flex items-center gap-2">
                  {categoryInfo && (
                    <span className="text-base">{categoryInfo.icon}</span>
                  )}
                  <span className="font-medium">{scenario.name}</span>
                  {scenario.isPreset && (
                    <Badge variant="secondary" className="text-xs">预置</Badge>
                  )}
                  {isSelected && (
                    <Badge variant="default" className="text-xs">已选</Badge>
                  )}
                </div>
                <p className="text-xs text-muted-foreground line-clamp-2">
                  {scenario.description}
                </p>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <span>包含节点:</span>
                  <span className="px-1 bg-green-100 text-green-700 rounded">Planner</span>
                  <span className="px-1 bg-blue-100 text-blue-700 rounded">Researcher</span>
                  <span className="px-1 bg-purple-100 text-purple-700 rounded">Reporter</span>
                  <span className="px-1 bg-orange-100 text-orange-700 rounded">Coder</span>
                </div>
              </div>
            </DropdownMenuItem>
          );
        })}
        
        {enabledScenarios.length === 0 && (
          <div className="px-3 py-4 text-center text-sm text-muted-foreground">
            暂无启用的场景
            <br />
            请在场景库中启用场景
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
    </div>
  );
} 