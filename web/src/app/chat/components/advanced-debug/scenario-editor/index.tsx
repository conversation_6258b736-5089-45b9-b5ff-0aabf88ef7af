"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import {
    AlertTriangle,
    Clock,
    Code,
    Edit3,
    FileText,
    Layers,
    RotateCcw,
    Save,
    Search,
    Settings,
    User
} from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Tooltip } from "~/components/deer-flow/tooltip";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Textarea } from "~/components/ui/textarea";
import { useDebugStore } from "~/core/store/debug-store";

import { SCENARIO_CATEGORIES } from "../types";

import { CoderNodeEditor, type CoderNodeEditorRef } from "./coder-node-editor";
import { PlannerNodeEditor, type PlannerNodeEditorRef } from "./planner-node-editor";
import { ReporterNodeEditor, type ReporterNodeEditorRef } from "./reporter-node-editor";
import { ResearcherNodeEditor, type ResearcherNodeEditorRef } from "./researcher-node-editor";

// 场景基本信息表单验证
const scenarioBasicSchema = z.object({
  name: z.string().min(1, "场景名称不能为空").max(50, "场景名称不能超过50个字符"),
  description: z.string().min(1, "场景描述不能为空").max(200, "场景描述不能超过200个字符"),
  category: z.string().min(1, "请选择场景分类"),
  estimatedTime: z.number().min(1).max(300),
  difficulty: z.enum(['simple', 'medium', 'complex']),
});

type ScenarioBasicFormData = z.infer<typeof scenarioBasicSchema>;

export function ScenarioEditor() {
  const {
    ui,
    getActiveScenario,
    updateScenario,
    setEditorState,
    validateConfig,
  } = useDebugStore();

  const [activeTab, setActiveTab] = useState("basic");
  const [isSaving, setIsSaving] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // 子组件引用
  const plannerRef = useRef<PlannerNodeEditorRef>(null);
  const researcherRef = useRef<ResearcherNodeEditorRef>(null);
  const reporterRef = useRef<ReporterNodeEditorRef>(null);
  const coderRef = useRef<CoderNodeEditorRef>(null);
  
  // 标记表单变化的回调函数
  const handleFormChange = useCallback(() => {
    setEditorState({ hasUnsavedChanges: true });
  }, [setEditorState]);

  const activeScenario = getActiveScenario();
  const isEditing = ui.editor.isEditing;
  const hasUnsavedChanges = ui.editor.hasUnsavedChanges;
  
  // 检查是否为预置场景
  const isPresetScenario = activeScenario?.isPreset ?? false;

  // 基本信息表单
  const basicForm = useForm<ScenarioBasicFormData>({
    resolver: zodResolver(scenarioBasicSchema),
    defaultValues: {
      name: activeScenario?.name ?? '',
      description: activeScenario?.description ?? '',
      category: activeScenario?.metadata.category ?? '',
      estimatedTime: activeScenario?.metadata.estimatedTime ?? 30,
      difficulty: activeScenario?.metadata.difficulty ?? 'medium',
    },
  });

  // 当活跃场景变化时更新表单
  useEffect(() => {
    if (activeScenario) {
      basicForm.reset({
        name: activeScenario.name,
        description: activeScenario.description,
        category: activeScenario.metadata.category,
        estimatedTime: activeScenario.metadata.estimatedTime,
        difficulty: activeScenario.metadata.difficulty,
      });
    }
  }, [activeScenario, basicForm]);

  // 验证配置
  useEffect(() => {
    if (activeScenario) {
      const errors = validateConfig(activeScenario.id);
      setValidationErrors(errors);
      setEditorState({ validationErrors: errors });
    }
  }, [activeScenario, validateConfig, setEditorState]);

  // 监听基础信息表单变化
  useEffect(() => {
    const subscription = basicForm.watch(() => {
      if (activeTab === 'basic') {
        setEditorState({ hasUnsavedChanges: true });
      }
    });
    return () => subscription.unsubscribe();
  }, [basicForm, activeTab, setEditorState]);

  // 保存基本信息
  const handleSaveBasic = useCallback(async (data: ScenarioBasicFormData) => {
    if (!activeScenario) return;

    console.log('[Debug] Saving basic form data:', data);
    setIsSaving(true);
    try {
      updateScenario(activeScenario.id, {
        name: data.name,
        description: data.description,
        metadata: {
          ...activeScenario.metadata,
          category: data.category,
          estimatedTime: data.estimatedTime,
          difficulty: data.difficulty,
          updatedAt: new Date(),
        },
      });

      // 使用短暂延迟确保状态更新完成
      setTimeout(() => {
        setEditorState({ hasUnsavedChanges: false });
        console.log('[Debug] Basic form saved and state updated');
      }, 50);
    } catch (error) {
      console.error('Failed to save scenario:', error);
    } finally {
      setIsSaving(false);
    }
  }, [activeScenario, updateScenario, setEditorState]);

  // 重置表单
  const handleReset = useCallback(() => {
    if (activeScenario) {
      basicForm.reset({
        name: activeScenario.name,
        description: activeScenario.description,
        category: activeScenario.metadata.category,
        estimatedTime: activeScenario.metadata.estimatedTime,
        difficulty: activeScenario.metadata.difficulty,
      });
      setEditorState({ hasUnsavedChanges: false });
    }
  }, [activeScenario, basicForm, setEditorState]);

  // 统一保存处理 - 修复基本信息保存
  const handleUnifiedSave = useCallback(async () => {
    console.log(`[Debug] Starting unified save for tab: ${activeTab}`);
    setIsSaving(true);
    try {
      switch (activeTab) {
        case 'basic': {
          const isValid = await basicForm.trigger();
          if (isValid) {
            const data = basicForm.getValues();
            await handleSaveBasic(data);
          } else {
            console.log('[Debug] Form validation failed');
          }
          break;
        }
        case 'planner':
          if (plannerRef.current) {
            await plannerRef.current.save();
          }
          break;
        case 'researcher':
          if (researcherRef.current) {
            await researcherRef.current.save();
          }
          break;
        case 'reporter':
          if (reporterRef.current) {
            await reporterRef.current.save();
          }
          break;
        case 'coder':
          if (coderRef.current) {
            await coderRef.current.save();
          }
          break;
        case 'flow':
          // 流程控制尚未实现
          setEditorState({ hasUnsavedChanges: false });
          break;
        default:
          console.log(`[Debug] Unknown tab: ${activeTab}`);
      }
      
      // 只有在保存成功时才设置为未修改状态
      setEditorState({ hasUnsavedChanges: false });
    } catch (error) {
      console.error(`[Debug] Failed to save ${activeTab} config:`, error);
      // 保存失败时不改变 hasUnsavedChanges 状态
    } finally {
      setIsSaving(false);
    }
  }, [activeTab, basicForm, handleSaveBasic, setEditorState]);

  // 统一重置处理
  const handleUnifiedReset = useCallback(() => {
    console.log(`[Debug] Starting reset for tab: ${activeTab}`);
    switch (activeTab) {
      case 'basic':
        handleReset();
        console.log('[Debug] Basic form reset successfully');
        break;
      case 'planner':
        if (plannerRef.current) {
          plannerRef.current.reset();
        }
        break;
      case 'researcher':
        if (researcherRef.current) {
          researcherRef.current.reset();
        }
        break;
      case 'reporter':
        if (reporterRef.current) {
          reporterRef.current.reset();
        }
        break;
             case 'coder':
         if (coderRef.current) {
           coderRef.current.reset();
         }
         break;
             case 'flow':
        // 流程控制尚未实现
        break;
      default:
        console.log(`[Debug] Unknown tab: ${activeTab}`);
    }
    
    // 重置后统一设置为未修改状态
    setEditorState({ hasUnsavedChanges: false });
  }, [activeTab, handleReset, setEditorState]);

  // 如果没有活跃场景
  if (!activeScenario) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center space-y-4">
          <Edit3 className="h-12 w-12 text-muted-foreground mx-auto" />
          <div>
            <h3 className="text-lg font-medium text-muted-foreground">没有选择场景</h3>
            <p className="text-sm text-muted-foreground">请在场景库中选择一个场景进行编辑</p>
          </div>
        </div>
      </div>
    );
  }

  // 获取分类信息
  const categoryInfo = SCENARIO_CATEGORIES.find(cat => cat.id === activeScenario.metadata.category);

  return (
    <div className="h-full flex flex-col">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
        {/* 紧凑的标签页头部 */}
        <div className="border-b bg-background">
          <div className="flex items-center justify-between px-3 py-2">
            <TabsList className="grid w-auto grid-cols-6 h-8">
              <TabsTrigger value="basic" className="flex items-center gap-1.5 px-3 py-1 text-xs">
                <Settings className="h-3 w-3" />
                基本信息
              </TabsTrigger>
              <TabsTrigger value="planner" className="flex items-center gap-1.5 px-3 py-1 text-xs">
                <User className="h-3 w-3" />
                Planner
              </TabsTrigger>
              <TabsTrigger value="researcher" className="flex items-center gap-1.5 px-3 py-1 text-xs">
                <Search className="h-3 w-3" />
                Researcher
              </TabsTrigger>
              <TabsTrigger value="reporter" className="flex items-center gap-1.5 px-3 py-1 text-xs">
                <FileText className="h-3 w-3" />
                Reporter
              </TabsTrigger>
              <TabsTrigger value="coder" className="flex items-center gap-1.5 px-3 py-1 text-xs">
                <Code className="h-3 w-3" />
                Coder
              </TabsTrigger>
              <TabsTrigger value="flow" className="flex items-center gap-1.5 px-3 py-1 text-xs">
                <Layers className="h-3 w-3" />
                流程控制
              </TabsTrigger>
            </TabsList>

            <div className="flex items-center gap-2">
              {validationErrors.length > 0 && (
                <Tooltip title={`${validationErrors.length} 个配置错误`}>
                  <Badge variant="destructive" className="h-6 px-2 text-xs">
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    {validationErrors.length}
                  </Badge>
                </Tooltip>
              )}

              {hasUnsavedChanges && !isPresetScenario && (
                <>
                  <Badge variant="outline" className="h-6 px-2 text-xs text-orange-600 border-orange-300">
                    <Clock className="h-3 w-3 mr-1" />
                    未保存
                  </Badge>
                  {isEditing && (
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        onClick={handleUnifiedSave}
                        disabled={isSaving}
                        className="h-6 px-2 text-xs"
                      >
                        <Save className="h-3 w-3 mr-1" />
                        {isSaving ? '保存中...' : '保存更改'}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleUnifiedReset}
                        disabled={isSaving}
                        className="h-6 px-2 text-xs"
                      >
                        <RotateCcw className="h-3 w-3 mr-1" />
                        重置
                      </Button>
                    </div>
                  )}
                </>
              )}
              
              {isPresetScenario && (
                <Badge variant="secondary" className="h-6 px-2 text-xs text-blue-600 border-blue-300">
                  <Settings className="h-3 w-3 mr-1" />
                  只读模式
                </Badge>
              )}

              <Badge variant="secondary" className="h-6 px-2 text-xs">
                {categoryInfo?.icon} {categoryInfo?.name}
              </Badge>
            </div>
          </div>
        </div>

        {/* 内容区域 - 占用所有剩余空间 */}
        <div className="flex-1 overflow-hidden">
          <TabsContent value="basic" className="m-0 h-full">
            <div className="h-full overflow-auto">
              <div className="p-6 max-w-4xl">
                {isPresetScenario && (
                  <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-start gap-3">
                      <Settings className="h-5 w-5 text-blue-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-blue-900 mb-1">预置场景说明</h4>
                        <p className="text-sm text-blue-800 mb-2">
                          这是系统内置的预置场景，经过优化的配置和提示词，为特定分析任务提供最佳性能。
                        </p>
                        <p className="text-sm text-blue-800">
                          预置场景为只读模式，无法修改。如需自定义配置，请复制此场景后进行编辑。
                        </p>
                      </div>
                    </div>
                  </div>
                )}
                
                <Form {...basicForm}>
                  <form onSubmit={basicForm.handleSubmit(handleSaveBasic)} className="space-y-6">
                    {/* 基本信息 */}
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={basicForm.control}
                          name="name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>场景名称</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="输入场景名称..."
                                  {...field}
                                  disabled={!isEditing || isPresetScenario}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={basicForm.control}
                          name="category"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>场景分类</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value} disabled={!isEditing || isPresetScenario}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="选择场景分类" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {SCENARIO_CATEGORIES.map((category) => (
                                    <SelectItem key={category.id} value={category.id}>
                                      <div className="flex items-center gap-2">
                                        <span>{category.icon}</span>
                                        <span>{category.name}</span>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={basicForm.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>场景描述</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="描述这个场景的用途和特点..."
                                className="resize-none"
                                rows={4}
                                {...field}
                                disabled={!isEditing || isPresetScenario}
                              />
                            </FormControl>
                            <FormDescription>
                              详细描述场景的用途、适用场合等
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={basicForm.control}
                          name="estimatedTime"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>预计时间（分钟）</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="1"
                                  max="300"
                                  placeholder="30"
                                  {...field}
                                  onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                                  disabled={!isEditing || isPresetScenario}
                                />
                              </FormControl>
                              <FormDescription>
                                预计完成分析所需时间
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={basicForm.control}
                          name="difficulty"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>难度等级</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value} disabled={!isEditing || isPresetScenario}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="选择难度" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="simple">
                                    <div className="flex items-center gap-2">
                                      <div className="w-2 h-2 rounded-full bg-green-500"></div>
                                      简单
                                    </div>
                                  </SelectItem>
                                  <SelectItem value="medium">
                                    <div className="flex items-center gap-2">
                                      <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                                      中等
                                    </div>
                                  </SelectItem>
                                  <SelectItem value="complex">
                                    <div className="flex items-center gap-2">
                                      <div className="w-2 h-2 rounded-full bg-red-500"></div>
                                      复杂
                                    </div>
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                场景的复杂程度
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    {/* 操作按钮 */}
                    {validationErrors.length > 0 && (
                      <div className="bg-destructive/10 border border-destructive/20 rounded-md p-4 mt-6">
                        <div className="flex items-start gap-2 text-destructive">
                          <AlertTriangle className="h-4 w-4 mt-0.5 shrink-0" />
                          <div className="space-y-1">
                            <div className="text-sm font-medium">配置验证失败</div>
                            <ul className="text-xs space-y-1">
                              {validationErrors.map((error, index) => (
                                <li key={index}>• {error}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    )}
                  </form>
                </Form>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="planner" className="m-0 h-full">
            <PlannerNodeEditor 
              ref={plannerRef} 
              scenario={activeScenario} 
              isEditing={isEditing}
              onFormChange={activeTab === 'planner' ? handleFormChange : undefined}
            />
          </TabsContent>

          <TabsContent value="researcher" className="m-0 h-full">
            <ResearcherNodeEditor 
              ref={researcherRef} 
              scenario={activeScenario} 
              isEditing={isEditing}
              onFormChange={activeTab === 'researcher' ? handleFormChange : undefined}
            />
          </TabsContent>

          <TabsContent value="reporter" className="m-0 h-full">
            <ReporterNodeEditor 
              ref={reporterRef} 
              scenario={activeScenario} 
              isEditing={isEditing}
              onFormChange={activeTab === 'reporter' ? handleFormChange : undefined}
            />
          </TabsContent>

          <TabsContent value="coder" className="m-0 h-full">
            <CoderNodeEditor 
              ref={coderRef} 
              scenario={activeScenario} 
              isEditing={isEditing}
              onFormChange={activeTab === 'coder' ? handleFormChange : undefined}
            />
          </TabsContent>

          <TabsContent value="flow" className="m-0 h-full">
            <div className="h-full overflow-auto">
              <div className="p-6 max-w-4xl">
                <div className="text-center py-12">
                  <Layers className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-xl font-medium text-muted-foreground mb-2">流程控制</h3>
                  <p className="text-muted-foreground mb-4">
                    配置节点间的执行流程和依赖关系
                  </p>
                  <div className="text-sm text-muted-foreground">
                    流程控制界面开发中...
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
} 