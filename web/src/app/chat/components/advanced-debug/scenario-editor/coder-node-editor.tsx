"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import {
  Code,
  Settings,
  MessageSquare,
  Terminal,
} from "lucide-react";
import React, { useState, useCallback, useEffect, forwardRef, useImperativeHandle, useMemo } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Checkbox } from "~/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Switch as UISwitch } from "~/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Textarea } from "~/components/ui/textarea";
import { useDebugStore } from "~/core/store/debug-store";

import type { ScenarioConfig, CoderNodeConfig } from "../types";

import { FormSection } from "./components/form-section";
import { ValidationErrorPanel } from "./components/validation-error-panel";

// Coder配置表单验证 - 简化版本
const coderConfigSchema = z.object({
  primaryLanguage: z.enum(['python', 'java', 'javascript', 'typescript', 'auto']),
  enableAutoDetection: z.boolean(),
  systemPrompt: z.string().min(10, "系统提示词至少需要10个字符"),
  enabledTools: z.array(z.string()),
  outputFormat: z.enum(['markdown', 'json', 'plain']),
  includeCodeComments: z.boolean(),
  includeTestCases: z.boolean(),
  includeDocumentation: z.boolean(),
  enableStaticAnalysis: z.boolean(),
  performanceAnalysis: z.boolean(),
  securityCheck: z.boolean(),
  codeQuality: z.boolean(),
});

type CoderConfigFormData = z.infer<typeof coderConfigSchema>;

// 可用的编程工具 - 简化版本
const CODER_TOOLS = [
  {
    id: 'python_repl',
    name: 'Python REPL',
    description: 'Python代码执行环境',
  },
  {
    id: 'code_search',
    name: '代码搜索',
    description: '搜索代码仓库中的代码片段',
  },
  {
    id: 'doc_search',
    name: '文档搜索',
    description: '搜索相关技术文档',
  },
  {
    id: 'file_read',
    name: '文件读取',
    description: '读取和分析文件内容',
  },
];

// 支持的编程语言
const SUPPORTED_LANGUAGES = [
  { value: 'auto', label: '自动检测' },
  { value: 'python', label: 'Python' },
  { value: 'java', label: 'Java' },
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' },
];

interface CoderNodeEditorProps {
  scenario: ScenarioConfig;
  isEditing: boolean;
  onFormChange?: () => void;
}

export interface CoderNodeEditorRef {
  save: () => Promise<void>;
  reset: () => void;
}

export const CoderNodeEditor = forwardRef<CoderNodeEditorRef, CoderNodeEditorProps>(
  ({ scenario, isEditing, onFormChange }, ref) => {
    const { updateCoderConfig } = useDebugStore();
    const [activeTab, setActiveTab] = useState<string>('config');
    const [validationErrors, setValidationErrors] = useState<string[]>([]);

    // 获取配置 - 使用useMemo包装以避免依赖警告
    const coderConfig = useMemo(() => scenario?.nodes.coder ?? {
      languages: {
        primary: 'auto' as const,
        supportedLanguages: ['python', 'java', 'javascript', 'typescript'],
        enableAutoDetection: true,
      },
      prompts: {
        systemPrompt: '你是一个专业的代码分析师和开发者，负责分析代码、实现功能、编写测试用例。',
      },
      tools: {
        enabled: ['python_repl', 'code_search', 'doc_search'],
        configs: {},
      },
      output: {
        format: 'markdown' as const,
        includeCodeComments: true,
        includeTestCases: true,
        includeDocumentation: true,
        debugOutput: false,
      },
      analysisStrategy: {
        enableStaticAnalysis: true,
        performanceAnalysis: false,
        securityCheck: true,
        codeQuality: true,
      },
      scenarioParams: {},
    }, [scenario?.nodes.coder]);

    // 移除未使用的表单状态变量

    // 配置表单
    const configForm = useForm<CoderConfigFormData>({
      resolver: zodResolver(coderConfigSchema),
      defaultValues: {
        primaryLanguage: coderConfig.languages.primary,
        enableAutoDetection: coderConfig.languages.enableAutoDetection,
        systemPrompt: coderConfig.prompts.systemPrompt,
        enabledTools: coderConfig.tools.enabled || [],
        outputFormat: coderConfig.output.format,
        includeCodeComments: coderConfig.output.includeCodeComments,
        includeTestCases: coderConfig.output.includeTestCases,
        includeDocumentation: coderConfig.output.includeDocumentation,
        enableStaticAnalysis: coderConfig.analysisStrategy.enableStaticAnalysis,
        performanceAnalysis: coderConfig.analysisStrategy.performanceAnalysis,
        securityCheck: coderConfig.analysisStrategy.securityCheck,
        codeQuality: coderConfig.analysisStrategy.codeQuality,
      },
    });

    // 当场景变化时更新表单
    useEffect(() => {
      configForm.reset({
        primaryLanguage: coderConfig.languages.primary,
        enableAutoDetection: coderConfig.languages.enableAutoDetection,
        systemPrompt: coderConfig.prompts.systemPrompt,
        enabledTools: coderConfig.tools.enabled || [],
        outputFormat: coderConfig.output.format,
        includeCodeComments: coderConfig.output.includeCodeComments,
        includeTestCases: coderConfig.output.includeTestCases,
        includeDocumentation: coderConfig.output.includeDocumentation,
        enableStaticAnalysis: coderConfig.analysisStrategy.enableStaticAnalysis,
        performanceAnalysis: coderConfig.analysisStrategy.performanceAnalysis,
        securityCheck: coderConfig.analysisStrategy.securityCheck,
        codeQuality: coderConfig.analysisStrategy.codeQuality,
      });
    }, [coderConfig, configForm]);

    // 监听表单变化
    useEffect(() => {
      const subscription = configForm.watch(() => {
        // 通知父组件表单发生变化
        onFormChange?.();
      });
      return () => subscription.unsubscribe();
    }, [configForm, onFormChange]);

    // 验证配置
    const validateCoderConfig = (config: Partial<CoderConfigFormData>): string[] => {
      const errors: string[] = [];

      if (config.systemPrompt && config.systemPrompt.length < 10) {
        errors.push('系统提示词过短');
      }

      return errors;
    };

    // 保存配置
    const handleSaveConfig = useCallback(async (data: CoderConfigFormData) => {
      try {
        // 验证配置
        const errors = validateCoderConfig(data);
        if (errors.length > 0) {
          setValidationErrors(errors);
          throw new Error(`Validation failed: ${errors.join(', ')}`);
        }

        // 更新配置
        const updatedConfig: Partial<CoderNodeConfig> = {
          languages: {
            ...coderConfig.languages,
            primary: data.primaryLanguage,
            enableAutoDetection: data.enableAutoDetection,
          },
          prompts: {
            systemPrompt: data.systemPrompt,
          },
          tools: {
            ...coderConfig.tools,
            enabled: data.enabledTools,
          },
          output: {
            ...coderConfig.output,
            format: data.outputFormat,
            includeCodeComments: data.includeCodeComments,
            includeTestCases: data.includeTestCases,
            includeDocumentation: data.includeDocumentation,
          },
          analysisStrategy: {
            ...coderConfig.analysisStrategy,
            enableStaticAnalysis: data.enableStaticAnalysis,
            performanceAnalysis: data.performanceAnalysis,
            securityCheck: data.securityCheck,
            codeQuality: data.codeQuality,
          },
        };

        updateCoderConfig(scenario.id, updatedConfig);
        setValidationErrors([]);
        // 不在这里设置 hasUnsavedChanges，由父组件统一管理

      } catch (error) {
        console.error('Failed to save coder config:', error);
        throw error; // 重新抛出错误，让父组件知道保存失败
      }
    }, [scenario.id, coderConfig, updateCoderConfig]);

    // 重置配置
    const handleReset = useCallback(() => {
      configForm.reset();
      setValidationErrors([]);
      // 不在这里设置 hasUnsavedChanges，由父组件统一管理
    }, [configForm]);

    // 暴露保存和重置方法给父组件
    useImperativeHandle(ref, () => ({
      save: async () => {
        const isValid = await configForm.trigger();
        if (isValid) {
          const data = configForm.getValues();
          await handleSaveConfig(data);
        }
      },
      reset: handleReset,
    }), [configForm, handleSaveConfig, handleReset]);

    return (
      <div className="h-full flex flex-col bg-muted/20">
        {/* 编辑器内容 */}
        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <div className="px-6 pt-4 pb-2 bg-background border-b">
              <TabsList className="grid w-full grid-cols-3 bg-muted/50 rounded-lg p-1 h-11">
                <TabsTrigger value="config" className="flex items-center gap-2 rounded-md h-9 data-[state=active]:bg-background data-[state=active]:shadow-sm">
                  <Settings className="h-4 w-4" />
                  <span className="hidden sm:inline">基础配置</span>
                  <span className="sm:hidden">配置</span>
                </TabsTrigger>
                <TabsTrigger value="prompts" className="flex items-center gap-2 rounded-md h-9 data-[state=active]:bg-background data-[state=active]:shadow-sm">
                  <MessageSquare className="h-4 w-4" />
                  <span className="hidden sm:inline">提示词</span>
                  <span className="sm:hidden">提示</span>
                </TabsTrigger>
                <TabsTrigger value="tools" className="flex items-center gap-2 rounded-md h-9 data-[state=active]:bg-background data-[state=active]:shadow-sm">
                  <Terminal className="h-4 w-4" />
                  <span className="hidden sm:inline">工具配置</span>
                  <span className="sm:hidden">工具</span>
                </TabsTrigger>
              </TabsList>
            </div>

            <div className="flex-1 overflow-auto bg-muted/10">
              <TabsContent value="config" className="m-0 h-full">
                <div className="h-full overflow-auto">
                  <div className="p-6 max-w-6xl mx-auto space-y-8">
                    <Form {...configForm}>
                      <FormSection
                        title="编程语言配置"
                        description="配置支持的编程语言和检测方式"
                        icon={Code}
                      >
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={configForm.control}
                            name="primaryLanguage"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>主要编程语言</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value} disabled={!isEditing}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="选择主要语言" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {SUPPORTED_LANGUAGES.map((lang) => (
                                      <SelectItem key={lang.value} value={lang.value}>
                                        {lang.label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <FormDescription>
                                  默认使用的编程语言
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={configForm.control}
                            name="enableAutoDetection"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border border-border/50 p-4 hover:bg-muted/30 transition-colors">
                                <div className="space-y-1 flex-1">
                                  <FormLabel className="text-sm font-medium leading-none">
                                    启用自动检测
                                  </FormLabel>
                                  <FormDescription className="text-xs text-muted-foreground">
                                    自动检测代码语言类型
                                  </FormDescription>
                                </div>
                                <FormControl>
                                  <UISwitch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                    disabled={!isEditing}
                                    className="data-[state=checked]:bg-primary"
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />
                        </div>
                      </FormSection>

                      <Card>
                        <CardHeader>
                          <CardTitle className="text-base">输出与分析配置</CardTitle>
                          <CardDescription>
                            配置代码输出格式和分析选项
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <FormField
                            control={configForm.control}
                            name="outputFormat"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>输出格式</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value} disabled={!isEditing}>
                                  <FormControl>
                                    <SelectTrigger className="w-[200px]">
                                      <SelectValue placeholder="选择输出格式" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="markdown">Markdown</SelectItem>
                                    <SelectItem value="json">JSON</SelectItem>
                                    <SelectItem value="plain">纯文本</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormDescription>
                                  代码分析结果的输出格式
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                            <FormField
                              control={configForm.control}
                              name="includeCodeComments"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between rounded-lg border border-border/50 p-4 hover:bg-muted/30 transition-colors">
                                  <div className="space-y-1 flex-1">
                                    <FormLabel className="text-sm font-medium leading-none">
                                      包含代码注释
                                    </FormLabel>
                                    <FormDescription className="text-xs text-muted-foreground">
                                      在输出中包含详细注释
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <UISwitch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                      disabled={!isEditing}
                                      className="data-[state=checked]:bg-primary"
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={configForm.control}
                              name="includeTestCases"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between rounded-lg border border-border/50 p-4 hover:bg-muted/30 transition-colors">
                                  <div className="space-y-1 flex-1">
                                    <FormLabel className="text-sm font-medium leading-none">
                                      包含测试用例
                                    </FormLabel>
                                    <FormDescription className="text-xs text-muted-foreground">
                                      生成相应的测试代码
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <UISwitch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                      disabled={!isEditing}
                                      className="data-[state=checked]:bg-primary"
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={configForm.control}
                              name="includeDocumentation"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between rounded-lg border border-border/50 p-4 hover:bg-muted/30 transition-colors">
                                  <div className="space-y-1 flex-1">
                                    <FormLabel className="text-sm font-medium leading-none">
                                      包含文档说明
                                    </FormLabel>
                                    <FormDescription className="text-xs text-muted-foreground">
                                      生成代码使用文档
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <UISwitch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                      disabled={!isEditing}
                                      className="data-[state=checked]:bg-primary"
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={configForm.control}
                              name="codeQuality"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between rounded-lg border border-border/50 p-4 hover:bg-muted/30 transition-colors">
                                  <div className="space-y-1 flex-1">
                                    <FormLabel className="text-sm font-medium leading-none">
                                      代码质量检查
                                    </FormLabel>
                                    <FormDescription className="text-xs text-muted-foreground">
                                      分析代码质量和规范
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <UISwitch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                      disabled={!isEditing}
                                      className="data-[state=checked]:bg-primary"
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </div>
                        </CardContent>
                      </Card>
                    </Form>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="prompts" className="m-0 h-full">
                <div className="h-full overflow-auto">
                  <div className="p-6 max-w-6xl mx-auto space-y-8">
                    <Form {...configForm}>
                      <FormSection
                        title="提示词配置"
                        description="配置Coder节点的各种提示词模板"
                        icon={MessageSquare}
                      >
                        <FormField
                          control={configForm.control}
                          name="systemPrompt"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>系统提示词</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="输入系统提示词..."
                                  className="resize-none min-h-[120px]"
                                  {...field}
                                  disabled={!isEditing}
                                />
                              </FormControl>
                              <FormDescription>
                                定义Coder的角色和基本行为规范
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </FormSection>
                    </Form>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="tools" className="m-0 h-full">
                <div className="h-full overflow-auto">
                  <div className="p-6 max-w-6xl mx-auto space-y-8">
                    <Form {...configForm}>
                      <FormSection
                        title="工具配置"
                        description="选择Coder节点可以使用的工具"
                        icon={Terminal}
                      >
                        <FormField
                          control={configForm.control}
                          name="enabledTools"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>启用的工具</FormLabel>
                              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-4">
                                {CODER_TOOLS.map((tool) => (
                                  <div key={tool.id} className="flex items-start space-x-3 rounded-lg border border-border/50 p-4 hover:bg-muted/30 transition-colors">
                                    <Checkbox
                                      id={tool.id}
                                      checked={field.value?.includes(tool.id) || false}
                                      onCheckedChange={(checked) => {
                                        const updatedTools = checked
                                          ? [...(field.value || []), tool.id]
                                          : (field.value || []).filter(id => id !== tool.id);
                                        field.onChange(updatedTools);
                                      }}
                                      disabled={!isEditing}
                                      className="mt-0.5"
                                    />
                                    <div className="grid gap-2 leading-none flex-1">
                                      <label
                                        htmlFor={tool.id}
                                        className="text-sm font-medium leading-none cursor-pointer peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                      >
                                        {tool.name}
                                      </label>
                                      <p className="text-xs text-muted-foreground leading-relaxed">
                                        {tool.description}
                                      </p>
                                    </div>
                                  </div>
                                ))}
                              </div>
                              <FormDescription>
                                选择Coder可以使用的工具集合
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </FormSection>
                    </Form>
                  </div>
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>

        {/* 验证错误提示 */}
        <ValidationErrorPanel errors={validationErrors} />
      </div>
    );
  });

CoderNodeEditor.displayName = 'CoderNodeEditor';