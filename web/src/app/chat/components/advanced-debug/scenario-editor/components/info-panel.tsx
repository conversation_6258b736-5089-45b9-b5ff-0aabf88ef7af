/**
 * 信息提示面板组件
 * 提供统一的信息展示UI
 */

import { Info } from "lucide-react";

interface InfoItem {
  key: string;
  description: string;
}

interface InfoPanelProps {
  title: string;
  items: InfoItem[];
  className?: string;
}

export function InfoPanel({ title, items, className }: InfoPanelProps) {
  return (
    <div className={`bg-blue-50/50 border border-blue-200/60 rounded-lg p-4 ${className ?? ''}`}>
      <div className="flex items-start gap-3">
        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
          <Info className="h-4 w-4 text-blue-600" />
        </div>
        <div className="flex-1">
          <div className="text-sm font-medium text-blue-900 mb-3">{title}</div>
          <ul className="space-y-2">
            {items.map((item) => (
              <li key={item.key} className="text-sm text-blue-800 flex items-start gap-2">
                <span className="text-blue-600 mt-1.5 block h-1 w-1 rounded-full bg-current shrink-0" />
                <span>
                  <code className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded text-xs font-mono">
                    {item.key}
                  </code>{' '}
                  <span className="text-blue-700">- {item.description}</span>
                </span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}

/**
 * 常用的模板变量信息
 */
export const COMMON_TEMPLATE_VARIABLES: InfoItem[] = [
  { key: '{userInput}', description: '用户输入内容' },
  { key: '{project}', description: '当前项目名称' },
  { key: '{codeTopicId}', description: '代码库TopicId' },
  { key: '{docTopicId}', description: '文档库TopicId' },
]; 