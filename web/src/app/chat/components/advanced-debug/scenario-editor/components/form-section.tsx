/**
 * 通用表单区域组件
 * 减少各个编辑器中重复的UI结构代码
 */

import type { LucideIcon } from "lucide-react";
import type { ReactNode } from "react";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

interface FormSectionProps {
  title: string;
  description?: string;
  icon?: LucideIcon;
  children: ReactNode;
  className?: string;
}

export function FormSection({ title, description, icon: Icon, children, className }: FormSectionProps) {
  return (
    <Card className={`transition-all duration-200 hover:shadow-sm border-0 shadow-sm ${className ?? ''}`}>
      <CardHeader className="pb-4">
        <CardTitle className="text-base flex items-center gap-2 text-foreground">
          {Icon && (
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary/10">
              <Icon className="h-4 w-4 text-primary" />
            </div>
          )}
          {title}
        </CardTitle>
        {description && (
          <CardDescription className="text-sm text-muted-foreground mt-1">
            {description}
          </CardDescription>
        )}
      </CardHeader>
      <CardContent className="space-y-6 pt-0">
        {children}
      </CardContent>
    </Card>
  );
} 