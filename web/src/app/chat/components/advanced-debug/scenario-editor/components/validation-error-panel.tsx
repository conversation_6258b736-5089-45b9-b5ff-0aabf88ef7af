/**
 * 验证错误显示面板
 * 提供统一的错误展示UI
 */

import { AlertTriangle } from "lucide-react";

interface ValidationErrorPanelProps {
  errors: string[];
  className?: string;
}

export function ValidationErrorPanel({ errors, className }: ValidationErrorPanelProps) {
  if (errors.length === 0) return null;

  return (
    <div className={`rounded-lg border border-destructive/20 bg-destructive/5 p-4 m-4 ${className ?? ''}`}>
      <div className="flex items-start gap-3">
        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-destructive/10">
          <AlertTriangle className="h-4 w-4 text-destructive" />
        </div>
        <div className="flex-1 space-y-2">
          <div className="text-sm font-medium text-destructive">配置验证失败</div>
          <ul className="space-y-1">
            {errors.map((error, index) => (
              <li key={index} className="text-sm text-destructive/80 flex items-start gap-2">
                <span className="text-destructive mt-1.5 block h-1 w-1 rounded-full bg-current shrink-0" />
                <span>{error}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
} 