"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import {
  FileText,
  MessageSquare,
  AlertTriangle,
} from "lucide-react";
import React, { useState, useCallback, useEffect, forwardRef, useImperativeHandle, useMemo } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Switch as UISwitch } from "~/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Textarea } from "~/components/ui/textarea";
import { useDebugStore } from "~/core/store/debug-store";

import type { ScenarioConfig, ReporterNodeConfig } from "../types";

// Reporter配置表单验证
const reporterConfigSchema = z.object({
  systemPrompt: z.string().min(10, "系统提示词至少需要10个字符"),
  outputFormat: z.enum(['markdown', 'html', 'plain']),
  includeTableOfContents: z.boolean(),
  includeMetadata: z.boolean(),
  includeImageAttachments: z.boolean(),
  enableAutoStructuring: z.boolean(),
  includeCitations: z.boolean(),
  citationStyle: z.enum(['apa', 'mla', 'chicago', 'custom']),
  maxSectionDepth: z.number().min(1).max(6),
  enableTableGeneration: z.boolean(),
});

type ReporterConfigFormData = z.infer<typeof reporterConfigSchema>;

interface ReporterNodeEditorProps {
  scenario: ScenarioConfig;
  isEditing: boolean;
  onFormChange?: () => void;
}

export interface ReporterNodeEditorRef {
  save: () => Promise<void>;
  reset: () => void;
}

export const ReporterNodeEditor = forwardRef<ReporterNodeEditorRef, ReporterNodeEditorProps>(
  ({ scenario, isEditing, onFormChange }, ref) => {
  const { updateReporterConfig } = useDebugStore();
  const [activeTab, setActiveTab] = useState<string>('prompts');
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // 获取配置
  const reporterConfig = useMemo(() => {
    if (!scenario?.nodes.reporter) {
      return {
        prompts: {
          systemPrompt: '你是一个专业的报告生成器，负责整理和总结分析结果，生成清晰、结构化的报告。',
        },
        tools: {
          enabled: [],
          configs: {},
        },
        output: {
          format: 'markdown' as const,
          includeTableOfContents: true,
          includeMetadata: true,
          includeImageAttachments: false,
          debugOutput: false,
        },
        reportStrategy: {
          enableAutoStructuring: true,
          includeCitations: true,
          citationStyle: 'apa' as const,
          maxSectionDepth: 3,
          enableTableGeneration: true,
        },
        scenarioParams: {},
      };
    }
    return scenario.nodes.reporter;
  }, [scenario?.nodes.reporter]);

  // 配置表单
  const configForm = useForm<ReporterConfigFormData>({
    resolver: zodResolver(reporterConfigSchema),
    defaultValues: {
      systemPrompt: reporterConfig.prompts.systemPrompt,
      outputFormat: reporterConfig.output.format,
      includeTableOfContents: reporterConfig.output.includeTableOfContents,
      includeMetadata: reporterConfig.output.includeMetadata,
      includeImageAttachments: reporterConfig.output.includeImageAttachments,
      enableAutoStructuring: reporterConfig.reportStrategy.enableAutoStructuring,
      includeCitations: reporterConfig.reportStrategy.includeCitations,
      citationStyle: reporterConfig.reportStrategy.citationStyle,
      maxSectionDepth: reporterConfig.reportStrategy.maxSectionDepth,
      enableTableGeneration: reporterConfig.reportStrategy.enableTableGeneration,
    },
  });

  // 当场景变化时更新表单
  useEffect(() => {
    configForm.reset({
      systemPrompt: reporterConfig.prompts.systemPrompt,
      outputFormat: reporterConfig.output.format,
      includeTableOfContents: reporterConfig.output.includeTableOfContents,
      includeMetadata: reporterConfig.output.includeMetadata,
      includeImageAttachments: reporterConfig.output.includeImageAttachments,
      enableAutoStructuring: reporterConfig.reportStrategy.enableAutoStructuring,
      includeCitations: reporterConfig.reportStrategy.includeCitations,
      citationStyle: reporterConfig.reportStrategy.citationStyle,
      maxSectionDepth: reporterConfig.reportStrategy.maxSectionDepth,
      enableTableGeneration: reporterConfig.reportStrategy.enableTableGeneration,
    });
  }, [reporterConfig, configForm]);

  // 监听表单变化
  useEffect(() => {
    const subscription = configForm.watch(() => {
      // 通知父组件表单发生变化
      onFormChange?.();
    });
    return () => subscription.unsubscribe();
  }, [configForm, onFormChange]);

  // 验证配置
  const validateReporterConfig = (config: Partial<ReporterConfigFormData>): string[] => {
    const errors: string[] = [];
    
    if (config.systemPrompt && config.systemPrompt.length < 10) {
      errors.push('系统提示词过短');
    }
    
    if (config.maxSectionDepth && (config.maxSectionDepth < 1 || config.maxSectionDepth > 6)) {
      errors.push('最大章节深度必须在1-6之间');
    }
    
    return errors;
  };

  // 保存配置
  const handleSaveConfig = useCallback(async (data: ReporterConfigFormData) => {
    try {
      // 验证配置
      const errors = validateReporterConfig(data);
      if (errors.length > 0) {
        setValidationErrors(errors);
        throw new Error(`Validation failed: ${errors.join(', ')}`);
      }
      
      // 更新配置
      const updatedConfig: Partial<ReporterNodeConfig> = {
        prompts: {
          systemPrompt: data.systemPrompt,
        },
        output: {
          ...reporterConfig.output,
          format: data.outputFormat,
          includeTableOfContents: data.includeTableOfContents,
          includeMetadata: data.includeMetadata,
          includeImageAttachments: data.includeImageAttachments,
        },
        reportStrategy: {
          ...reporterConfig.reportStrategy,
          enableAutoStructuring: data.enableAutoStructuring,
          includeCitations: data.includeCitations,
          citationStyle: data.citationStyle,
          maxSectionDepth: data.maxSectionDepth,
          enableTableGeneration: data.enableTableGeneration,
        },
      };
      
      updateReporterConfig(scenario.id, updatedConfig);
      setValidationErrors([]);
      // 不在这里设置 hasUnsavedChanges，由父组件统一管理
      
    } catch (error) {
      console.error('Failed to save reporter config:', error);
      throw error; // 重新抛出错误，让父组件知道保存失败
    }
  }, [scenario.id, reporterConfig, updateReporterConfig]);

  // 重置配置
  const handleReset = useCallback(() => {
    configForm.reset();
    setValidationErrors([]);
    // 不在这里设置 hasUnsavedChanges，由父组件统一管理
  }, [configForm]);

  // 暴露保存和重置方法给父组件
  useImperativeHandle(ref, () => ({
    save: async () => {
      const isValid = await configForm.trigger();
      if (isValid) {
        const data = configForm.getValues();
        await handleSaveConfig(data);
      }
    },
    reset: handleReset,
  }), [configForm, handleSaveConfig, handleReset]);

  return (
    <div className="h-full flex flex-col">
      {/* 编辑器内容 */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <div className="px-6 pt-4">
            <TabsList className="grid w-full grid-cols-2 bg-muted rounded-lg p-1">
              <TabsTrigger value="prompts" className="flex items-center gap-2 rounded-md">
                <MessageSquare className="h-4 w-4" />
                提示词配置
              </TabsTrigger>
              <TabsTrigger value="output" className="flex items-center gap-2 rounded-md">
                <FileText className="h-4 w-4" />
                输出配置
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="flex-1 overflow-auto">
            <Form {...configForm}>
              <form onSubmit={configForm.handleSubmit(handleSaveConfig)} className="space-y-6">
                
                {/* 提示词配置 */}
                <TabsContent value="prompts" className="m-0 p-6 space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">系统提示词配置</CardTitle>
                      <CardDescription>
                        定义Reporter节点的角色和基本行为规范
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <FormField
                        control={configForm.control}
                        name="systemPrompt"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Reporter系统提示词</FormLabel>
                            <FormControl>
                              <Textarea 
                                {...field}
                                disabled={!isEditing}
                                rows={12}
                                className="resize-none font-mono"
                                placeholder="你是一个专业的报告生成器，负责整理和总结分析结果..."
                              />
                            </FormControl>
                            <FormDescription>
                              定义AI助手的角色和基本行为准则。支持模板变量：{`{userInput}`}, {`{project}`}, {`{outputFormat}`}等
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                        <div className="flex items-start gap-2 text-blue-800">
                          <AlertTriangle className="h-4 w-4 mt-0.5" />
                          <div className="text-sm">
                            <div className="font-medium mb-1">可用模板变量</div>
                            <ul className="text-xs space-y-1">
                              <li>• <code className="bg-blue-100 px-1 rounded">{`{userInput}`}</code> - 用户输入内容</li>
                              <li>• <code className="bg-blue-100 px-1 rounded">{`{project}`}</code> - 当前项目名称</li>
                              <li>• <code className="bg-blue-100 px-1 rounded">{`{outputFormat}`}</code> - 输出格式</li>
                              <li>• <code className="bg-blue-100 px-1 rounded">{`{citationStyle}`}</code> - 引用格式</li>
                              <li>• <code className="bg-blue-100 px-1 rounded">{`{maxSectionDepth}`}</code> - 最大章节深度</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* 输出配置 */}
                <TabsContent value="output" className="m-0 p-6 space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">输出配置</CardTitle>
                      <CardDescription>
                        配置报告的输出格式和内容选项
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <FormField
                        control={configForm.control}
                        name="outputFormat"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>输出格式</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value} disabled={!isEditing}>
                              <FormControl>
                                <SelectTrigger className="w-[200px]">
                                  <SelectValue placeholder="选择输出格式" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="markdown">Markdown</SelectItem>
                                <SelectItem value="html">HTML</SelectItem>
                                <SelectItem value="plain">纯文本</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              报告的输出文件格式
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={configForm.control}
                          name="includeTableOfContents"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <div className="space-y-0.5">
                                <FormLabel className="text-sm font-medium">
                                  包含目录
                                </FormLabel>
                                <FormDescription>
                                  在报告开头生成目录
                                </FormDescription>
                              </div>
                              <FormControl>
                                <UISwitch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={!isEditing}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={configForm.control}
                          name="includeMetadata"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <div className="space-y-0.5">
                                <FormLabel className="text-sm font-medium">
                                  包含元数据
                                </FormLabel>
                                <FormDescription>
                                  在报告中包含生成信息
                                </FormDescription>
                              </div>
                              <FormControl>
                                <UISwitch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={!isEditing}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={configForm.control}
                          name="enableAutoStructuring"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <div className="space-y-0.5">
                                <FormLabel className="text-sm font-medium">
                                  自动结构化
                                </FormLabel>
                                <FormDescription>
                                  自动组织报告结构
                                </FormDescription>
                              </div>
                              <FormControl>
                                <UISwitch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={!isEditing}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={configForm.control}
                          name="includeCitations"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <div className="space-y-0.5">
                                <FormLabel className="text-sm font-medium">
                                  包含引用
                                </FormLabel>
                                <FormDescription>
                                  在报告中包含引用信息
                                </FormDescription>
                              </div>
                              <FormControl>
                                <UISwitch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={!isEditing}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={configForm.control}
                          name="enableTableGeneration"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <div className="space-y-0.5">
                                <FormLabel className="text-sm font-medium">
                                  表格生成
                                </FormLabel>
                                <FormDescription>
                                  优先使用表格展示数据
                                </FormDescription>
                              </div>
                              <FormControl>
                                <UISwitch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={!isEditing}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={configForm.control}
                          name="citationStyle"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>引用格式</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value} disabled={!isEditing}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="选择引用格式" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="apa">APA</SelectItem>
                                  <SelectItem value="mla">MLA</SelectItem>
                                  <SelectItem value="chicago">Chicago</SelectItem>
                                  <SelectItem value="custom">自定义</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                引用文献的格式标准
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </form>
            </Form>
          </div>
        </Tabs>
      </div>
      
      {/* 验证错误提示 */}
      {validationErrors.length > 0 && (
        <div className="border-t bg-destructive/5 border-destructive/20 p-4">
          <div className="flex items-start gap-2 text-destructive">
            <AlertTriangle className="h-4 w-4 mt-0.5 shrink-0" />
            <div className="space-y-1">
              <div className="text-sm font-medium">配置验证失败</div>
              <ul className="text-xs space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

ReporterNodeEditor.displayName = 'ReporterNodeEditor';