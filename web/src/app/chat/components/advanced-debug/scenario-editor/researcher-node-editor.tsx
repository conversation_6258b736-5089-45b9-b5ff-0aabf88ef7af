"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import {
  Search,
  Settings,
  Zap,
  MessageSquare,
  FileText,
  Database,
  Code,
  Link,
  Info,
} from "lucide-react";
import { useState, useEffect, useCallback, useImperativeHandle, forwardRef, useMemo } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Badge } from "~/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Checkbox } from "~/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Slider } from "~/components/ui/slider";
import { Switch as UISwitch } from "~/components/ui/switch";
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "~/components/ui/tabs";
import { Textarea } from "~/components/ui/textarea";
import { useDebugStore } from "~/core/store/debug-store";

import type { ScenarioConfig, ResearcherNodeConfig } from "../types";

// 基于tools-tab.tsx的工具数据定义
const DOCCHAIN_TOOLS = [
  {
    id: 'search_code_by_docchain',
    name: 'DocChain代码搜索',
    displayName: 'DocChain代码搜索',
    description: '使用DocChain搜索代码相关信息的工具。适用于搜索代码文件、Java类、方法实现、配置文件、API接口等代码元素。',
    icon: <Code className="h-4 w-4" />,
    category: '代码搜索',
    parameters: [
      { name: 'query', type: 'str', required: true, description: '搜索查询内容' },
      { name: 'topic_id', type: 'str', required: false, default: '项目默认的TopicId', description: '需要搜索的topic_id' },
      { name: 'size', type: 'int', required: false, default: '5', description: '返回结果数量' }
    ],
    example: 'search_code_by_docchain(query="用户登录验证", size=10)',
  },
  {
    id: 'search_doc_by_docchain',
    name: 'DocChain文档搜索',
    displayName: 'DocChain文档搜索',
    description: '使用DocChain搜索文档相关信息的工具。适用于搜索技术文档、业务规范、操作手册、API文档等文档内容。',
    icon: <FileText className="h-4 w-4" />,
    category: '文档搜索',
    parameters: [
      { name: 'query', type: 'str', required: true, description: '搜索查询内容' },
      { name: 'topic_id', type: 'str', required: false, default: '项目默认的TopicId', description: '需要搜索的topic_id' },
      { name: 'size', type: 'int', required: false, default: '5', description: '返回结果数量' }
    ],
    example: 'search_doc_by_docchain(query="用户管理API接口", size=8)',
  },
  {
    id: 'file_read_by_docchain',
    name: 'DocChain文件读取',
    displayName: 'DocChain文件读取',
    description: '读取DocChain中指定文档的完整内容，支持分页读取和批量读取多个文件。必须先使用搜索工具获取doc_id。',
    icon: <Database className="h-4 w-4" />,
    category: '文件操作',
    parameters: [
      { name: 'doc_ids', type: 'List[str]', required: true, description: 'DocChain文档ID列表，从搜索结果中获取' },
      { name: 'page', type: 'int', required: false, default: '1', description: '页码' },
      { name: 'size', type: 'int', required: false, default: '80000', description: '每页大小' }
    ],
    example: 'file_read_by_docchain(doc_ids=["12345", "67890"])',
  },
  {
    id: 'search_file_by_name',
    name: '按文件名搜索',
    displayName: '按文件名搜索',
    description: '根据文件名搜索DocChain中的文档，返回文档ID和文档名称列表。可以帮助快速定位具体文件。',
    icon: <Search className="h-4 w-4" />,
    category: '文件搜索',
    parameters: [
      { name: 'keyword', type: 'str', required: true, description: '文件名关键词，如"TimeUtil"、"UserController.java"' },
      { name: 'topic_id', type: 'str', required: false, default: '项目默认的TopicId', description: '需要搜索的topic_id' },
      { name: 'page', type: 'int', required: false, default: '1', description: '页码' },
      { name: 'size', type: 'int', required: false, default: '10', description: '每页大小' }
    ],
    example: 'search_file_by_name(keyword="UserController.java")',
  },
  {
    id: 'search_by_docchain_llm',
    name: 'DocChain智能搜索',
    displayName: 'DocChain智能搜索',
    description: '使用DocChain大模型对话接口进行智能搜索和问答。支持自然语言问答，可以直接提问获取精准答案。',
    icon: <Zap className="h-4 w-4" />,
    category: '智能搜索',
    parameters: [
      { name: 'query', type: 'str', required: true, description: '要询问DocChain大模型的问题或查询内容' },
      { name: 'topic_ids', type: 'str', required: false, default: '项目默认的TopicId', description: '需要搜索的topic_ids，用逗号分隔' }
    ],
    example: 'search_by_docchain_llm(query="用户登录接口在哪个类中实现？方法名是什么？")',
  },
  {
    id: 'smart_code_analyzer',
    name: '智能代码分析器',
    displayName: '智能代码分析器',
    description: '专注于代码定位和结构分析的工具，可以快速找到接口实现位置和相关代码，提供代码结构概览。',
    icon: <Link className="h-4 w-4" />,
    category: '代码分析',
    parameters: [
      { name: 'query', type: 'str', required: true, description: '要分析的代码问题或查询内容' },
      { name: 'topic_id', type: 'str', required: false, default: '项目默认的TopicId', description: '需要搜索的代码库topic_id' }
    ],
    example: 'smart_code_analyzer(query="wfm/itf/appointment/om/appointmentQuery/v1 这个接口的实现位置和调用关系")',
  }
];

const METHOD_CHAIN_TOOLS = [
  {
    id: 'java_method_call_chain',
    name: 'Java方法调用链分析',
    displayName: 'Java方法调用链分析',
    description: '分析Java代码中指定方法的调用链。可以分析指定方法被哪些其他方法调用，以及它调用了哪些其他方法，帮助理解代码的调用关系和依赖结构。',
    icon: <Link className="h-4 w-4" />,
    category: '调用链分析',
    parameters: [
      { name: 'package_path', type: 'str', required: true, description: 'Java包路径，例如: com.ztesoft.zmq.controller' },
      { name: 'class_name', type: 'str', required: true, description: 'Java类名，例如: ExpertDiagnosisAction' },
      { name: 'method_name', type: 'str', required: true, description: 'Java方法名，例如: createDiagnosis' },
      { name: 'level', type: 'int', required: false, default: '4', description: '调用链分析层级深度，1-20之间' }
    ],
    example: 'java_method_call_chain(package_path="com.example.service", class_name="UserService", method_name="login", level=3)',
  }
];

// 所有可用工具
const ALL_RESEARCH_TOOLS = [...DOCCHAIN_TOOLS, ...METHOD_CHAIN_TOOLS];

// 简化的Researcher配置表单验证
const researcherConfigSchema = z.object({
  maxSearchResults: z.number().min(5).max(100),
  searchDepth: z.number().min(1).max(10),
  systemPrompt: z.string().min(10, "系统提示词至少需要10个字符"),
  enabledTools: z.array(z.string()),
  enableParallelSearch: z.boolean(),
  searchQuality: z.number().min(0.1).max(1.0),
  cacheResults: z.boolean(),
  outputFormat: z.enum(['json', 'markdown', 'plain']),
});

type ResearcherConfigFormData = z.infer<typeof researcherConfigSchema>;

interface ResearcherNodeEditorProps {
  scenario: ScenarioConfig;
  isEditing: boolean;
  onFormChange?: () => void;
}

export interface ResearcherNodeEditorRef {
  save: () => Promise<void>;
  reset: () => void;
}

export const ResearcherNodeEditor = forwardRef<ResearcherNodeEditorRef, ResearcherNodeEditorProps>(
  ({ scenario, isEditing, onFormChange }, ref) => {
  const { updateResearcherConfig } = useDebugStore();
  const [activeTab, setActiveTab] = useState<string>('config');

  // 获取配置 - 使用useMemo包装以避免依赖警告
  const researcherConfig = useMemo(() => scenario?.nodes.researcher ?? {
    maxSearchResults: 10,
    searchDepth: 3,
    prompts: {
      systemPrompt: '你是一个专业的研究员，负责收集和分析信息。',
    },
    tools: {
      enabled: ['search', 'crawl'],
      configs: {},
    },
    searchStrategy: {
      enableParallelSearch: true,
      searchQuality: 'high' as const,
      cacheResults: true,
    },
    output: {
      format: 'markdown' as const,
    },
    scenarioParams: {},
  }, [scenario?.nodes.researcher]);
  
  // 配置表单
  const configForm = useForm<ResearcherConfigFormData>({
    resolver: zodResolver(researcherConfigSchema),
    defaultValues: {
      maxSearchResults: researcherConfig.maxSearchResults,
      searchDepth: researcherConfig.searchDepth,
      systemPrompt: researcherConfig.prompts.systemPrompt,
      enabledTools: researcherConfig.tools.enabled || [],
      enableParallelSearch: researcherConfig.searchStrategy.enableParallelSearch,
      searchQuality: researcherConfig.searchStrategy.searchQuality,
      cacheResults: researcherConfig.searchStrategy.cacheResults,
      outputFormat: researcherConfig.output.format,
    },
  });

  // 当场景变化时更新表单
  useEffect(() => {
    const config = scenario.nodes.researcher;
    configForm.reset({
      maxSearchResults: config.maxSearchResults,
      searchDepth: config.searchDepth,
      systemPrompt: config.prompts.systemPrompt,
      enabledTools: config.tools.enabled || [],
      enableParallelSearch: config.searchStrategy.enableParallelSearch,
      searchQuality: config.searchStrategy.searchQuality,
      cacheResults: config.searchStrategy.cacheResults,
      outputFormat: config.output.format,
    });
  }, [scenario.nodes.researcher, configForm]);

  // 保存配置
  const handleSaveConfig = useCallback(async (data: ResearcherConfigFormData) => {
    try {
      const updates: Partial<ResearcherNodeConfig> = {
        maxSearchResults: data.maxSearchResults,
        searchDepth: data.searchDepth,
        prompts: {
          systemPrompt: data.systemPrompt,
        },
        tools: {
          ...researcherConfig.tools,
          enabled: data.enabledTools,
        },
        searchStrategy: {
          ...researcherConfig.searchStrategy,
          enableParallelSearch: data.enableParallelSearch,
          searchQuality: data.searchQuality,
          cacheResults: data.cacheResults,
        },
        output: {
          ...researcherConfig.output,
          format: data.outputFormat,
        },
      };

      updateResearcherConfig(scenario.id, updates);
      // 不在这里设置 hasUnsavedChanges，由父组件统一管理
    } catch (error) {
      console.error('Failed to save researcher config:', error);
      throw error; // 重新抛出错误，让父组件知道保存失败
    }
  }, [scenario.id, researcherConfig, updateResearcherConfig]);

  // 重置表单
  const handleReset = useCallback(() => {
    const config = scenario.nodes.researcher;
    configForm.reset({
      maxSearchResults: config.maxSearchResults,
      searchDepth: config.searchDepth,
      systemPrompt: config.prompts.systemPrompt,
      enabledTools: config.tools.enabled || [],
      enableParallelSearch: config.searchStrategy.enableParallelSearch,
      searchQuality: config.searchStrategy.searchQuality,
      cacheResults: config.searchStrategy.cacheResults,
      outputFormat: config.output.format,
    });
    // 不在这里设置 hasUnsavedChanges，由父组件统一管理
  }, [scenario.nodes.researcher, configForm]);

  // 简单验证
  const validateResearcherConfig = (config: Partial<ResearcherConfigFormData>): string[] => {
    const errors: string[] = [];
    
    if (config.systemPrompt && config.systemPrompt.length < 10) {
      errors.push('系统提示词太短');
    }
    
    if (config.searchDepth !== undefined && (config.searchDepth < 1 || config.searchDepth > 10)) {
      errors.push('搜索深度应在1-10之间');
    }
    
    if (config.enabledTools !== undefined && config.enabledTools.length === 0) {
      errors.push('至少需要选择一个研究工具');
    }
    
    return errors;
  };

  // 监听表单变化进行验证
  useEffect(() => {
    const subscription = configForm.watch((value) => {
      // 确保enabledTools是有效的数组
      const cleanedValue = {
        ...value,
        enabledTools: Array.isArray(value.enabledTools) 
          ? value.enabledTools.filter((tool): tool is string => typeof tool === 'string')
          : []
      };
              validateResearcherConfig(cleanedValue);
        // 通知父组件表单发生变化
        onFormChange?.();
    });
    return () => subscription.unsubscribe();
  }, [configForm, onFormChange]);

  // 暴露保存和重置方法给父组件
  useImperativeHandle(ref, () => ({
    save: async () => {
      const isValid = await configForm.trigger();
      if (isValid) {
        const data = configForm.getValues();
        await handleSaveConfig(data);
      }
    },
    reset: handleReset,
  }), [configForm, handleSaveConfig, handleReset]);

  return (
    <div className="h-full flex flex-col">
      {/* 编辑器内容 */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <div className="px-6 pt-4">
            <TabsList className="grid w-full grid-cols-3 bg-muted rounded-lg p-1">
              <TabsTrigger value="config" className="flex items-center gap-2 rounded-md">
                <Settings className="h-4 w-4" />
                基础配置
              </TabsTrigger>
              <TabsTrigger value="tools" className="flex items-center gap-2 rounded-md">
                <Zap className="h-4 w-4" />
                工具配置
              </TabsTrigger>
              <TabsTrigger value="prompts" className="flex items-center gap-2 rounded-md">
                <MessageSquare className="h-4 w-4" />
                提示词配置
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="flex-1 overflow-auto">
            <Form {...configForm}>
              <form onSubmit={configForm.handleSubmit(handleSaveConfig)} className="space-y-6">
                
                {/* 基础配置 */}
                <TabsContent value="config" className="m-0 p-6 space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">节点基础配置</CardTitle>
                      <CardDescription>
                        配置Researcher节点的基本运行参数
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <FormField
                        control={configForm.control}
                        name="maxSearchResults"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>最大搜索结果数</FormLabel>
                              <FormControl>
                                <Input 
                                  type="number"
                                  min={5}
                                  max={100}
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                  disabled={!isEditing}
                                />
                              </FormControl>
                              <FormDescription>
                                单次搜索返回的最大结果数量
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={configForm.control}
                          name="searchDepth"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>搜索深度</FormLabel>
                              <FormControl>
                                <Input 
                                  type="number"
                                  min={1}
                                  max={10}
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                  disabled={!isEditing}
                                />
                              </FormControl>
                              <FormDescription>
                                搜索的最大深度层级
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* 工具配置 */}
                <TabsContent value="tools" className="m-0 p-6 space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">研究工具选择</CardTitle>
                      <CardDescription>
                        选择Researcher节点可以使用的研究工具
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <FormField
                        control={configForm.control}
                        name="enabledTools"
                        render={() => (
                          <FormItem>
                            <div className="mb-4">
                              <FormLabel className="text-base">可用工具</FormLabel>
                              <FormDescription>
                                选择一个或多个研究工具来执行信息收集任务
                              </FormDescription>
                            </div>
                            <div className="grid grid-cols-1 gap-3">
                              {ALL_RESEARCH_TOOLS.map((tool) => (
                                <FormField
                                  key={tool.id}
                                  control={configForm.control}
                                  name="enabledTools"
                                  render={({ field }) => {
                                    return (
                                      <FormItem
                                        key={tool.id}
                                        className="flex flex-row items-start space-x-3 space-y-0"
                                      >
                                        <FormControl>
                                          <Checkbox
                                            checked={Array.isArray(field.value) && field.value.includes(tool.id)}
                                            onCheckedChange={(checked) => {
                                              const currentTools = Array.isArray(field.value) ? field.value : [];
                                              return checked
                                                ? field.onChange([...currentTools, tool.id])
                                                : field.onChange(
                                                    currentTools.filter(
                                                      (value) => value !== tool.id
                                                    )
                                                  )
                                            }}
                                            disabled={!isEditing}
                                          />
                                        </FormControl>
                                        <div className="flex-1 rounded-lg border p-3">
                                          <div className="flex items-center gap-2 mb-1">
                                            {tool.icon}
                                            <FormLabel className="text-sm font-medium">
                                              {tool.name}
                                            </FormLabel>
                                            <Badge variant="outline" className="text-xs">
                                              {tool.category}
                                            </Badge>
                                          </div>
                                          <p className="text-xs text-muted-foreground">
                                            {tool.description}
                                          </p>
                                        </div>
                                      </FormItem>
                                    )
                                  }}
                                />
                              ))}
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* 提示词配置 */}
                <TabsContent value="prompts" className="m-0 p-6 space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">系统提示词配置</CardTitle>
                      <CardDescription>
                        定义Researcher节点的角色和基本行为规则
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <FormField
                        control={configForm.control}
                        name="systemPrompt"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Researcher系统提示词</FormLabel>
                            <FormControl>
                              <Textarea 
                                {...field}
                                disabled={!isEditing}
                                rows={12}
                                className="resize-none font-mono"
                                placeholder="你是一个专业的研究分析师，负责收集和分析信息..."
                              />
                            </FormControl>
                            <FormDescription>
                              定义AI助手的角色和基本行为准则。支持模板变量：{`{userInput}`}, {`{project}`}, {`{maxSearchResults}`}, {`{enabledTools}`}等
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                        <div className="flex items-start gap-2 text-blue-800">
                          <Info className="h-4 w-4 mt-0.5" />
                          <div className="text-sm">
                            <div className="font-medium mb-1">可用模板变量</div>
                            <ul className="text-xs space-y-1">
                              <li>• <code className="bg-blue-100 px-1 rounded">{`{userInput}`}</code> - 用户输入内容</li>
                              <li>• <code className="bg-blue-100 px-1 rounded">{`{project}`}</code> - 当前项目名称</li>
                              <li>• <code className="bg-blue-100 px-1 rounded">{`{codeTopicId}`}</code> - 代码库TopicId</li>
                              <li>• <code className="bg-blue-100 px-1 rounded">{`{docTopicId}`}</code> - 文档库TopicId</li>
                              <li>• <code className="bg-blue-100 px-1 rounded">{`{maxSearchResults}`}</code> - 最大搜索结果数</li>
                              <li>• <code className="bg-blue-100 px-1 rounded">{`{searchDepth}`}</code> - 搜索深度</li>
                              <li>• <code className="bg-blue-100 px-1 rounded">{`{enabledTools}`}</code> - 启用的工具列表</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">搜索策略配置</CardTitle>
                      <CardDescription>
                        配置Researcher节点的搜索和分析策略
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <FormField
                        control={configForm.control}
                        name="enableParallelSearch"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                启用并行搜索
                              </FormLabel>
                              <FormDescription>
                                允许同时进行多个搜索查询
                              </FormDescription>
                            </div>
                            <FormControl>
                              <UISwitch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                disabled={!isEditing}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={configForm.control}
                        name="searchQuality"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>搜索质量 ({Math.round((field.value ?? 0) * 100)}%)</FormLabel>
                            <FormControl>
                              <Slider
                                value={[(field.value ?? 0) * 100]}
                                onValueChange={(value) => field.onChange((value[0] ?? 0) / 100)}
                                max={100}
                                min={10}
                                step={1}
                                disabled={!isEditing}
                              />
                            </FormControl>
                            <FormDescription>
                              搜索结果的相关性和可靠性要求
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={configForm.control}
                        name="cacheResults"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                启用结果缓存
                              </FormLabel>
                              <FormDescription>
                                缓存搜索结果以提高性能
                              </FormDescription>
                            </div>
                            <FormControl>
                              <UISwitch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                disabled={!isEditing}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={configForm.control}
                        name="outputFormat"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>输出格式</FormLabel>
                            <Select 
                              onValueChange={field.onChange} 
                              defaultValue={field.value}
                              disabled={!isEditing}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="markdown">Markdown</SelectItem>
                                <SelectItem value="json">JSON</SelectItem>
                                <SelectItem value="plain">纯文本</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              研究结果的输出格式
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>
                </TabsContent>
              </form>
            </Form>
          </div>
        </Tabs>
      </div>
    </div>
  );
});

ResearcherNodeEditor.displayName = 'ResearcherNodeEditor';