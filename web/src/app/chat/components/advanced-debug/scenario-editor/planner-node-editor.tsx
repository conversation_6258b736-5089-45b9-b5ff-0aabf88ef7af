"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import {
  Settings,
  MessageSquare,
  Info,
} from "lucide-react";
import { useState, useCallback, useEffect, useImperativeHandle, forwardRef } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Textarea } from "~/components/ui/textarea";
import { useDebugStore } from "~/core/store/debug-store";

import type { ScenarioConfig, PlannerNodeConfig } from "../types";

// 简化的Planner配置表单验证
const plannerConfigSchema = z.object({
  maxPlanIterations: z.number().min(1).max(10),
  systemPrompt: z.string().min(10, "系统提示词至少需要10个字符"),
});

type PlannerConfigFormData = z.infer<typeof plannerConfigSchema>;

interface PlannerNodeEditorProps {
  scenario: ScenarioConfig;
  isEditing: boolean;
  onFormChange?: () => void;
}

export interface PlannerNodeEditorRef {
  save: () => Promise<void>;
  reset: () => void;
}

export const PlannerNodeEditor = forwardRef<PlannerNodeEditorRef, PlannerNodeEditorProps>(
  ({ scenario, isEditing, onFormChange }, ref) => {
  const { updatePlannerConfig } = useDebugStore();
  
  const [activeTab, setActiveTab] = useState("config");

  const plannerConfig = scenario.nodes.planner;

  // 配置表单
  const configForm = useForm<PlannerConfigFormData>({
    resolver: zodResolver(plannerConfigSchema),
    defaultValues: {
      maxPlanIterations: plannerConfig.maxIterations || 3,
      systemPrompt: plannerConfig.prompts.systemPrompt,
    },
  });

  // 当场景变化时更新表单
  useEffect(() => {
    const config = scenario.nodes.planner;
    configForm.reset({
      maxPlanIterations: config.maxIterations || 3,
      systemPrompt: config.prompts.systemPrompt,
    });
  }, [scenario.nodes.planner, configForm]);

  // 保存配置
  const handleSaveConfig = useCallback(async (data: PlannerConfigFormData) => {
    try {
      const updates: Partial<PlannerNodeConfig> = {
        maxIterations: data.maxPlanIterations,
        prompts: {
          systemPrompt: data.systemPrompt,
        },
      };

      updatePlannerConfig(scenario.id, updates);
      // 不在这里设置 hasUnsavedChanges，由父组件统一管理
    } catch (error) {
      console.error('Failed to save planner config:', error);
      throw error; // 重新抛出错误，让父组件知道保存失败
    }
  }, [scenario.id, updatePlannerConfig]);

  // 重置表单
  const handleReset = useCallback(() => {
    const config = scenario.nodes.planner;
    configForm.reset({
      maxPlanIterations: config.maxIterations || 3,
      systemPrompt: config.prompts.systemPrompt,
    });
    // 不在这里设置 hasUnsavedChanges，由父组件统一管理
  }, [scenario.nodes.planner, configForm]);

  // 移除未使用的验证函数

  // 监听表单变化进行验证
  useEffect(() => {
    const subscription = configForm.watch(() => {
      // 通知父组件表单发生变化
      onFormChange?.();
    });
    return () => subscription.unsubscribe();
  }, [configForm, onFormChange]);

  // 暴露保存和重置方法给父组件
  useImperativeHandle(ref, () => ({
    save: async () => {
      const isValid = await configForm.trigger();
      if (isValid) {
        const data = configForm.getValues();
        await handleSaveConfig(data);
      }
    },
    reset: handleReset,
  }), [configForm, handleSaveConfig, handleReset]);

  return (
    <div className="h-full flex flex-col">
      {/* 编辑器内容 */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <div className="px-6 pt-4">
            <TabsList className="grid w-full grid-cols-2 bg-muted rounded-lg p-1">
              <TabsTrigger value="config" className="flex items-center gap-2 rounded-md">
                <Settings className="h-4 w-4" />
                基础配置
              </TabsTrigger>
              <TabsTrigger value="prompts" className="flex items-center gap-2 rounded-md">
                <MessageSquare className="h-4 w-4" />
                提示词配置
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="flex-1 overflow-auto">
            <Form {...configForm}>
              <form onSubmit={configForm.handleSubmit(handleSaveConfig)} className="space-y-6">
                
                {/* 基础配置 */}
                <TabsContent value="config" className="m-0 p-6 space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">节点基础配置</CardTitle>
                      <CardDescription>
                        配置Planner节点的基本运行参数
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <FormField
                        control={configForm.control}
                        name="maxPlanIterations"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>最大迭代次数</FormLabel>
                            <FormControl>
                              <Input 
                                type="number"
                                min={1}
                                max={10}
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                                disabled={!isEditing}
                              />
                            </FormControl>
                            <FormDescription>
                              规划的最大迭代次数
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* 提示词配置 */}
                <TabsContent value="prompts" className="m-0 p-6 space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">系统提示词配置</CardTitle>
                      <CardDescription>
                        定义Planner节点的角色和基本行为规则
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <FormField
                        control={configForm.control}
                        name="systemPrompt"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Planner系统提示词</FormLabel>
                            <FormControl>
                              <Textarea 
                                {...field}
                                disabled={!isEditing}
                                rows={12}
                                className="resize-none font-mono"
                                placeholder="你是一个专业的项目规划师，负责分析需求并制定详细的执行计划..."
                              />
                            </FormControl>
                            <FormDescription>
                              定义AI助手的角色和基本行为准则。支持模板变量：{`{userInput}`}, {`{project}`}, {`{codeTopicId}`}, {`{docTopicId}`}等
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                        <div className="flex items-start gap-2 text-blue-800">
                          <Info className="h-4 w-4 mt-0.5" />
                          <div className="text-sm">
                            <div className="font-medium mb-1">可用模板变量</div>
                            <ul className="text-xs space-y-1">
                              <li>• <code className="bg-blue-100 px-1 rounded">{`{userInput}`}</code> - 用户输入内容</li>
                              <li>• <code className="bg-blue-100 px-1 rounded">{`{project}`}</code> - 当前项目名称</li>
                              <li>• <code className="bg-blue-100 px-1 rounded">{`{codeTopicId}`}</code> - 代码库TopicId</li>
                              <li>• <code className="bg-blue-100 px-1 rounded">{`{docTopicId}`}</code> - 文档库TopicId</li>
                              <li>• <code className="bg-blue-100 px-1 rounded">{`{enabledTools}`}</code> - 启用的工具列表</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>


              </form>
            </Form>
          </div>
        </Tabs>
      </div>
    </div>
  );
});

PlannerNodeEditor.displayName = 'PlannerNodeEditor';