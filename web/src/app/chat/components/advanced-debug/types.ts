// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

/**
 * 高级调试功能的类型定义
 */

// ==================== 场景配置架构 ====================

/**
 * 场景配置 - 核心概念
 * 每个场景包含完整的节点配置和业务逻辑
 */
export interface ScenarioConfig {
  id: string;
  code: string; // 后端唯一标识，小写英文和下划线，如 'pd', 'sa', 'general_analysis'
  name: string;
  description: string;
  
  // 场景元数据
  metadata: {
    category: string; // 如: '需求分析', '代码审查', '技术调研'
    tags: string[];
    difficulty: 'simple' | 'medium' | 'complex';
    estimatedTime: number; // 预估执行时间（分钟）
    createdAt: Date;
    updatedAt: Date;
    author?: string;
    version: string;
  };
  
  // 场景级全局配置
  global: {
    project: string;
    codeTopicId: string;
    docTopicId: string;
    locale: 'zh-CN' | 'en-US' | 'ja-JP';
    debugMode: boolean;
    timeoutMinutes: number;
  };
  
  // 节点配置
  nodes: {
    planner: PlannerNodeConfig;
    researcher: ResearcherNodeConfig;
    reporter: ReporterNodeConfig;
    coder: CoderNodeConfig;
  };
  
  // 场景特定的流程控制
  workflow: {
    maxIterations: number;
    enableParallelExecution: boolean;
    failureStrategy: 'abort' | 'continue' | 'retry';
    retryCount: number;
  };
  
  // 场景变量和上下文
  variables: Record<string, unknown>;
  
  // 是否为预置场景（系统内置，不可删除）
  isPreset?: boolean;
  
  // 是否为模板
  isTemplate?: boolean;
}

/**
 * Planner节点配置
 */
export interface PlannerNodeConfig {
  // 基础配置
  maxIterations: number;
  
  // 简化的提示词配置 - 只保留系统提示词
  prompts: {
    systemPrompt: string;  // planner_system_prompt
  };
  
  // 场景特定参数
  scenarioParams: Record<string, unknown>;
}

/**
 * Researcher节点配置
 */
export interface ResearcherNodeConfig {
  // 基础配置
  maxSearchResults: number;
  searchDepth: number;
  
  // 搜索策略
  searchStrategy: {
    enableParallelSearch: boolean;
    searchQuality: number;
    cacheResults: boolean;
  };
  
  // 简化的提示词配置 - 只保留系统提示词
  prompts: {
    systemPrompt: string;  // researcher_system_prompt
  };
  
  // 工具配置
  tools: {
    enabled: string[]; // 启用的工具ID列表
    configs: Record<string, ToolConfig>; // 工具特定配置
  };
  
  // 输出配置
  output: {
    format: 'json' | 'markdown' | 'plain';
    debugOutput: boolean;
  };
  
  // 场景特定参数
  scenarioParams: Record<string, unknown>;
}

/**
 * Reporter节点配置
 */
export interface ReporterNodeConfig {
  // 简化的提示词配置 - 只保留系统提示词
  prompts: {
    systemPrompt: string;  // reporter_system_prompt
  };
  
  // 工具配置
  tools: {
    enabled: string[]; // 启用的工具ID列表
    configs: Record<string, ToolConfig>; // 工具特定配置
  };
  
  // 输出配置
  output: {
    format: 'markdown' | 'html' | 'plain';
    includeTableOfContents: boolean;
    includeMetadata: boolean;
    includeImageAttachments: boolean;
    debugOutput: boolean;
  };
  
  // 报告生成策略
  reportStrategy: {
    enableAutoStructuring: boolean;
    includeCitations: boolean;
    citationStyle: 'apa' | 'mla' | 'chicago' | 'custom';
    maxSectionDepth: number;
    enableTableGeneration: boolean;
  };
  
  // 场景特定参数
  scenarioParams: Record<string, unknown>;
}

/**
 * Coder节点配置
 */
export interface CoderNodeConfig {
  // 编程语言配置
  languages: {
    primary: 'python' | 'java' | 'javascript' | 'typescript' | 'auto';
    supportedLanguages: string[];
    enableAutoDetection: boolean;
  };
  
  // 简化的提示词配置 - 只保留系统提示词
  prompts: {
    systemPrompt: string;  // coder_system_prompt
  };
  
  // 工具配置
  tools: {
    enabled: string[]; // 启用的工具ID列表
    configs: Record<string, ToolConfig>; // 工具特定配置
  };
  
  // 输出配置
  output: {
    format: 'markdown' | 'json' | 'plain';
    includeCodeComments: boolean;
    includeTestCases: boolean;
    includeDocumentation: boolean;
    debugOutput: boolean;
  };
  
  // 代码分析策略
  analysisStrategy: {
    enableStaticAnalysis: boolean;
    performanceAnalysis: boolean;
    securityCheck: boolean;
    codeQuality: boolean;
  };
  
  // 场景特定参数
  scenarioParams: Record<string, unknown>;
}

/**
 * 工具配置
 */
export interface ToolConfig {
  id: string;
  name: string;
  enabled: boolean;
  priority: number;
  parameters: Record<string, unknown>;
  timeoutSeconds?: number;
  retryCount?: number;
}

/**
 * 场景模板
 * 可以从场景保存为模板，或从模板创建场景
 */
export interface ScenarioTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  
  // 模板元数据
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    author?: string;
    version: string;
    downloads: number;
    rating: number;
    isOfficial: boolean;
    isPublic: boolean;
  };
  
  // 场景配置（作为模板）
  template: Omit<ScenarioConfig, 'id' | 'code' | 'metadata'>;
  
  // 模板使用统计
  usage: {
    totalUses: number;
    lastUsed: Date;
    successRate: number;
  };
}

/**
 * 场景库管理
 */
export interface ScenarioLibrary {
  // 当前活跃场景（用于编辑）
  activeScenarioId: string | null;
  
  // 已启用的场景列表（用于 @ 选择器）
  enabledScenarios: string[];
  
  // 所有场景
  scenarios: Record<string, ScenarioConfig>;
  
  // 场景分类
  categories: {
    id: string;
    name: string;
    description: string;
    scenarios: string[]; // 场景ID列表
  }[];
  
  // 最近使用
  recentUsed: string[]; // 场景ID列表
  
  // 收藏夹
  favorites: string[]; // 场景ID列表
}

/**
 * 模板库管理
 */
export interface TemplateLibrary {
  // 所有模板
  templates: Record<string, ScenarioTemplate>;
  
  // 模板分类
  categories: {
    id: string;
    name: string;
    description: string;
    templates: string[]; // 模板ID列表
  }[];
  
  // 最近使用
  recentUsed: string[]; // 模板ID列表
  
  // 收藏夹
  favorites: string[]; // 模板ID列表
  
  // 导入历史
  importHistory: {
    templateId: string;
    importedAt: Date;
    source: string;
  }[];
}

/**
 * 调试配置（重构）
 */
export interface DebugConfiguration {
  // 场景库
  scenarioLibrary: ScenarioLibrary;
  
  // 模板库
  templateLibrary: TemplateLibrary;
  
  // 全局设置
  globalSettings: {
    defaultScenarioId: string | null;
    autoSaveInterval: number; // 自动保存间隔（秒）
    enablePreview: boolean;
    enableValidation: boolean;
    debugMode: boolean;
  };
  
  // 元数据
  metadata: {
    version: string;
    createdAt: Date;
    updatedAt: Date;
  };
}

/**
 * 流程状态监控
 */
export interface FlowStatus {
  scenarioId: string;
  status: 'idle' | 'running' | 'completed' | 'failed' | 'cancelled' | 'paused' | 'stopped';
  currentNode: 'planner' | 'researcher' | null;
  progress: number; // 0-100
  startedAt: Date;
  updatedAt: Date;
  endTime?: Date;
  
  // 节点状态
  nodeStatuses: {
    nodeId: string;
    nodeName: string;
    status: 'idle' | 'running' | 'completed' | 'failed' | 'cancelled' | 'paused' | 'stopped';
    startedAt?: Date;
    completedAt?: Date;
    duration?: number;
    message?: string;
  }[];
  
  // 错误信息
  errors: {
    nodeId: string;
    message: string;
    stack?: string;
    timestamp: Date;
  }[];
  
  // 日志
  logs: {
    timestamp: Date;
    level: 'info' | 'warn' | 'error' | 'debug';
    message: string;
    node?: string;
  }[];
  
  // 性能指标
  metrics: {
    plannerDuration?: number;
    researcherDuration?: number;
    totalDuration?: number;
    apiCalls: number;
    cacheHits: number;
  };
}

/**
 * 工具定义
 */
export interface ToolDefinition {
  id: string;
  name: string;
  description: string;
  category: 'search' | 'analysis' | 'data' | 'communication' | 'utility';
  
  // 工具参数定义
  parameters: {
    name: string;
    type: 'string' | 'number' | 'boolean' | 'array' | 'object';
    required: boolean;
    description: string;
    defaultValue?: unknown;
    validation?: {
      min?: number;
      max?: number;
      pattern?: string;
      options?: string[];
    };
  }[];
  
  // 工具适用性
  applicability: {
    nodes: ('planner' | 'researcher')[];
    scenarios: string[]; // 适用的场景类型
    conflicts: string[]; // 冲突的工具ID
  };
  
  // 性能特征
  performance: {
    averageLatency: number; // 平均延迟（毫秒）
    successRate: number; // 成功率
    costFactor: number; // 成本因子
  };
}

/**
 * 导出的配置
 */
export interface ExportedConfig {
  type: 'scenario' | 'template' | 'full';
  version: string;
  createdAt: Date;
  data: ScenarioConfig | ScenarioTemplate | DebugConfiguration;
  metadata: {
    exportedBy?: string;
    source: string;
    checksum: string;
  };
}

// ==================== UI状态管理 ====================

/**
 * UI状态
 */
export interface UIState {
  // 对话框状态
  isDialogOpen: boolean;
  
  // 当前标签页
  activeTab: 'scenarios' | 'editor' | 'templates' | 'monitor';
  
  // 场景编辑器状态
  editor: {
    isEditing: boolean;
    currentScenarioId: string | null;
    hasUnsavedChanges: boolean;
    validationErrors: string[];
  };
  
  // 搜索和过滤状态
  filters: {
    category: string | null;
    tags: string[];
    searchQuery: string;
    sortBy: 'name' | 'created' | 'used' | 'rating';
    sortOrder: 'asc' | 'desc';
  };
  
  // 选择状态
  selection: {
    selectedScenarios: string[];
    selectedTemplates: string[];
  };
}

/**
 * 预设场景分类
 */
export const SCENARIO_CATEGORIES = [
  {
    id: 'requirement_analysis',
    name: '需求分析',
    description: '分析和理解业务需求，制定技术方案',
    icon: '📋',
  },
  {
    id: 'product_design',
    name: '产品设计',
    description: '产品需求分析、设计规划、用户体验优化',
    icon: '🎨',
  },
  {
    id: 'system_architecture',
    name: '系统架构',
    description: '系统架构设计、技术选型、性能优化',
    icon: '🏗️',
  },
  {
    id: 'code_review',
    name: '代码审查',
    description: '代码质量检查、安全审计、性能优化建议',
    icon: '🔍',
  },
  {
    id: 'technical_research',
    name: '技术调研',
    description: '技术选型、框架对比、最佳实践研究',
    icon: '🔬',
  },
  {
    id: 'problem_solving',
    name: '问题解决',
    description: '故障排查、性能优化、bug修复',
    icon: '🛠️',
  },
  {
    id: 'documentation',
    name: '文档生成',
    description: '技术文档、API文档、使用指南生成',
    icon: '📚',
  },
  {
    id: 'learning',
    name: '学习研究',
    description: '新技术学习、概念理解、示例生成',
    icon: '🎓',
  },
  {
    id: 'testing',
    name: '测试场景',
    description: '自动化测试用例生成、边缘场景挖掘、测试数据构造',
    icon: '🧪',
  },
] as const;

/**
 * 预设工具分类
 */
export const TOOL_CATEGORIES = [
  {
    id: 'search',
    name: '搜索工具',
    description: '网络搜索、代码搜索、文档检索',
    icon: '🔍',
  },
  {
    id: 'analysis',
    name: '分析工具',
    description: '代码分析、数据分析、文本处理',
    icon: '📊',
  },
  {
    id: 'data',
    name: '数据工具',
    description: '数据库查询、API调用、文件处理',
    icon: '💾',
  },
  {
    id: 'communication',
    name: '通信工具',
    description: '邮件发送、通知推送、消息传递',
    icon: '📡',
  },
  {
    id: 'utility',
    name: '实用工具',
    description: '文件操作、格式转换、计算处理',
    icon: '🔧',
  },
] as const;

// 执行策略类型
export interface ExecutionStrategy {
  parallelExecution: boolean;
  retryEnabled: boolean;
  timeout: number;
  cacheEnabled: boolean;
}

// 节点配置类型
export interface NodeConfig {
  nodeName: 'planner' | 'researcher' | 'reporter' | 'coder';
  
  // 基础配置字段 - 兼容现有组件
  maxIterations?: number;
  enableAutoRetry?: boolean;
  timeoutSeconds?: number;
  tools?: string[];
  priority?: string;
  debugOutput?: boolean;
  
  // Researcher特定字段
  maxSearchResults?: number;
  searchDepth?: number;
  enableParallelSearch?: boolean;
  cacheResults?: boolean;
  searchQuality?: number;
  
  // 原有的详细配置字段
  promptParameters?: {
    maxStepNum?: number;
    locale: string;
    analysisDepth: 'shallow' | 'deep' | 'complete';
    outputDetail: 'concise' | 'standard' | 'detailed';
    [key: string]: unknown;
  };
  toolConfigs?: Record<string, ToolConfig>;
  executionStrategy?: ExecutionStrategy;
}

// 全局配置类型
export interface GlobalConfig {
  project: string;
  codeTopicId: string;
  docTopicId: string;
  maxPlanIterations: number;
  maxStepNum: number;
  maxSearchResults: number;
  enableBackgroundInvestigation: boolean;
  debugMode: boolean;
  locale: string;
}

// 配置模板类型
export interface ConfigTemplate {
  // 模板基本信息
  id: string;
  name: string;
  description: string;
  category: 'system' | 'custom' | 'imported' | 'shared';
  author: string;
  version: string;
  createdAt: Date;
  updatedAt: Date;
  tags: string[];
  
  // 模板图标和颜色
  icon: string;
  color: string;
  
  // 使用统计
  usage: {
    useCount: number;
    lastUsed: Date;
    avgRating?: number;
  };
  
  // 完整配置数据
  config: DebugConfiguration;
  
  // 模板元数据
  metadata: {
    supportedLanguages: string[];
    requiredTools: string[];
    targetDomains: string[];
    complexity: 'simple' | 'moderate' | 'complex';
    estimatedSetupTime: number; // 分钟
  };
}

// @选择器设置类型
export interface SelectorSettings {
  enableSmartRecommendation: boolean;
  recentSelectionLimit: number;
  autoApplyTemplate: boolean;
  showTemplatePreview: boolean;
}

// 存储配置类型
export interface StoredDebugConfig {
  version: string;
  timestamp: number;
  
  // 模板库管理
  templateLibrary: TemplateLibrary;
  
  // 当前会话配置
  currentConfig: DebugConfiguration;
  
  // @选择器设置
  selectorSettings: SelectorSettings;
}

// 导出模板类型
export interface ExportedTemplate {
  formatVersion: "1.0";
  exportedAt: Date;
  exportedBy: string;
  template: ConfigTemplate;
  metadata: {
    sourceSystem: string;
    sourceVersion: string;
    checksum: string;
  };
}

// 批量导出模板类型
export interface ExportedTemplateBundle {
  formatVersion: "1.0";
  exportedAt: Date;
  exportedBy: string;
  templates: ConfigTemplate[];
  dependencies: {
    requiredTools: string[];
    recommendedSettings: Record<string, unknown>;
  };
  metadata: {
    bundleName: string;
    description: string;
    sourceSystem: string;
    sourceVersion: string;
    checksum: string;
  };
}

// API响应类型
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 调试事件类型
export type DebugEvent = 
  | { type: 'node_start'; nodeId: string; timestamp: Date }
  | { type: 'node_complete'; nodeId: string; duration: number; timestamp: Date }
  | { type: 'tool_call'; toolName: string; nodeId: string; timestamp: Date }
  | { type: 'error'; nodeId: string; error: string; timestamp: Date }
  | { type: 'config_change'; changes: Partial<DebugConfiguration>; timestamp: Date }; 