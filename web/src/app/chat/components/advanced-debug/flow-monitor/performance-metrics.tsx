"use client";

import { BarChart3, TrendingUp } from "lucide-react";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

import type { FlowStatus } from "../types";

export function PerformanceMetrics({ 
  flowStatus: _flowStatus 
}: { 
  flowStatus: FlowStatus | null; 
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2">
          <BarChart3 className="h-4 w-4" />
          性能指标
        </CardTitle>
        <CardDescription>
          监控流程执行的性能和资源使用情况
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-center py-12">
          <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-muted-foreground mb-2">
            性能监控
          </h3>
          <p className="text-sm text-muted-foreground">
            此功能正在开发中，将提供详细的性能指标和统计图表
          </p>
        </div>
      </CardContent>
    </Card>
  );
} 