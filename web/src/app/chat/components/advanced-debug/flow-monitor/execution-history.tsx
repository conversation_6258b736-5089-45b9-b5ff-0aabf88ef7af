"use client";

import { History, Clock } from "lucide-react";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

export function ExecutionHistory() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2">
          <History className="h-4 w-4" />
          执行历史
        </CardTitle>
        <CardDescription>
          查看过往的流程执行记录和统计信息
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-center py-12">
          <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-muted-foreground mb-2">
            执行历史
          </h3>
          <p className="text-sm text-muted-foreground">
            此功能正在开发中，将提供执行历史记录和分析报告
          </p>
        </div>
      </CardContent>
    </Card>
  );
} 