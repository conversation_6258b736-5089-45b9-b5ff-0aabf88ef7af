"use client";

import {
  Act<PERSON>,
  AlertTriangle,
  Bar<PERSON>hart3,
  CheckCircle,
  Clock,
  Eye,
  History,
  Pause,
  Play,
  Square,
} from "lucide-react";
import { useState, useEffect } from "react";

import { Badge } from "~/components/ui/badge";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Progress } from "~/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
// 暂未使用 import { Separator } from "~/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { useDebugStore } from "~/core/store/debug-store";

import { ExecutionHistory } from "./execution-history";
import { FlowVisualization } from "./flow-visualization";
import { PerformanceMetrics } from "./performance-metrics";

export function FlowMonitor() {
  // 状态管理
  const activeScenario = useDebugStore((state) => state.getActiveScenario());
  const flowStatus = useDebugStore((state) => state.flowStatus);
  const updateFlowStatus = useDebugStore((state) => state.updateFlowStatus);
  
  const [activeTab, setActiveTab] = useState("status");
  const [autoRefresh, setAutoRefresh] = useState(true);

  // 模拟流程状态更新
  useEffect(() => {
    if (!autoRefresh || !flowStatus) return;

    const interval = setInterval(() => {
      // 这里应该从实际的流程服务获取状态
      // 目前是模拟数据
    }, 2000);

    return () => clearInterval(interval);
  }, [autoRefresh, flowStatus]);

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'text-blue-600 bg-blue-50';
      case 'completed':
        return 'text-green-600 bg-green-50';
      case 'failed':
        return 'text-red-600 bg-red-50';
      case 'paused':
        return 'text-yellow-600 bg-yellow-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Play className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'failed':
        return <AlertTriangle className="h-4 w-4" />;
      case 'paused':
        return <Pause className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  // 模拟控制动作
  const handleFlowControl = (action: string) => {
    // 这里应该发送到实际的流程服务
    console.log(`Flow control action: ${action}`);
    
    // 模拟状态更新
    if (flowStatus) {
      updateFlowStatus({
        ...flowStatus,
        status: action === 'start' ? 'running' : action === 'pause' ? 'paused' : 'stopped',
        updatedAt: new Date(),
      });
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* 头部状态 */}
      <div className="p-6 border-b space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary/10">
              <Activity className="h-4 w-4 text-primary" />
            </div>
            <div>
              <h2 className="text-lg font-semibold">流程监控</h2>
              <p className="text-sm text-muted-foreground">
                实时监控分析流程的执行状态和性能指标
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Select
              value={autoRefresh ? "auto" : "manual"}
              onValueChange={(value) => setAutoRefresh(value === "auto")}
            >
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="auto">自动刷新</SelectItem>
                <SelectItem value="manual">手动刷新</SelectItem>
              </SelectContent>
            </Select>
            
            {flowStatus && (
              <div className="flex items-center gap-1">
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => handleFlowControl('start')}
                  disabled={flowStatus.status === 'running'}
                >
                  <Play className="h-3 w-3 mr-1" />
                  启动
                </Button>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => handleFlowControl('pause')}
                  disabled={flowStatus.status !== 'running'}
                >
                  <Pause className="h-3 w-3 mr-1" />
                  暂停
                </Button>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => handleFlowControl('stop')}
                  disabled={flowStatus.status === 'stopped'}
                >
                  <Square className="h-3 w-3 mr-1" />
                  停止
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* 当前场景信息 */}
        {activeScenario && (
          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium">当前场景: {activeScenario.name}</div>
                <div className="text-sm text-muted-foreground">{activeScenario.description}</div>
              </div>
              {flowStatus && (
                <div className="flex items-center gap-2">
                  <Badge className={getStatusColor(flowStatus.status)}>
                    {getStatusIcon(flowStatus.status)}
                    <span className="ml-1 capitalize">{flowStatus.status}</span>
                  </Badge>
                  <div className="text-sm text-muted-foreground">
                    执行时间: {Math.floor((new Date().getTime() - new Date(flowStatus.startedAt).getTime()) / 1000)}s
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 整体进度 */}
        {flowStatus && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>整体进度</span>
              <span>{flowStatus.progress}%</span>
            </div>
            <Progress value={flowStatus.progress} className="h-2" />
          </div>
        )}
      </div>

      {/* 监控内容 */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <div className="px-6 pt-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="status" className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                实时状态
              </TabsTrigger>
              <TabsTrigger value="visualization" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                流程可视化
              </TabsTrigger>
              <TabsTrigger value="metrics" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                性能指标
              </TabsTrigger>
              <TabsTrigger value="history" className="flex items-center gap-2">
                <History className="h-4 w-4" />
                执行历史
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="flex-1 overflow-auto">
            {/* 实时状态 */}
            <TabsContent value="status" className="m-0 p-6 space-y-6">
              {!flowStatus ? (
                <div className="text-center py-12">
                  <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-muted-foreground mb-2">
                    暂无流程运行
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    开始一个分析任务来监控流程状态
                  </p>
                  <Button onClick={() => handleFlowControl('start')}>
                    <Play className="h-4 w-4 mr-2" />
                    启动流程
                  </Button>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* 节点状态 */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">节点执行状态</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {flowStatus.nodeStatuses.map((nodeStatus) => (
                        <div key={nodeStatus.nodeId} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className={`p-2 rounded-lg ${getStatusColor(nodeStatus.status)}`}>
                              {getStatusIcon(nodeStatus.status)}
                            </div>
                            <div>
                              <div className="font-medium">{nodeStatus.nodeName}</div>
                              <div className="text-sm text-muted-foreground">
                                {nodeStatus.message ?? `状态: ${nodeStatus.status}`}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium">
                              {nodeStatus.duration ? `${nodeStatus.duration}ms` : '-'}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {nodeStatus.startedAt ? new Date(nodeStatus.startedAt).toLocaleTimeString() : '-'}
                            </div>
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>

                  {/* 错误信息 */}
                  {flowStatus.errors.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base flex items-center gap-2">
                          <AlertTriangle className="h-4 w-4 text-red-500" />
                          错误信息
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          {flowStatus.errors.map((error, index) => (
                            <div key={index} className="bg-red-50 border border-red-200 rounded-lg p-3">
                              <div className="font-medium text-red-800">{error.message}</div>
                              <div className="text-sm text-red-600">
                                节点: {error.nodeId} | 时间: {new Date(error.timestamp).toLocaleString()}
                              </div>
                              {error.stack && (
                                <details className="mt-2">
                                  <summary className="cursor-pointer text-sm text-red-600">详细错误信息</summary>
                                  <pre className="mt-1 text-xs bg-red-100 p-2 rounded overflow-auto">
                                    {error.stack}
                                  </pre>
                                </details>
                              )}
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              )}
            </TabsContent>

            {/* 流程可视化 */}
            <TabsContent value="visualization" className="m-0 p-6">
              <FlowVisualization scenario={activeScenario} flowStatus={flowStatus} />
            </TabsContent>

            {/* 性能指标 */}
            <TabsContent value="metrics" className="m-0 p-6">
              <PerformanceMetrics flowStatus={flowStatus} />
            </TabsContent>

            {/* 执行历史 */}
            <TabsContent value="history" className="m-0 p-6">
              <ExecutionHistory />
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
} 