"use client";

import { Activity } from "lucide-react";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

import type { ScenarioConfig, FlowStatus } from "../types";

export function FlowVisualization({ 
  scenario: _scenario, 
  flowStatus: _flowStatus 
}: { 
  scenario: ScenarioConfig | null; 
  flowStatus: FlowStatus | null; 
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2">
          <Activity className="h-4 w-4" />
          流程可视化
        </CardTitle>
        <CardDescription>
          以图形化方式展示流程执行状态
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-center py-12">
          <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-muted-foreground mb-2">
            流程可视化
          </h3>
          <p className="text-sm text-muted-foreground">
            此功能正在开发中，将提供节点流程图和实时状态展示
          </p>
        </div>
      </CardContent>
    </Card>
  );
} 