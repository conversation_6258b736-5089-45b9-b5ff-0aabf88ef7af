// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

"use client";

import { <PERSON><PERSON>, Layers, BookOpen, Activity } from "lucide-react";
import { useState, useCallback } from "react";

import { Tooltip } from "~/components/deer-flow/tooltip";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { Tabs, TabsContent } from "~/components/ui/tabs";
import { useDebugStore } from "~/core/store/debug-store";
import { cn } from "~/lib/utils";

import { ScenarioLibrary } from "./scenario-library";

// 临时组件 - 稍后会创建具体实现
function TemplateLibrary() {
  return (
    <div className="h-full flex flex-col p-6 space-y-4">
      <div className="flex-shrink-0">
        <h3 className="text-lg font-semibold">模板库</h3>
        <p className="text-muted-foreground">管理场景模板，导入导出配置</p>
      </div>
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-sm text-muted-foreground">
          模板库界面开发中...
        </div>
      </div>
    </div>
  );
}

function FlowMonitor() {
  return (
    <div className="h-full flex flex-col p-6 space-y-4">
      <div className="flex-shrink-0">
        <h3 className="text-lg font-semibold">流程监控</h3>
        <p className="text-muted-foreground">实时监控分析流程的执行状态</p>
      </div>
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-sm text-muted-foreground">
          流程监控界面开发中...
        </div>
      </div>
    </div>
  );
}

// 新的标签页定义 - 以场景为核心，去掉独立的场景编辑器标签页
const DEBUG_TABS = [
  {
    id: 'scenarios',
    label: '场景库',
    icon: Layers,
    description: '管理和切换不同的分析场景',
    component: ScenarioLibrary,
  },
  {
    id: 'templates',
    label: '模板库',
    icon: BookOpen,
    description: '管理场景模板，导入导出配置',
    badge: 'NEW' as const,
    component: TemplateLibrary,
  },
  {
    id: 'monitor',
    label: '流程监控',
    icon: Activity,
    description: '实时监控分析流程的执行状态',
    component: FlowMonitor,
  },
] as const;

export function AdvancedDebugDialog() {
  const {
    ui,
    config,
    setDialogOpen,
    setActiveTab,
    getActiveScenario,
  } = useDebugStore();

  const [hasChanges, setHasChanges] = useState(false);

  const handleSave = useCallback(() => {
    if (hasChanges) {
      // TODO: 保存配置到后端
      console.log('Saving debug configuration:', config);
      setHasChanges(false);
    }
    setDialogOpen(false);
  }, [hasChanges, config, setDialogOpen]);

  const handleClose = useCallback(() => {
    setHasChanges(false);
    setDialogOpen(false);
  }, [setDialogOpen]);

  // 获取当前活跃场景信息
  const activeScenario = getActiveScenario();

  return (
    <Dialog open={ui.isDialogOpen} onOpenChange={setDialogOpen}>
      <Tooltip title="高级调试">
        <DialogTrigger asChild>
          <Button variant="ghost" size="icon">
            <Wrench />
          </Button>
        </DialogTrigger>
      </Tooltip>
      <DialogContent className="sm:max-w-[1400px] h-[800px] max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <Wrench className="h-5 w-5" />
            DeerFlow 高级调试
          </DialogTitle>
          <DialogDescription className="flex items-center gap-2">
            专业级调试和配置管理工具
            {activeScenario && (
              <Badge variant="outline" className="ml-2">
                当前场景: {activeScenario.name}
              </Badge>
            )}
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={ui.activeTab} className="flex-1 flex flex-col min-h-0">
          <div className="flex-1 flex w-full border-y overflow-hidden">
            {/* 左侧标签页列表 */}
            <div className="w-60 shrink-0 border-r bg-muted/30">
              <div className="p-2 space-y-1">
                {DEBUG_TABS.map((tab) => {
                  const IconComponent = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      className={cn(
                        "w-full flex items-center gap-3 px-3 py-3 rounded-md text-left transition-colors hover:bg-accent hover:text-accent-foreground",
                        ui.activeTab === tab.id &&
                          "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground"
                      )}
                      onClick={() => setActiveTab(tab.id)}
                    >
                      <IconComponent className="h-5 w-5 shrink-0" />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium truncate">{tab.label}</span>
                          {'badge' in tab && tab.badge && (
                            <Badge
                              variant="secondary"
                              className={cn(
                                "px-1.5 py-0 text-xs",
                                ui.activeTab === tab.id &&
                                  "bg-primary-foreground/20 text-primary-foreground"
                              )}
                            >
                              {tab.badge}
                            </Badge>
                          )}
                        </div>
                        <p className={cn(
                          "text-xs text-muted-foreground mt-0.5 truncate",
                          ui.activeTab === tab.id && "text-primary-foreground/80"
                        )}>
                          {tab.description}
                        </p>
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
            
            {/* 右侧内容区域 */}
            <div className="flex-1 min-w-0 flex flex-col overflow-hidden">
              {DEBUG_TABS.map((tab) => (
                <TabsContent 
                  key={tab.id} 
                  value={tab.id}
                  className="flex-1 m-0 overflow-hidden"
                >
                  <tab.component />
                </TabsContent>
              ))}
            </div>
          </div>
        </Tabs>

        <DialogFooter className="flex-shrink-0 border-t">
          <div className="flex items-center justify-between w-full">
            <div className="text-xs text-muted-foreground">
              配置版本: 2.0.0 · 更新时间: {new Date().toLocaleString('zh-CN')}
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleClose}>
                取消
              </Button>
              <Button onClick={handleSave} disabled={!hasChanges}>
                关闭
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 