"use client";

import {
  <PERSON>,
  <PERSON>Off,
  Download,
  Trash2,
  Play,
  Clock,
  MoreH<PERSON>zontal,
  Crown,
  Heart,
  Users
} from "lucide-react";
import { useState } from "react";

import { Tooltip } from "~/components/deer-flow/tooltip";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { cn } from "~/lib/utils";

import { SCENARIO_CATEGORIES } from "../types";
import type { ScenarioTemplate } from "../types";

interface TemplateCardProps {
  template: ScenarioTemplate;
  isFavorite: boolean;
  onUse: () => void;
  onFavorite: () => void;
  onExport: () => void;
  onDelete: () => void;
}

export function TemplateCard({
  template,
  isFavorite,
  onUse,
  onFavorite,
  onExport,
  onDelete,
}: TemplateCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  // 获取分类信息
  const categoryInfo = SCENARIO_CATEGORIES.find(cat => cat.id === template.category);

  // 评分颜色
  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return 'text-green-600';
    if (rating >= 3.5) return 'text-yellow-600';
    return 'text-gray-600';
  };

  // 格式化下载数
  const formatDownloads = (downloads: number) => {
    if (downloads >= 1000) {
      return `${(downloads / 1000).toFixed(1)}k`;
    }
    return downloads.toString();
  };

  return (
    <Card
      className={cn(
        "relative transition-all duration-200 cursor-pointer",
        isHovered && "shadow-md"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onUse}
    >
      {/* 收藏标记 */}
      {isFavorite && (
        <div className="absolute top-2 right-2 z-10">
          <Heart className="h-4 w-4 text-red-500 fill-red-500" />
        </div>
      )}

      {/* 官方认证标记 */}
      {template.metadata.isOfficial && (
        <div className="absolute top-2 left-2 z-10">
          <Crown className="h-4 w-4 text-yellow-500 fill-yellow-500" />
        </div>
      )}

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-base font-semibold flex items-center gap-2 mb-1">
              {categoryInfo && (
                <span className="text-lg">{categoryInfo.icon}</span>
              )}
              <span className="truncate">{template.name}</span>
              {template.metadata.isOfficial && (
                <Badge variant="default" className="text-xs bg-yellow-500">官方</Badge>
              )}
            </CardTitle>
            <CardDescription className="text-sm line-clamp-2">
              {template.description}
            </CardDescription>
          </div>

          {/* 操作菜单 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onUse(); }}>
                <Play className="h-4 w-4 mr-2" />
                使用模板
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onFavorite(); }}>
                {isFavorite ? (
                  <>
                    <StarOff className="h-4 w-4 mr-2" />
                    取消收藏
                  </>
                ) : (
                  <>
                    <Star className="h-4 w-4 mr-2" />
                    添加收藏
                  </>
                )}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onExport(); }}>
                <Download className="h-4 w-4 mr-2" />
                导出模板
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-destructive"
                onClick={(e) => { e.stopPropagation(); onDelete(); }}
                disabled={template.metadata.isOfficial}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                删除模板
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="pt-0 space-y-3">
        {/* 模板标签 */}
        {template.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {template.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {template.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{template.tags.length - 3}
              </Badge>
            )}
          </div>
        )}

        {/* 统计信息 */}
        <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <Users className="h-3 w-3" />
            <span>{formatDownloads(template.metadata.downloads)} 次使用</span>
          </div>
          <div className="flex items-center gap-1">
            <Star className={cn("h-3 w-3", getRatingColor(template.metadata.rating))} />
            <span>{template.metadata.rating.toFixed(1)} 评分</span>
          </div>
        </div>

        {/* 使用统计 */}
        <div className="flex gap-2 text-xs">
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 rounded-full bg-green-500" />
            <span className="text-muted-foreground">
              成功率 {(template.usage.successRate * 100).toFixed(0)}%
            </span>
          </div>
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-muted-foreground" />
            <span className="text-muted-foreground">
              {template.usage.totalUses} 次使用
            </span>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex gap-2 pt-2">
          <Button
            size="sm"
            className="flex-1"
            onClick={(e) => { e.stopPropagation(); onUse(); }}
          >
            <Play className="h-3 w-3 mr-1" />
            使用模板
          </Button>
          <Tooltip title="添加收藏">
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => { e.stopPropagation(); onFavorite(); }}
            >
              {isFavorite ? (
                <Heart className="h-3 w-3 text-red-500 fill-red-500" />
              ) : (
                <Heart className="h-3 w-3" />
              )}
            </Button>
          </Tooltip>
          <Tooltip title="导出模板">
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => { e.stopPropagation(); onExport(); }}
            >
              <Download className="h-3 w-3" />
            </Button>
          </Tooltip>
        </div>

        {/* 更新时间 */}
        <div className="text-xs text-muted-foreground border-t pt-2">
          创建于 {new Date(template.metadata.createdAt).toLocaleDateString()}
        </div>
      </CardContent>
    </Card>
  );
} 