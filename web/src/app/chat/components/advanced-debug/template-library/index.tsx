"use client";

import {
  Book<PERSON><PERSON>,
  Filter,
  Plus,
  Search,
  SortAsc,
  Upload,
} from "lucide-react";
import React, { useState, useMemo } from "react";

import { Tooltip } from "~/components/deer-flow/tooltip";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { useDebugStore } from "~/core/store/debug-store";

import { SCENARIO_CATEGORIES } from "../types";
import type { ScenarioTemplate } from "../types";

import { CreateTemplateDialog } from "./create-template-dialog";
import { TemplateCard } from "./template-card";

export function TemplateLibrary() {
  const {
    config,
    ui,
    setFilters,
    addToFavorites,
    removeFromFavorites,
    deleteTemplate,
    importTemplate,
    exportTemplate,
    createScenarioFromTemplate,
    setActiveScenario,
    setActiveTab,
  } = useDebugStore();

  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState(ui.filters.searchQuery);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  // 获取过滤后的模板列表
  const filteredTemplates = useMemo(() => {
    const templates = Object.values(config.templateLibrary.templates);

    return templates.filter((template) => {
      // 搜索过滤
      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase();
        if (
          !template.name.toLowerCase().includes(query) &&
          !template.description.toLowerCase().includes(query) &&
          !template.tags.some(tag => tag.toLowerCase().includes(query))
        ) {
          return false;
        }
      }

      // 分类过滤
      if (ui.filters.category && template.category !== ui.filters.category) {
        return false;
      }

      // 标签过滤
      if (ui.filters.tags.length > 0) {
        if (!ui.filters.tags.some(tag => template.tags.includes(tag))) {
          return false;
        }
      }

      return true;
    });
  }, [config.templateLibrary.templates, searchQuery, ui.filters]);

  // 排序后的模板列表
  const sortedTemplates = useMemo(() => {
    const sorted = [...filteredTemplates];

    switch (ui.filters.sortBy) {
      case 'name':
        sorted.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'created':
        sorted.sort((a, b) => new Date(b.metadata.createdAt).getTime() - new Date(a.metadata.createdAt).getTime());
        break;
      case 'used':
        // 按使用次数排序
        sorted.sort((a, b) => b.usage.totalUses - a.usage.totalUses);
        break;
      default:
        break;
    }

    if (ui.filters.sortOrder === 'desc') {
      sorted.reverse();
    }

    return sorted;
  }, [filteredTemplates, ui.filters.sortBy, ui.filters.sortOrder]);

  // 模板操作处理
  const handleTemplateUse = (template: ScenarioTemplate) => {
    const scenarioName = `${template.name} (${new Date().toLocaleDateString()})`;
    const scenarioId = createScenarioFromTemplate(template.id, scenarioName);
    setActiveScenario(scenarioId);
    setActiveTab('editor');
  };

  const handleTemplateFavorite = (template: ScenarioTemplate) => {
    const isFavorite = config.templateLibrary.favorites.includes(template.id);
    if (isFavorite) {
      removeFromFavorites('template', template.id);
    } else {
      addToFavorites('template', template.id);
    }
  };

  const handleTemplateDelete = (template: ScenarioTemplate) => {
    if (confirm(`确定要删除模板"${template.name}"吗？此操作不可撤销。`)) {
      deleteTemplate(template.id);
    }
  };

  const handleExportTemplate = (template: ScenarioTemplate) => {
    const exportData = exportTemplate(template.id);
    if (exportData) {
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${template.name.replace(/[^a-zA-Z0-9]/g, '_')}_template.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const handleImportTemplate = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const templateData = JSON.parse(e.target?.result as string);
          importTemplate(templateData);
        } catch (error) {
          alert('模板文件格式错误，导入失败。');
          console.error('Failed to import template:', error);
        }
      };
      reader.readAsText(file);
    }
    // 重置文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    setFilters({ searchQuery: value });
  };

  return (
    <div className="h-full flex flex-col">
      {/* 头部操作区 */}
      <div className="p-6 border-b space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary/10">
              <BookOpen className="h-4 w-4 text-primary" />
            </div>
            <div>
              <h2 className="text-lg font-semibold">模板库</h2>
              <p className="text-sm text-muted-foreground">
                管理和分享场景模板，总共 {Object.keys(config.templateLibrary.templates).length} 个模板
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Tooltip title="导入模板">
              <Button variant="outline" onClick={() => fileInputRef.current?.click()}>
                <Upload className="h-4 w-4 mr-2" />
                导入
              </Button>
            </Tooltip>
            <Button onClick={() => setCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              创建模板
            </Button>
          </div>
        </div>

        {/* 搜索和过滤 */}
        <div className="flex gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索模板名称、描述或标签..."
              value={searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select
            value={ui.filters.category ?? 'all'}
            onValueChange={(value) => setFilters({ category: value === 'all' ? null : value })}
          >
            <SelectTrigger className="w-48">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="所有分类" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有分类</SelectItem>
              {SCENARIO_CATEGORIES.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  <span className="mr-2">{category.icon}</span>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={ui.filters.sortBy}
            onValueChange={(value) => setFilters({ sortBy: value as typeof ui.filters.sortBy })}
          >
            <SelectTrigger className="w-32">
              <SortAsc className="h-4 w-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name">名称</SelectItem>
              <SelectItem value="created">创建时间</SelectItem>
              <SelectItem value="used">使用次数</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* 模板列表 */}
      <div className="flex-1 overflow-auto p-6">
        {sortedTemplates.length === 0 ? (
          <div className="text-center py-12">
            <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-muted-foreground mb-2">
              {searchQuery.trim() || ui.filters.category ? '没有找到匹配的模板' : '还没有模板'}
            </h3>
            <p className="text-sm text-muted-foreground mb-4">
              {searchQuery.trim() || ui.filters.category
                ? '尝试调整搜索条件或过滤器'
                : '创建第一个模板或从场景库保存模板'
              }
            </p>
            {!searchQuery.trim() && !ui.filters.category && (
              <Button onClick={() => setCreateDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                创建第一个模板
              </Button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
            {sortedTemplates.map((template) => (
              <TemplateCard
                key={template.id}
                template={template}
                isFavorite={config.templateLibrary.favorites.includes(template.id)}
                onUse={() => handleTemplateUse(template)}
                onFavorite={() => handleTemplateFavorite(template)}
                onExport={() => handleExportTemplate(template)}
                onDelete={() => handleTemplateDelete(template)}
              />
            ))}
          </div>
        )}
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".json"
        onChange={handleImportTemplate}
        style={{ display: 'none' }}
      />

      {/* 创建模板对话框 */}
      <CreateTemplateDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
      />
    </div>
  );
} 