"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { Plus, Save, X } from "lucide-react";
import { useEffect, useCallback } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Badge } from "~/components/ui/badge";
import { But<PERSON> } from "~/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Textarea } from "~/components/ui/textarea";
import { useDebugStore } from "~/core/store/debug-store";

import { SCENARIO_CATEGORIES } from "../types";

// 表单验证
const createTemplateSchema = z.object({
  name: z.string().min(1, "模板名称不能为空").max(50, "模板名称不能超过50个字符"),
  description: z.string().min(10, "描述至少需要10个字符").max(200, "描述不能超过200个字符"),
  category: z.string().min(1, "请选择一个分类"),
  tags: z.string(),
  sourceScenario: z.string().optional(),
});

type CreateTemplateFormData = z.infer<typeof createTemplateSchema>;

interface CreateTemplateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CreateTemplateDialog({ open, onOpenChange }: CreateTemplateDialogProps) {
  const { config, saveScenarioAsTemplate } = useDebugStore();
  
  // 移除未使用的isSaving状态

  // 表单配置
  const form = useForm<CreateTemplateFormData>({
    resolver: zodResolver(createTemplateSchema),
    defaultValues: {
      name: "",
      description: "",
      category: "",
      tags: "",
      sourceScenario: "",
    },
  });

  // 获取可用场景列表
  const availableScenarios = Object.values(config.scenarioLibrary.scenarios);

  // 重置表单
  const resetForm = useCallback(() => {
    form.reset({
      name: "",
      description: "",
      category: "",
      tags: "",
      sourceScenario: "",
    });
  }, [form]);

  // 当对话框打开时重置表单
  useEffect(() => {
    if (open) {
      resetForm();
    }
  }, [open, resetForm]);

  // 处理表单提交
  const handleSubmit = async (formData: CreateTemplateFormData) => {
    if (!formData.name?.trim()) {
      return;
    }

    try {
      // 移除未使用的newTemplate变量

      const templateId = saveScenarioAsTemplate(
        formData.sourceScenario!,
        formData.name,
        formData.description
      );

      if (templateId) {
        onOpenChange(false);
        resetForm();
      }
    } catch (error) {
      console.error('创建模板失败:', error);
    }
  };

  // 监听源场景变化，自动填充部分字段
  const sourceScenario = form.watch("sourceScenario");
  const scenarios = config.scenarioLibrary.scenarios;
  useEffect(() => {
    if (sourceScenario) {
      const scenario = scenarios[sourceScenario];
      if (scenario) {
        // 自动填充名称和描述
        if (!form.getValues("name")) {
          form.setValue("name", `${scenario.name} 模板`);
        }
        if (!form.getValues("description")) {
          form.setValue("description", `基于场景"${scenario.name}"创建的模板`);
        }
        if (!form.getValues("category")) {
          form.setValue("category", scenario.metadata.category);
        }
        if (!form.getValues("tags")) {
          const tags = scenario.metadata.tags.join(", ");
          form.setValue("tags", tags);
        }
      }
    }
  }, [sourceScenario, scenarios, form]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            创建新模板
          </DialogTitle>
          <DialogDescription>
            选择一个现有场景并创建可重复使用的模板
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* 源场景选择 */}
            <FormField
              control={form.control}
              name="sourceScenario"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>基础场景 *</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择一个场景作为模板基础" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {availableScenarios.length === 0 ? (
                        <div className="p-4 text-center text-muted-foreground">
                          暂无可用场景
                        </div>
                      ) : (
                        availableScenarios.map((scenario) => {
                          const categoryInfo = SCENARIO_CATEGORIES.find(cat => cat.id === scenario.metadata.category);
                          return (
                            <SelectItem key={scenario.id} value={scenario.id}>
                              <div className="flex items-center gap-2">
                                {categoryInfo && <span>{categoryInfo.icon}</span>}
                                <span>{scenario.name}</span>
                                <Badge variant="outline" className="text-xs">
                                  {categoryInfo?.name}
                                </Badge>
                              </div>
                            </SelectItem>
                          );
                        })
                      )}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    模板将基于所选场景的配置创建
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 模板基本信息 */}
            <div className="grid grid-cols-1 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>模板名称 *</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="请输入模板名称"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      简洁明了的模板名称，便于识别和搜索
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>模板描述 *</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="请详细描述模板的用途和特点..."
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      详细说明模板的用途、适用场景和特点
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* 分类和标签 */}
            <div className="grid grid-cols-1 gap-4">
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>分类 *</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择模板分类" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {SCENARIO_CATEGORIES.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            <div className="flex items-center gap-2">
                              <span>{category.icon}</span>
                              <span>{category.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      选择最适合的分类以便用户查找
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>标签</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="请输入标签，用逗号分隔"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      添加相关标签，用逗号分隔，便于搜索和分类
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* 预览信息 */}
            {form.watch("sourceScenario") && (
              <div className="bg-muted/50 rounded-lg p-4">
                <h4 className="font-medium mb-2">源场景信息</h4>
                {(() => {
                  const scenarioId = form.watch("sourceScenario");
                  const scenario = scenarioId ? config.scenarioLibrary.scenarios[scenarioId] : null;
                  const categoryInfo = SCENARIO_CATEGORIES.find(cat => cat.id === scenario?.metadata.category);
                  
                  if (!scenario) return null;
                  
                  return (
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center gap-2">
                        {categoryInfo && <span>{categoryInfo.icon}</span>}
                        <span className="font-medium">{scenario.name}</span>
                        <Badge variant="outline">{categoryInfo?.name}</Badge>
                      </div>
                      <p className="text-muted-foreground">{scenario.description}</p>
                      <div className="flex flex-wrap gap-1">
                        {scenario.metadata.tags.map((tag: string) => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  );
                })()}
              </div>
            )}

            {/* 操作按钮 */}
            <div className="flex gap-2 pt-4">
              <Button 
                type="submit" 
                disabled={!form.watch("sourceScenario")}
                className="flex-1"
              >
                <Save className="h-4 w-4 mr-2" />
                创建模板
              </Button>
              <Button 
                type="button" 
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                <X className="h-4 w-4 mr-2" />
                取消
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 