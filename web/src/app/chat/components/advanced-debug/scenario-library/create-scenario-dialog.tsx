"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { Plus } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Badge } from "~/components/ui/badge";
import { <PERSON><PERSON> } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Textarea } from "~/components/ui/textarea";
import { useDebugStore } from "~/core/store/debug-store";

import { SCENARIO_CATEGORIES } from "../types";

// 表单验证模式
const createScenarioSchema = z.object({
  name: z.string().min(1, "场景名称不能为空").max(50, "场景名称不能超过50个字符"),
  description: z.string().min(1, "场景描述不能为空").max(200, "场景描述不能超过200个字符"),
  category: z.string().min(1, "请选择场景分类"),
  basedOn: z.string().optional(),
});

type CreateScenarioFormData = z.infer<typeof createScenarioSchema>;

interface CreateScenarioDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CreateScenarioDialog({ open, onOpenChange }: CreateScenarioDialogProps) {
  const { config, createScenario, setActiveScenario } = useDebugStore();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<CreateScenarioFormData>({
    resolver: zodResolver(createScenarioSchema),
    defaultValues: {
      name: '',
      description: '',
      category: '',
      basedOn: '__empty__',
    },
  });

  const handleSubmit = async (data: CreateScenarioFormData) => {
    setIsSubmitting(true);

    try {
      const newScenarioId = createScenario(
        data.name,
        data.category,
        data.basedOn === "__empty__" ? undefined : data.basedOn ?? undefined
      );

      // 切换到新创建的场景
      setActiveScenario(newScenarioId);

      // 重置表单并关闭对话框
      form.reset();
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to create scenario:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.reset();
    onOpenChange(false);
  };

  // 获取可用的基础场景
  const availableScenarios = Object.values(config.scenarioLibrary.scenarios);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            创建新场景
          </DialogTitle>
          <DialogDescription>
            创建一个新的分析场景，可以从头开始或基于现有场景创建
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* 场景名称 */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>场景名称 *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="输入场景名称..."
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    给场景起一个简洁易懂的名称
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 场景描述 */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>场景描述 *</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="描述这个场景的用途和特点..."
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    详细描述场景的用途、适用场合等
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 场景分类 */}
            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>场景分类 *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择场景分类" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {SCENARIO_CATEGORIES.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          <div className="flex items-center gap-2">
                            <span>{category.icon}</span>
                            <div>
                              <div className="font-medium">{category.name}</div>
                              <div className="text-xs text-muted-foreground">
                                {category.description}
                              </div>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    选择最符合场景用途的分类
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 基础场景 */}
            {availableScenarios.length > 0 && (
              <FormField
                control={form.control}
                name="basedOn"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>基于现有场景（可选）</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择基础场景或从头开始" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="__empty__">从头开始创建</SelectItem>
                        {availableScenarios.map((scenario) => (
                          <SelectItem key={scenario.id} value={scenario.id}>
                            <div className="flex items-center gap-2">
                              <span className="text-sm">{scenario.name}</span>
                              <Badge variant="outline" className="text-xs">
                                {SCENARIO_CATEGORIES.find(cat => cat.id === scenario.metadata.category)?.name}
                              </Badge>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      可以基于现有场景的配置来创建新场景
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isSubmitting}
              >
                取消
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? '创建中...' : '创建场景'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 