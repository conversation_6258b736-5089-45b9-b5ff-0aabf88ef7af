"use client";

import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    Edit3,
    Filter,
    Layers,
    Plus,
    Search,
    SortAsc,
    Tag
} from "lucide-react";
import React, { useCallback, useMemo, useState } from "react";

import { Badge } from "~/components/ui/badge";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Separator } from "~/components/ui/separator";
import { useDebugStore } from "~/core/store/debug-store";

import { ScenarioEditor } from "../scenario-editor";
import type { ScenarioConfig } from "../types";
import { SCENARIO_CATEGORIES } from "../types";

import { CreateScenarioDialog } from "./create-scenario-dialog";
import { ScenarioCard } from "./scenario-card";

export function ScenarioLibrary() {
  const {
    config,
    ui,
    getEnabledScenarios,
    setActiveScenario,
    addToFavorites,
    removeFromFavorites,
    deleteScenario,
    setFilters,
    duplicateScenario,
    setEditorState,
    getActiveScenario,
    toggleScenarioEnabled,
  } = useDebugStore();

  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState(ui.filters.searchQuery);

  const activeScenario = getActiveScenario();
  const isEditing = ui.editor.isEditing;

  const enabledScenarios = getEnabledScenarios();
  const allScenarios = Object.values(config.scenarioLibrary.scenarios);
  
  // 🔍 调试日志
  React.useEffect(() => {
    // 检查特定的预置场景
    const hasPresetPD = allScenarios.some(s => s.id === 'preset-pd');
    const hasPresetSA = allScenarios.some(s => s.id === 'preset-sa');
    if (!hasPresetPD || !hasPresetSA) {
      console.warn('⚠️ 缺少预置场景，请清除 LocalStorage 并刷新页面');
    }
  }, [allScenarios, enabledScenarios, config.scenarioLibrary.enabledScenarios]);
  
  // 移除未使用的变量

  // 获取过滤后的场景列表
  const filteredScenarios = useMemo(() => {
    const scenarios = Object.values(config.scenarioLibrary.scenarios);

    return scenarios.filter((scenario) => {
      // 搜索过滤
      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase();
        if (
          !scenario.name.toLowerCase().includes(query) &&
          !scenario.description.toLowerCase().includes(query) &&
          !scenario.metadata.tags.some(tag => tag.toLowerCase().includes(query))
        ) {
          return false;
        }
      }

      // 分类过滤
      if (ui.filters.category && scenario.metadata.category !== ui.filters.category) {
        return false;
      }

      // 标签过滤
      if (ui.filters.tags.length > 0) {
        if (!ui.filters.tags.some(tag => scenario.metadata.tags.includes(tag))) {
          return false;
        }
      }

      return true;
    });
  }, [config.scenarioLibrary.scenarios, searchQuery, ui.filters]);

  // 排序后的场景列表
  const sortedScenarios = useMemo(() => {
    const sorted = [...filteredScenarios];

    switch (ui.filters.sortBy) {
      case 'name':
        sorted.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'created':
        sorted.sort((a, b) => new Date(b.metadata.createdAt).getTime() - new Date(a.metadata.createdAt).getTime());
        break;
      case 'used':
        // 按最近使用排序
        sorted.sort((a, b) => {
          const aIndex = config.scenarioLibrary.recentUsed.indexOf(a.id);
          const bIndex = config.scenarioLibrary.recentUsed.indexOf(b.id);
          if (aIndex === -1 && bIndex === -1) return 0;
          if (aIndex === -1) return 1;
          if (bIndex === -1) return -1;
          return aIndex - bIndex;
        });
        break;
      default:
        break;
    }

    if (ui.filters.sortOrder === 'desc') {
      sorted.reverse();
    }

    return sorted;
  }, [filteredScenarios, ui.filters.sortBy, ui.filters.sortOrder, config.scenarioLibrary.recentUsed]);

  // 场景操作处理 - 使用useCallback优化性能
  const handleScenarioSelect = useCallback((scenario: ScenarioConfig) => {
    setActiveScenario(scenario.id);
  }, [setActiveScenario]);

  const handleScenarioEdit = useCallback((scenario: ScenarioConfig) => {
    if (scenario.isPreset) {
      alert('预置场景为系统内置场景，不支持修改。\n\n如需自定义配置，请复制此场景后进行编辑。');
      return;
    }
    
    setActiveScenario(scenario.id);
    setEditorState({
      isEditing: true,
      currentScenarioId: scenario.id,
      hasUnsavedChanges: false,
    });
  }, [setActiveScenario, setEditorState]);

  const handleExitEdit = useCallback(() => {
    setEditorState({
      isEditing: false,
      currentScenarioId: null,
      hasUnsavedChanges: false,
    });
  }, [setEditorState]);

  const handleScenarioToggleEnabled = useCallback((scenario: ScenarioConfig) => {
    toggleScenarioEnabled(scenario.id);
  }, [toggleScenarioEnabled]);

  const handleScenarioFavorite = useCallback((scenario: ScenarioConfig) => {
    const favorites = config.scenarioLibrary.favorites || [];
    const isFavorite = favorites.includes(scenario.id);
    if (isFavorite) {
      removeFromFavorites('scenario', scenario.id);
    } else {
      addToFavorites('scenario', scenario.id);
    }
  }, [config.scenarioLibrary.favorites, removeFromFavorites, addToFavorites]);

  const handleScenarioDuplicate = useCallback((scenario: ScenarioConfig) => {
    const newName = `${scenario.name} (副本)`;
    const newId = duplicateScenario(scenario.id, newName);
    setActiveScenario(newId);
  }, [duplicateScenario, setActiveScenario]);

  const handleScenarioDelete = useCallback((scenario: ScenarioConfig) => {
    if (scenario.isPreset) {
      alert('预置场景不能删除');
      return;
    }

    if (confirm(`确定要删除场景"${scenario.name}"吗？\n\n此操作不可撤销，场景的所有配置都将永久丢失。`)) {
      deleteScenario(scenario.id);
    }
  }, [deleteScenario]);

  const handleSearchChange = useCallback((value: string) => {
    setSearchQuery(value);
    setFilters({ searchQuery: value });
  }, [setFilters]);

  // 如果正在编辑，显示编辑器界面
  if (isEditing && activeScenario) {
    return (
      <div className="h-full flex flex-col">
        {/* 编辑器头部 */}
        <div className="p-4 border-b flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleExitEdit}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            返回场景库
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <div className="flex items-center gap-2">
            <Edit3 className="h-4 w-4 text-primary" />
            <span className="font-medium">编辑场景：{activeScenario.name}</span>
            {activeScenario.isPreset && (
              <Badge variant="secondary" className="text-xs">预置</Badge>
            )}
          </div>
        </div>

        {/* 嵌入场景编辑器 */}
        <div className="flex-1 overflow-hidden">
          <ScenarioEditor />
        </div>
      </div>
    );
  }

  // 否则显示场景库界面
  return (
    <div className="h-full flex flex-col">
        {/* 头部操作区 */}
        <div className="p-4 border-b space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary/10">
                <Layers className="h-4 w-4 text-primary" />
              </div>
              <div>
                <h2 className="text-lg font-semibold">场景库</h2>
                <p className="text-sm text-muted-foreground">
                  管理和切换不同的分析场景，总共 {Object.keys(config.scenarioLibrary.scenarios).length} 个场景
                </p>
              </div>
            </div>
            <Button onClick={() => setCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              新建场景
            </Button>
          </div>

          {/* 搜索和过滤 */}
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索场景名称、描述或标签..."
                value={searchQuery}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select
              value={ui.filters.category ?? 'all'}
              onValueChange={(value) => setFilters({ category: value === 'all' ? null : value })}
            >
              <SelectTrigger className="w-48">
                <SelectValue placeholder="选择分类" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4" />
                    所有分类
                  </div>
                </SelectItem>
                {SCENARIO_CATEGORIES.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    <div className="flex items-center gap-2">
                      <span>{category.icon}</span>
                      {category.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={ui.filters.sortBy}
              onValueChange={(value) => setFilters({ sortBy: value as 'name' | 'created' | 'used' })}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="排序" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">
                  <div className="flex items-center gap-2">
                    <SortAsc className="h-4 w-4" />
                    名称
                  </div>
                </SelectItem>
                <SelectItem value="created">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    创建时间
                  </div>
                </SelectItem>
                <SelectItem value="used">
                  <div className="flex items-center gap-2">
                    <Tag className="h-4 w-4" />
                    使用频率
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 场景列表 */}
        <div className="flex-1 overflow-auto p-4">
          {sortedScenarios.length === 0 ? (
            <div className="text-center py-8">
              <Layers className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-muted-foreground mb-2">
                {searchQuery.trim() ? '未找到匹配的场景' : '暂无场景'}
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                {searchQuery.trim()
                  ? '请尝试修改搜索条件或清空搜索框查看所有场景'
                  : '开始创建您的第一个分析场景'
                }
              </p>
              {!searchQuery.trim() && (
                <Button onClick={() => setCreateDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  创建第一个场景
                </Button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {sortedScenarios.map((scenario) => (
                <ScenarioCard
                  key={scenario.id}
                  scenario={scenario}
                  isActive={activeScenario?.id === scenario.id}
                  isEnabled={(config.scenarioLibrary.enabledScenarios || []).includes(scenario.id)}
                  isFavorite={(config.scenarioLibrary.favorites || []).includes(scenario.id)}
                  onSelect={() => handleScenarioSelect(scenario)}
                  onToggleEnabled={() => handleScenarioToggleEnabled(scenario)}
                  onEdit={() => handleScenarioEdit(scenario)}
                  onFavorite={() => handleScenarioFavorite(scenario)}
                  onDuplicate={() => handleScenarioDuplicate(scenario)}
                  onDelete={() => handleScenarioDelete(scenario)}
                />
              ))}
            </div>
          )}
        </div>

        {/* 创建场景对话框 */}
        <CreateScenarioDialog
          open={createDialogOpen}
          onOpenChange={setCreateDialogOpen}
        />
      </div>
  );
} 