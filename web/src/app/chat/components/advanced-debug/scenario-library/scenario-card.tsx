"use client";

import {
  <PERSON>,
  Co<PERSON>,
  Edit,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Star,
  <PERSON>Off,
  Tag,
  Trash2,
} from "lucide-react";
import { memo, useCallback, useState } from "react";

import { Tooltip } from "~/components/deer-flow/tooltip";
import { Badge } from "~/components/ui/badge";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { cn } from "~/lib/utils";

import type { ScenarioConfig } from "../types";
import { SCENARIO_CATEGORIES } from "../types";

interface ScenarioCardProps {
  scenario: ScenarioConfig;
  isActive: boolean;
  isEnabled: boolean;
  isFavorite: boolean;
  onSelect: () => void;
  onToggleEnabled: () => void;
  onEdit: () => void;
  onFavorite: () => void;
  onDuplicate: () => void;
  onDelete: () => void;
}

export const ScenarioCard = memo(function ScenarioCard({
  scenario,
  isActive,
  isEnabled,
  isFavorite,
  onSelect,
  onToggleEnabled,
  onEdit,
  onFavorite,
  onDuplicate,
  onDelete,
}: ScenarioCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  // 优化事件处理器
  const handleMouseEnter = useCallback(() => setIsHovered(true), []);
  const handleMouseLeave = useCallback(() => setIsHovered(false), []);
  const handleToggleEnabled = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleEnabled();
  }, [onToggleEnabled]);
  const handleEdit = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit();
  }, [onEdit]);
  const handleDelete = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete();
  }, [onDelete]);

  // 获取分类信息
  const categoryInfo = SCENARIO_CATEGORIES.find(cat => cat.id === scenario.metadata.category);

  // 难度颜色映射
  const difficultyColors = {
    simple: 'bg-green-100 text-green-800 border-green-200',
    medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    complex: 'bg-red-100 text-red-800 border-red-200',
  };

  // 难度文本映射
  const difficultyLabels = {
    simple: '简单',
    medium: '中等',
    complex: '复杂',
  };

  return (
    <Card
      className={cn(
        "relative transition-all duration-300 cursor-pointer group border-border/50",
        "hover:shadow-lg hover:border-border hover:-translate-y-0.5",
        isActive && "ring-2 ring-primary ring-offset-2 shadow-md",
        isHovered && "shadow-lg border-border"
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={onSelect}
    >
      {/* 收藏标记 */}
      {isFavorite && (
        <div className="absolute top-2 right-2 z-10">
          <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
        </div>
      )}

      {/* 激活状态指示器 */}
      {isActive && (
        <div className="absolute top-0 left-0 w-full h-1 bg-primary rounded-t-lg" />
      )}

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-base font-semibold flex items-center gap-2 mb-1">
              {categoryInfo && (
                <span className="text-lg">{categoryInfo.icon}</span>
              )}
              <span className="truncate">{scenario.name}</span>
              {scenario.isPreset && (
                <Badge variant="secondary" className="text-xs">预置</Badge>
              )}
            </CardTitle>
            <CardDescription className="text-sm line-clamp-2">
              {scenario.description}
            </CardDescription>
          </div>

          {/* 操作菜单 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 opacity-60 group-hover:opacity-100 hover:opacity-100 transition-opacity"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem 
                onClick={handleEdit}
                disabled={scenario.isPreset}
              >
                <Edit className="h-4 w-4 mr-2" />
                {scenario.isPreset ? '查看场景' : '编辑场景'}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onFavorite(); }}>
                {isFavorite ? (
                  <>
                    <StarOff className="h-4 w-4 mr-2" />
                    取消收藏
                  </>
                ) : (
                  <>
                    <Star className="h-4 w-4 mr-2" />
                    添加收藏
                  </>
                )}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onDuplicate(); }}>
                <Copy className="h-4 w-4 mr-2" />
                复制场景
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-destructive"
                onClick={handleDelete}
                disabled={scenario.isPreset}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                删除场景
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="pt-0 space-y-3">
        {/* 场景标签 */}
        {scenario.metadata.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {scenario.metadata.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {scenario.metadata.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{scenario.metadata.tags.length - 3}
              </Badge>
            )}
          </div>
        )}

        {/* 场景属性 */}
        <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>{scenario.metadata.estimatedTime}分钟</span>
          </div>
          <div className="flex items-center gap-1">
            <Tag className="h-3 w-3" />
            <Badge
              variant="outline"
              className={cn("text-xs", difficultyColors[scenario.metadata.difficulty])}
            >
              {difficultyLabels[scenario.metadata.difficulty]}
            </Badge>
          </div>
        </div>

        {/* 节点状态 */}
        <div className="grid grid-cols-2 gap-1 text-xs">
          <div className="flex items-center gap-1">
            <span className="text-muted-foreground">Planner</span>
          </div>
          <div className="flex items-center gap-1">
            <span className="text-muted-foreground">Researcher</span>
          </div>
          <div className="flex items-center gap-1">
            <span className="text-muted-foreground">Reporter</span>
          </div>
          <div className="flex items-center gap-1">
            <span className="text-muted-foreground">Coder</span>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex gap-2 pt-2">
          <Button
            size="sm"
            variant={isEnabled ? "default" : "outline"}
            className="flex-1"
            onClick={handleToggleEnabled}
          >
            {isEnabled ? (
              <>
                <Play className="h-3 w-3 mr-1 fill-current" />
                已启用
              </>
            ) : (
              <>
                <Play className="h-3 w-3 mr-1" />
                启用
              </>
            )}
          </Button>
          <Tooltip title={scenario.isPreset ? "预置场景不可编辑" : (isActive ? "当前编辑场景" : "编辑场景")}>
            <Button
              size="sm"
              variant={isActive ? "default" : "outline"}
              onClick={handleEdit}
              disabled={scenario.isPreset}
            >
              <Edit className="h-3 w-3" />
            </Button>
          </Tooltip>
          {!scenario.isPreset && (
            <Tooltip title="删除场景">
              <Button
                size="sm"
                variant="outline"
                className="text-destructive hover:text-destructive hover:bg-destructive/10"
                onClick={handleDelete}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </Tooltip>
          )}
        </div>

        {/* 更新时间 */}
        <div className="text-xs text-muted-foreground border-t pt-2">
          更新于 {new Date(scenario.metadata.updatedAt).toLocaleDateString()}
        </div>
      </CardContent>
    </Card>
  );
}); 