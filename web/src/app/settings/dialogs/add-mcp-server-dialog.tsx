// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { Loader2 } from "lucide-react";
import { useCallback, useEffect, useState } from "react";

import { But<PERSON> } from "~/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "~/components/ui/dialog";
import { Textarea } from "~/components/ui/textarea";
import { queryMCPServerMetadata } from "~/core/api";
import {
    MCPConfigSchema,
    type MCPServerMetadata,
    type SimpleMCPServerMetadata,
    type SimpleSSEMCPServerMetadata,
    type SimpleStdioMCPServerMetadata,
} from "~/core/mcp";

interface MCPServerDialogProps {
  mode?: 'add' | 'edit';
  serverToEdit?: MCPServerMetadata;
  trigger?: React.ReactNode;
  onAdd?: (servers: MCPServerMetadata[]) => void;
  onEdit?: (server: MCPServerMetadata) => void;
}

export function MCPServerDialog({
  mode = 'add',
  serverToEdit,
  trigger,
  onAdd,
  onEdit,
}: MCPServerDialogProps) {
  const [open, setOpen] = useState(false);
  const [input, setInput] = useState("");
  const [validationError, setValidationError] = useState<string | null>("");
  const [error, setError] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);

  // 当打开编辑模式时，填充现有服务器配置
  useEffect(() => {
    if (mode === 'edit' && serverToEdit && open) {
      const config = serverToEditToConfig(serverToEdit);
      setInput(JSON.stringify(config, null, 2));
    } else if (mode === 'add' && open) {
      setInput("");
    }
  }, [mode, serverToEdit, open]);

  const handleChange = useCallback((value: string) => {
    setInput(value);
    if (!value.trim()) {
      setValidationError(null);
      return;
    }
    setValidationError(null);
    try {
      const parsed = JSON.parse(value);
      if (!("mcpServers" in parsed)) {
        setValidationError("Missing `mcpServers` in JSON");
        return;
      }
    } catch {
      setValidationError("Invalid JSON");
      return;
    }
    const result = MCPConfigSchema.safeParse(JSON.parse(value));
    if (!result.success) {
      if (result.error.errors[0]) {
        const error = result.error.errors[0];
        if (error.code === "invalid_union") {
          if (error.unionErrors[0]?.errors[0]) {
            setValidationError(error.unionErrors[0].errors[0].message);
            return;
          }
        }
      }
      const errorMessage =
        result.error.errors[0]?.message ?? "Validation failed";
      setValidationError(errorMessage);
      return;
    }

    const keys = Object.keys(result.data.mcpServers);
    if (keys.length === 0) {
      setValidationError("Missing server name in `mcpServers`");
      return;
    }

    // 编辑模式下检查服务器名称
    if (mode === 'edit' && serverToEdit) {
      if (keys.length > 1) {
        setValidationError("Edit mode only supports single server configuration");
        return;
      }
      // 允许修改服务器名称，但会在保存时处理
    }
  }, [mode, serverToEdit]);

  const handleSubmit = useCallback(async () => {
    const config = MCPConfigSchema.parse(JSON.parse(input));
    setInput(JSON.stringify(config, null, 2));
    
    if (mode === 'edit' && serverToEdit) {
      // 编辑模式
      const serverEntries = Object.entries(config.mcpServers);
      if (serverEntries.length !== 1) {
        setError("Edit mode only supports single server configuration");
        return;
      }

      const [newServerName, serverConfig] = serverEntries[0]!;
      
      setProcessing(true);
      try {
        setError(null);
        
        let simpleMetadata: SimpleMCPServerMetadata;
        if ("command" in serverConfig) {
          simpleMetadata = {
            transport: "stdio",
            name: newServerName,
            command: serverConfig.command,
            args: serverConfig.args,
            env: serverConfig.env,
            enabled_tools: serverConfig.enabled_tools,
            add_to_agents: serverConfig.add_to_agents,
          };
        } else if ("url" in serverConfig) {
          simpleMetadata = {
            transport: "sse",
            name: newServerName,
            url: serverConfig.url,
            enabled_tools: serverConfig.enabled_tools,
            add_to_agents: serverConfig.add_to_agents,
          };
        } else {
          throw new Error("Invalid server configuration");
        }

        const metadata = await queryMCPServerMetadata(simpleMetadata);
        const updatedServer: MCPServerMetadata = {
          ...metadata,
          name: newServerName,
          enabled: serverToEdit.enabled,
          enabled_tools: serverConfig.enabled_tools,
          add_to_agents: serverConfig.add_to_agents,
          createdAt: serverToEdit.createdAt,
          updatedAt: Date.now(),
        };

        onEdit?.(updatedServer);
        setInput("");
        setOpen(false);
      } catch (e) {
        console.error(e);
        setError(`Failed to update server: ${e instanceof Error ? e.message : 'Unknown error'}`);
      } finally {
        setProcessing(false);
      }
    } else {
      // 新增模式 - 保持原有逻辑
      const addingServers: SimpleMCPServerMetadata[] = [];
      for (const [key, server] of Object.entries(config.mcpServers)) {
        if ("command" in server) {
          const metadata: SimpleStdioMCPServerMetadata = {
            transport: "stdio",
            name: key,
            command: server.command,
            args: server.args,
            env: server.env,
            enabled_tools: server.enabled_tools,
            add_to_agents: server.add_to_agents,
          };
          addingServers.push(metadata);
        } else if ("url" in server) {
          const metadata: SimpleSSEMCPServerMetadata = {
            transport: "sse",
            name: key,
            url: server.url,
            enabled_tools: server.enabled_tools,
            add_to_agents: server.add_to_agents,
          };
          addingServers.push(metadata);
        }
      }
      setProcessing(true);

      const results: MCPServerMetadata[] = [];
      let processingServer: string | null = null;
      try {
        setError(null);
        for (const server of addingServers) {
          processingServer = server.name;
          const metadata = await queryMCPServerMetadata(server);
          results.push({ 
            ...metadata, 
            name: server.name, 
            enabled: true,
            enabled_tools: server.enabled_tools,
            add_to_agents: server.add_to_agents
          });
        }
        if (results.length > 0) {
          onAdd?.(results);
        }
        setInput("");
        setOpen(false);
      } catch (e) {
        console.error(e);
        setError(`Failed to add server: ${processingServer}`);
      } finally {
        setProcessing(false);
      }
    }
  }, [input, mode, serverToEdit, onAdd, onEdit]);

  const defaultTrigger = mode === 'add' ? (
    <Button size="sm">Add Servers</Button>
  ) : null;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger ?? defaultTrigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[700px] max-h-[85vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>
            {mode === 'edit' ? 'Edit MCP Server' : 'Add New MCP Servers'}
          </DialogTitle>
          <DialogDescription className="text-sm leading-relaxed">
            {mode === 'edit' 
              ? 'Modify the configuration for this MCP server. Update the config below and click "Save" to apply changes.'
              : 'DeerFlow uses the standard JSON MCP config to create a new server. Paste your config below and click "Add" to add new servers.'
            }
          </DialogDescription>
        </DialogHeader>

        <main className="flex-1 min-h-0 overflow-hidden">
          <Textarea
            className="h-full min-h-[400px] resize-none font-mono text-sm"
            placeholder={
              mode === 'edit' 
                ? 'Server configuration will be loaded automatically...'
                : `Example:

{
  "mcpServers": {
    "my-server": {
      "enabled_tools": "tool1,tool2,tool3",
      "add_to_agents": "researcher",
      "url": "http://localhost:8080/mcp/sse"
    }
  }
}`
            }
            value={input}
            onChange={(e) => handleChange(e.target.value)}
          />
        </main>

        <DialogFooter className="flex-shrink-0">
          <div className="flex flex-col w-full gap-3">
            {(validationError ?? error) && (
              <div className="text-destructive text-sm leading-relaxed break-words bg-destructive/5 border border-destructive/20 rounded-md p-3">
                {validationError ?? error}
              </div>
            )}
            <div className="flex items-center justify-between gap-2">
              <div className="text-xs text-muted-foreground">
                {mode === 'edit' ? 'Modify server configuration' : 'Add new MCP servers'}
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" onClick={() => setOpen(false)}>
                  Cancel
                </Button>
                <Button
                  className="w-24"
                  type="submit"
                  disabled={!input.trim() || !!validationError || processing}
                  onClick={handleSubmit}
                >
                  {processing && <Loader2 className="animate-spin" />}
                  {mode === 'edit' ? 'Save' : 'Add'}
                </Button>
              </div>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// 为了向后兼容，保留原有的AddMCPServerDialog组件
export function AddMCPServerDialog({
  onAdd,
}: {
  onAdd?: (servers: MCPServerMetadata[]) => void;
}) {
  return <MCPServerDialog mode="add" onAdd={onAdd} />;
}

// 将MCPServerMetadata转换为配置格式的辅助函数
function serverToEditToConfig(server: MCPServerMetadata) {
  const serverConfig: Record<string, unknown> = {
    enabled_tools: server.enabled_tools,
    add_to_agents: server.add_to_agents,
  };

  if (server.transport === "stdio") {
    serverConfig.command = server.command;
    if (server.args) {
      serverConfig.args = server.args;
    }
    if (server.env) {
      serverConfig.env = server.env;
    }
  } else if (server.transport === "sse") {
    serverConfig.url = server.url;
  }

  return {
    mcpServers: {
      [server.name]: serverConfig
    }
  };
}
