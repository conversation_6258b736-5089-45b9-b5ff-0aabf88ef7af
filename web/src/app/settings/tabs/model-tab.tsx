    // Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
    // SPDX-License-Identifier: MIT

    import { zodResolver } from "@hookform/resolvers/zod";
    import { Bot, TestTube, AlertCircle, CheckCircle } from "lucide-react";
    import { useCallback, useEffect, useMemo, useState } from "react";
    import { useForm } from "react-hook-form";
    import { z } from "zod";

    import { Alert, AlertDescription } from "~/components/ui/alert";
    import { Badge } from "~/components/ui/badge";
    import { Button } from "~/components/ui/button";
    import {
        Form,
        FormControl,
        FormDescription,
        FormField,
        FormItem,
        FormLabel,
        FormMessage,
    } from "~/components/ui/form";
    import { Input } from "~/components/ui/input";
    import {
        Select,
        SelectContent,
        SelectItem,
        SelectTrigger,
        SelectValue,
    } from "~/components/ui/select";
    import { testConnection, type TestConnectionRequest } from "~/core/api/settings";
    import type { SettingsData } from "~/core/store/settings-store";

    import type { Tab } from "./types";

    const modelFormSchema = z.object({
        apiKey: z.string().min(1, "API Key是必填项，请输入有效的密钥"),
        baseUrl: z.string().optional(),
        model: z.string().optional(),
    });

    // 只保留常用和热门的模型
    const COMMON_MODEL_OPTIONS = [
        "gpt-4.1",
        "gpt-4.1-mini",
        "gpt-4.1-nano",
        "gpt-4-omni",
        "gpt-4o-mini",
        "gemini-2.5-pro",
        "gemini-2.5-flash",
        "claude-4-opus",
        "claude-4-sonnet",
        "claude-3.7-sonnet",
        "claude-3.5-sonnet",
        "Qwen/Qwen3-235B-A22B",
        "Qwen/Qwen3-32B",
        "DeepSeek-R1",
        "DeepSeek-V3",
        "glm-4-air",
        "glm-z1-air",
        "glm-z1-airx",
        "doubao-pro-256k",
        "doubao-pro",
        "doubao-lite-32k",
        "moonshot-v1-128k",
    ];

    export const ModelTab: Tab = ({
        settings,
        onChange,
    }: {
        settings: SettingsData;
        onChange: (changes: Partial<SettingsData>) => void;
    }) => {
        const generalSettings = useMemo(() => settings.general, [settings]);
        const [isCustomModel, setIsCustomModel] = useState(false);
        const [customModel, setCustomModel] = useState("");
        const [isTestingConnection, setIsTestingConnection] = useState(false);
        const [testResult, setTestResult] = useState<{ success: boolean; message: string; error?: string } | null>(null);

        const form = useForm<z.infer<typeof modelFormSchema>>({
            resolver: zodResolver(modelFormSchema, undefined, undefined),
            defaultValues: {
                apiKey: generalSettings.apiKey,
                baseUrl: generalSettings.baseUrl,
                model: generalSettings.model,
            },
            mode: "all",
            reValidateMode: "onBlur",
        });

        const currentSettings = form.watch();

        // 检查当前model是否在预设选项中
        useEffect(() => {
            const currentModel = currentSettings.model;
            if (currentModel && !COMMON_MODEL_OPTIONS.includes(currentModel)) {
                setIsCustomModel(true);
                setCustomModel(currentModel);
            } else {
                setIsCustomModel(false);
                setCustomModel("");
            }
        }, [currentSettings.model]);

        useEffect(() => {
            let hasChanges = false;
            for (const key in currentSettings) {
                if (
                    currentSettings[key as keyof typeof currentSettings] !==
                    generalSettings[key as keyof typeof generalSettings]
                ) {
                    hasChanges = true;
                    break;
                }
            }
            if (hasChanges) {
                onChange({
                    general: {
                        ...generalSettings,
                        ...currentSettings
                    }
                });
            }
        }, [currentSettings, onChange, generalSettings]);

        const handleTestConnection = useCallback(async () => {
            try {
                setIsTestingConnection(true);
                setTestResult(null);

                const request: TestConnectionRequest = {
                    api_key: currentSettings.apiKey ?? "",
                    base_url: currentSettings.baseUrl ?? "",
                    model: currentSettings.model ?? "",
                };

                const result = await testConnection(request);
                setTestResult(result);
            } catch (error) {
                setTestResult({
                    success: false,
                    message: "测试连接失败",
                    error: error instanceof Error ? error.message : "未知错误",
                });
            } finally {
                setIsTestingConnection(false);
            }
        }, [currentSettings]);

        const handleModelChange = useCallback((value: string) => {
            if (value === "custom") {
                setIsCustomModel(true);
                setCustomModel("");
                form.setValue("model", "");
            } else {
                setIsCustomModel(false);
                setCustomModel("");
                form.setValue("model", value);
            }
            setTestResult(null);
        }, [form]);

        const handleCustomModelChange = useCallback((value: string) => {
            setCustomModel(value);
            form.setValue("model", value);
            setTestResult(null);
        }, [form]);

        return (
            <div className="flex flex-col gap-6">
                <header>
                    <h1 className="text-xl font-semibold flex items-center gap-2">
                        <Bot className="h-5 w-5" />
                        Model Configuration
                    </h1>
                    <p className="text-muted-foreground text-sm mt-1">
                        配置AI模型连接设置，包括模型选择、API密钥和服务地址
                    </p>
                </header>

                <main>
                    <Form {...form}>
                        <form className="space-y-6">
                            {/* 模型选择 */}
                            <div className="space-y-4 p-4 border rounded-lg">
                                <h3 className="text-lg font-medium">模型选择</h3>
                                <FormField
                                    control={form.control}
                                    name="model"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>AI Model</FormLabel>
                                            <div className="space-y-3">
                                                {!isCustomModel ? (
                                                    <Select
                                                        onValueChange={handleModelChange}
                                                        defaultValue={field.value}
                                                    >
                                                        <FormControl>
                                                            <SelectTrigger className="h-11">
                                                                <SelectValue placeholder="选择AI模型" />
                                                            </SelectTrigger>
                                                        </FormControl>
                                                        <SelectContent className="max-h-[300px] overflow-y-auto">

                                                            {COMMON_MODEL_OPTIONS.map((model) => (
                                                                <SelectItem key={model} value={model} className="py-2.5">
                                                                    <div className="flex items-center justify-between w-full">
                                                                        <span className="text-sm">{model}</span>
                                                                        {(model === "gpt-4.1" || model === "gpt-4.1-mini" || model.includes("gemini") || model.includes("Qwen") || model === "DeepSeek-V3") && (
                                                                            <Badge variant="secondary" className="text-xs ml-2">热门</Badge>
                                                                        )}
                                                                        {model === ("DeepSeek-R1") && (
                                                                            <Badge variant="outline" className="text-xs ml-2">推理</Badge>
                                                                        )}
                                                                    </div>
                                                                </SelectItem>
                                                            ))}
                                                            <SelectItem value="custom" className="py-2.5">
                                                                <div className="flex items-center gap-2">
                                                                    <span>自定义模型</span>
                                                                    <Badge variant="outline" className="text-xs">手动输入</Badge>
                                                                </div>
                                                            </SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                ) : (
                                                    <div className="space-y-3">
                                                        <div className="flex items-center gap-3">
                                                            <Input
                                                                placeholder="输入完整的模型名称，如 gpt-4 或 claude-3-sonnet"
                                                                value={customModel}
                                                                onChange={(e) => handleCustomModelChange(e.target.value)}
                                                                className="h-11"
                                                            />
                                                            <Button
                                                                type="button"
                                                                variant="outline"
                                                                onClick={() => handleModelChange("")}
                                                                className="shrink-0"
                                                            >
                                                                选择预设
                                                            </Button>
                                                        </div>
                                                        <FormDescription>
                                                            请输入完整的模型名称，支持OpenAI、Claude、Gemini等兼容接口
                                                        </FormDescription>
                                                    </div>
                                                )}
                                            </div>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            {/* API配置 */}
                            <div className="space-y-4 p-4 border rounded-lg">
                                <h3 className="text-lg font-medium">API配置</h3>
                                <FormField
                                    control={form.control}
                                    name="apiKey"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>API Key <span className="text-red-500">*</span></FormLabel>
                                            <FormControl>
                                                <Input
                                                    placeholder="请输入您的API密钥"
                                                    type="password"
                                                    className="h-11"
                                                    {...field}
                                                    onChange={(e) => {
                                                        field.onChange(e);
                                                        setTestResult(null);
                                                    }}
                                                />
                                            </FormControl>
                                            <FormDescription>
                                                API Key是使用AI服务的必需凭证，我们会安全地保存在本地
                                            </FormDescription>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="baseUrl"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Base URL</FormLabel>
                                            <FormControl>
                                                <Input
                                                    placeholder="请输入API服务地址，如 https://api.openai.com/v1"
                                                    className="h-11"
                                                    {...field}
                                                    onChange={(e) => {
                                                        field.onChange(e);
                                                        setTestResult(null);
                                                    }}
                                                />
                                            </FormControl>
                                            <FormDescription>
                                                API服务的基础URL地址，留空将使用默认地址
                                            </FormDescription>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <div className="flex items-center gap-3">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={handleTestConnection}
                                        disabled={isTestingConnection || !currentSettings.apiKey || !currentSettings.baseUrl || !currentSettings.model}
                                        className="flex items-center gap-2 h-11"
                                    >
                                        <TestTube className="h-4 w-4" />
                                        {isTestingConnection ? "测试中..." : "测试连接"}
                                    </Button>
                                    <div className="text-sm text-muted-foreground">
                                        验证当前配置是否能正常连接到AI服务
                                    </div>
                                </div>

                                {testResult && (
                                    <Alert className={`border-2 ${testResult.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}`}>
                                        <div className="flex items-start gap-3">
                                            {testResult.success ? (
                                                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                                            ) : (
                                                <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
                                            )}
                                            <div className="flex-1">
                                                <AlertDescription className={testResult.success ? "text-green-800" : "text-red-800"}>
                                                    <div className="font-medium text-base">{testResult.message}</div>
                                                    {testResult.error && (
                                                        <div className="text-sm mt-2 opacity-90">{testResult.error}</div>
                                                    )}
                                                </AlertDescription>
                                            </div>
                                        </div>
                                    </Alert>
                                )}
                            </div>
                        </form>
                    </Form>
                </main>
            </div>
        );
    };

    ModelTab.displayName = "Model";
    ModelTab.icon = Bot;