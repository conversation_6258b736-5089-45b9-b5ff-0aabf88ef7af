// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { Wrench, Code, FileText, Search, Link, Database, Zap } from "lucide-react";
import { ChevronDown, ChevronRight } from "lucide-react";
import { useState } from "react";

import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "~/components/ui/collapsible";
import { ScrollArea } from "~/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";

import type { Tab } from "./types";

// 工具数据定义
const TOOLS_DATA = {
  docchain: [
    {
      name: "search_code_by_docchain",
      displayName: "DocChain代码搜索",
      icon: <Code className="h-4 w-4" />,
      description: "使用DocChain搜索代码相关信息的工具。适用于搜索代码文件、Java类、方法实现、配置文件、API接口等代码元素。",
      category: "代码搜索",
      parameters: [
        {
          name: "query",
          type: "str",
          required: true,
          description: "搜索查询内容"
        },
        {
          name: "topic_id",
          type: "str",
          required: false,
          default: "项目默认的TopicId",
          description: "需要搜索的topic_id"
        },
        {
          name: "size",
          type: "int",
          required: false,
          default: "5",
          description: "返回结果数量"
        }
      ],
      returnType: "str",
      returnDescription: "格式化的搜索结果，包含文档ID、章节信息、内容等",
      example: {
        title: "搜索用户认证相关代码",
        code: 'search_code_by_docchain(\n  query="用户登录验证",\n  size=10\n)',
        description: "搜索包含用户登录验证相关的代码片段"
      }
    },
    {
      name: "search_doc_by_docchain",
      displayName: "DocChain文档搜索",
      icon: <FileText className="h-4 w-4" />,
      description: "使用DocChain搜索文档相关信息的工具。适用于搜索技术文档、业务规范、操作手册、API文档等文档内容。",
      category: "文档搜索",
      parameters: [
        {
          name: "query",
          type: "str",
          required: true,
          description: "搜索查询内容"
        },
        {
          name: "topic_id",
          type: "str",
          required: false,
          default: "项目默认的TopicId",
          description: "需要搜索的topic_id"
        },
        {
          name: "size",
          type: "int",
          required: false,
          default: "5",
          description: "返回结果数量"
        }
      ],
      returnType: "str",
      returnDescription: "格式化的搜索结果，包含文档ID、章节信息、内容等",
      example: {
        title: "搜索API文档",
        code: 'search_doc_by_docchain(\n  query="用户管理API接口",\n  size=8\n)',
        description: "搜索用户管理相关的API文档和接口说明"
      }
    },
    {
      name: "file_read_by_docchain",
      displayName: "DocChain文件读取",
      icon: <Database className="h-4 w-4" />,
      description: "读取DocChain中指定文档的完整内容，支持分页读取和批量读取多个文件。必须先使用搜索工具获取doc_id。",
      category: "文件操作",
      parameters: [
        {
          name: "doc_ids",
          type: "List[str]",
          required: true,
          description: "DocChain文档ID列表，从搜索结果中获取"
        },
        {
          name: "page",
          type: "int",
          required: false,
          default: "1",
          description: "页码"
        },
        {
          name: "size",
          type: "int",
          required: false,
          default: "80000",
          description: "每页大小"
        }
      ],
      returnType: "str",
      returnDescription: "完整的文件内容，包含文档信息和格式化内容",
      example: {
        title: "读取具体代码文件",
        code: 'file_read_by_docchain(\n  doc_ids=["12345", "67890"]\n)',
        description: "根据搜索结果中的文档ID读取完整的文件内容"
      }
    },
    {
      name: "search_file_by_name",
      displayName: "按文件名搜索",
      icon: <Search className="h-4 w-4" />,
      description: "根据文件名搜索DocChain中的文档，返回文档ID和文档名称列表。可以帮助快速定位具体文件。",
      category: "文件搜索",
      parameters: [
        {
          name: "keyword",
          type: "str",
          required: true,
          description: "文件名关键词，如'TimeUtil'、'UserController.java'"
        },
        {
          name: "topic_id",
          type: "str",
          required: false,
          default: "项目默认的TopicId",
          description: "需要搜索的topic_id"
        },
        {
          name: "page",
          type: "int",
          required: false,
          default: "1",
          description: "页码"
        },
        {
          name: "size",
          type: "int",
          required: false,
          default: "10",
          description: "每页大小"
        }
      ],
      returnType: "str",
      returnDescription: "包含文档ID和文件名的列表",
      example: {
        title: "按文件名搜索Java类",
        code: 'search_file_by_name(\n  keyword="UserController.java"\n)',
        description: "搜索名称包含'UserController.java'的文件"
      }
    },
    {
      name: "search_by_docchain_llm",
      displayName: "DocChain智能搜索",
      icon: <Zap className="h-4 w-4" />,
      description: "使用DocChain大模型对话接口进行智能搜索和问答。支持自然语言问答，可以直接提问获取精准答案。",
      category: "智能搜索",
      parameters: [
        {
          name: "query",
          type: "str",
          required: true,
          description: "要询问DocChain大模型的问题或查询内容"
        },
        {
          name: "topic_ids",
          type: "str",
          required: false,
          default: "项目默认的TopicId",
          description: "需要搜索的topic_ids，用逗号分隔"
        }
      ],
      returnType: "str",
      returnDescription: "AI智能回答，包含精准的代码定位和技术信息",
      example: {
        title: "智能查询API接口",
        code: 'search_by_docchain_llm(\n  query="用户登录接口在哪个类中实现？方法名是什么？"\n)',
        description: "使用自然语言查询特定功能的实现位置"
      }
    },
    {
      name: "smart_code_analyzer",
      displayName: "智能代码分析器",
      icon: <Link className="h-4 w-4" />,
      description: "专注于代码定位和结构分析的工具，可以快速找到接口实现位置和相关代码，提供代码结构概览。",
      category: "代码分析",
      parameters: [
        {
          name: "query",
          type: "str",
          required: true,
          description: "要分析的代码问题或查询内容"
        },
        {
          name: "topic_id",
          type: "str",
          required: false,
          default: "项目默认的TopicId",
          description: "需要搜索的代码库topic_id"
        }
      ],
      returnType: "str",
      returnDescription: "完整的代码分析报告，包含文件内容、方法调用链等",
      example: {
        title: "分析API接口实现",
        code: 'smart_code_analyzer(\n  query="wfm/itf/appointment/om/appointmentQuery/v1 这个接口的实现位置和调用关系"\n)',
        description: "分析特定API接口的实现位置和相关代码结构"
      }
    }
  ],
  methodChain: [
    {
      name: "java_method_call_chain",
      displayName: "Java方法调用链分析",
      icon: <Link className="h-4 w-4" />,
      description: "分析Java代码中指定方法的调用链。可以分析指定方法被哪些其他方法调用，以及它调用了哪些其他方法，帮助理解代码的调用关系和依赖结构。",
      category: "调用链分析",
      parameters: [
        {
          name: "package_path",
          type: "str",
          required: true,
          description: "Java包路径，例如: com.ztesoft.zmq.controller"
        },
        {
          name: "class_name",
          type: "str",
          required: true,
          description: "Java类名，例如: ExpertDiagnosisAction"
        },
        {
          name: "method_name",
          type: "str",
          required: true,
          description: "Java方法名，例如: createDiagnosis"
        },
        {
          name: "level",
          type: "int",
          required: false,
          default: "4",
          description: "调用链分析层级深度，1-20之间"
        }
      ],
      returnType: "str",
      returnDescription: "详细的方法调用链分析结果，包含方法内容、调用关系、参数信息等",
      example: {
        title: "分析方法调用关系",
        code: 'java_method_call_chain(\n  package_path="com.example.service",\n  class_name="UserService",\n  method_name="login",\n  level=3\n)',
        description: "分析UserService类中login方法的调用链关系，深度为3层"
      }
    }
  ]
};

interface ToolCardProps {
  tool: typeof TOOLS_DATA.docchain[0];
}

const ToolCard = ({ tool }: ToolCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              {tool.icon}
            </div>
            <div>
              <CardTitle className="text-lg font-semibold">{tool.displayName}</CardTitle>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="secondary" className="text-xs">{tool.category}</Badge>
                <code className="text-xs bg-muted px-2 py-1 rounded">{tool.name}</code>
              </div>
            </div>
          </div>
        </div>
        <CardDescription className="mt-2 text-sm leading-relaxed">
          {tool.description}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="pt-0">
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-8">
              <span className="text-sm font-medium">查看详细信息</span>
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          
          <CollapsibleContent className="space-y-4 mt-4">
            {/* 参数信息 */}
            <div>
              <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                <span>输入参数</span>
                <Badge variant="outline" className="text-xs">{tool.parameters.length}个</Badge>
              </h4>
              <div className="space-y-2">
                {tool.parameters.map((param, index) => (
                  <div key={index} className="border rounded-lg p-3 bg-muted/30">
                    <div className="flex items-center gap-2 mb-1">
                      <code className="text-xs bg-muted px-1.5 py-0.5 rounded font-mono">
                        {param.name}
                      </code>
                      <Badge variant={param.required ? "destructive" : "secondary"} className="text-xs">
                        {param.required ? "必需" : "可选"}
                      </Badge>
                      <span className="text-xs text-muted-foreground">{param.type}</span>
                    </div>
                    <p className="text-xs text-muted-foreground mb-1">
                      {param.description}
                    </p>
                    {!param.required && param.default && (
                      <p className="text-xs text-blue-600">
                        默认值: {param.default}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* 返回值信息 */}
            <div>
              <h4 className="font-medium text-sm mb-2">返回值</h4>
              <div className="border rounded-lg p-3 bg-muted/30">
                <div className="flex items-center gap-2 mb-1">
                  <code className="text-xs bg-muted px-1.5 py-0.5 rounded font-mono">
                    {tool.returnType}
                  </code>
                </div>
                <p className="text-xs text-muted-foreground">
                  {tool.returnDescription}
                </p>
              </div>
            </div>

            {/* 使用示例 */}
            <div>
              <h4 className="font-medium text-sm mb-2">使用示例</h4>
              <div className="border rounded-lg p-3 bg-muted/30">
                <h5 className="text-xs font-medium mb-2">{tool.example.title}</h5>
                <pre className="text-xs bg-black/5 p-2 rounded border overflow-x-auto font-mono">
                  <code>{tool.example.code}</code>
                </pre>
                <p className="text-xs text-muted-foreground mt-2">
                  {tool.example.description}
                </p>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
};

export const ToolsTab: Tab = () => {
  return (
    <div className="flex flex-col gap-6 max-w-full">
      <header>
        <h1 className="text-xl font-semibold flex items-center gap-2">
          <Wrench className="h-5 w-5" />
          内置工具
        </h1>
        <p className="text-muted-foreground text-sm mt-1">
          DeerFlow内置的工具集合，包含DocChain搜索、方法调用链分析等功能
        </p>
      </header>

      <main>
        <Tabs defaultValue="docchain" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="docchain" className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              DocChain工具
              <Badge variant="secondary" className="ml-1">{TOOLS_DATA.docchain.length}</Badge>
            </TabsTrigger>
            <TabsTrigger value="methodchain" className="flex items-center gap-2">
              <Link className="h-4 w-4" />
              调用链分析
              <Badge variant="secondary" className="ml-1">{TOOLS_DATA.methodChain.length}</Badge>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="docchain" className="mt-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">DocChain搜索工具</h3>
                <Badge variant="outline">{TOOLS_DATA.docchain.length}个工具</Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                用于搜索和检索DocChain平台中的代码、文档和相关资源的工具集合
              </p>
              
              <ScrollArea className="h-[500px] w-full">
                <div className="space-y-4 pr-4">
                  {TOOLS_DATA.docchain.map((tool, index) => (
                    <ToolCard key={index} tool={tool} />
                  ))}
                </div>
              </ScrollArea>
            </div>
          </TabsContent>

          <TabsContent value="methodchain" className="mt-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">方法调用链分析工具</h3>
                <Badge variant="outline">{TOOLS_DATA.methodChain.length}个工具</Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                分析Java代码中方法调用关系和依赖结构的专业工具
              </p>
              
              <ScrollArea className="h-[500px] w-full">
                <div className="space-y-4 pr-4">
                  {TOOLS_DATA.methodChain.map((tool, index) => (
                    <ToolCard key={index} tool={tool} />
                  ))}
                </div>
              </ScrollArea>
            </div>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
};

ToolsTab.displayName = "Tools";
ToolsTab.icon = Wrench; 