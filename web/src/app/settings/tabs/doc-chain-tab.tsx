// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { zodResolver } from "@hookform/resolvers/zod";
import { Server, TestTube, AlertCircle, CheckCircle } from "lucide-react";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Alert, AlertDescription } from "~/components/ui/alert";
import { Button } from "~/components/ui/button";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import type { SettingsData } from "~/core/store/settings-store";

import type { Tab } from "./types";

const docChainFormSchema = z.object({
    baseUrl: z.string().min(1, "DocChain地址是必填项，请输入有效的服务地址"),
    apiKey: z.string().min(1, "API Key是必填项，请输入有效的密钥"),
});

export const DocChainTab: Tab = ({
    settings,
    onChange,
}: {
    settings: SettingsData;
    onChange: (changes: Partial<SettingsData>) => void;
}) => {
    const docChainSettings = useMemo(() => settings.docChain, [settings]);
    const [isTestingConnection, setIsTestingConnection] = useState(false);
    const [testResult, setTestResult] = useState<{ success: boolean; message: string; error?: string } | null>(null);

    const form = useForm<z.infer<typeof docChainFormSchema>>({
        resolver: zodResolver(docChainFormSchema, undefined, undefined),
        defaultValues: {
            baseUrl: docChainSettings.baseUrl,
            apiKey: docChainSettings.apiKey,
        },
        mode: "all",
        reValidateMode: "onBlur",
    });

    const currentSettings = form.watch();

    useEffect(() => {
        let hasChanges = false;
        for (const key in currentSettings) {
            if (
                currentSettings[key as keyof typeof currentSettings] !==
                docChainSettings[key as keyof typeof docChainSettings]
            ) {
                hasChanges = true;
                break;
            }
        }
        if (hasChanges) {
            onChange({
                docChain: {
                    ...docChainSettings,
                    ...currentSettings
                }
            });
        }
    }, [currentSettings, onChange, docChainSettings]);

    const handleTestConnection = useCallback(async () => {
        try {
            setIsTestingConnection(true);
            setTestResult(null);

            // 这里可以添加DocChain连接测试的逻辑
            // 暂时模拟测试结果
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 简单的URL格式验证
            const isValidUrl = /^https?:\/\/.+/.exec(currentSettings.baseUrl);
            if (!isValidUrl) {
                setTestResult({
                    success: false,
                    message: "连接测试失败",
                    error: "请输入有效的URL格式（需要包含http://或https://）",
                });
                return;
            }

            setTestResult({
                success: true,
                message: "DocChain连接配置验证通过！",
            });
        } catch (error) {
            setTestResult({
                success: false,
                message: "测试连接失败",
                error: error instanceof Error ? error.message : "未知错误",
            });
        } finally {
            setIsTestingConnection(false);
        }
    }, [currentSettings]);

    return (
        <div className="flex flex-col gap-6">
            <header>
                <h1 className="text-xl font-semibold flex items-center gap-2">
                    <Server className="h-5 w-5" />
                    DocChain Configuration
                </h1>
                <p className="text-muted-foreground text-sm mt-1">
                    配置DocChain服务连接设置，包括服务地址和API密钥
                </p>
            </header>

            <main>
                <Form {...form}>
                    <form className="space-y-6">
                        {/* DocChain配置 */}
                        <div className="space-y-4 p-4 border rounded-lg">
                            <h3 className="text-lg font-medium">DocChain服务配置</h3>
                            
                            <FormField
                                control={form.control}
                                name="baseUrl"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>DocChain服务地址 <span className="text-red-500">*</span></FormLabel>
                                        <FormControl>
                                            <Input
                                                placeholder="请输入DocChain服务地址，如 http://10.10.176.213:7000/llmdoc"
                                                className="h-11"
                                                {...field}
                                                onChange={(e) => {
                                                    field.onChange(e);
                                                    setTestResult(null);
                                                }}
                                            />
                                        </FormControl>
                                        <FormDescription>
                                            DocChain服务的基础URL地址，用于文档搜索和内容检索
                                        </FormDescription>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="apiKey"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>API Key <span className="text-red-500">*</span></FormLabel>
                                        <FormControl>
                                            <Input
                                                placeholder="请输入DocChain服务的API密钥"
                                                type="password"
                                                className="h-11"
                                                {...field}
                                                onChange={(e) => {
                                                    field.onChange(e);
                                                    setTestResult(null);
                                                }}
                                            />
                                        </FormControl>
                                        <FormDescription>
                                            DocChain服务的API密钥，用于身份验证和访问控制
                                        </FormDescription>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <div className="flex items-center gap-3">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleTestConnection}
                                    disabled={isTestingConnection || !currentSettings.baseUrl || !currentSettings.apiKey}
                                    className="flex items-center gap-2 h-11"
                                >
                                    <TestTube className="h-4 w-4" />
                                    {isTestingConnection ? "测试中..." : "测试连接"}
                                </Button>
                                <div className="text-sm text-muted-foreground">
                                    验证当前配置是否能正常连接到DocChain服务
                                </div>
                            </div>

                            {testResult && (
                                <Alert className={`border-2 ${testResult.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}`}>
                                    <div className="flex items-start gap-3">
                                        {testResult.success ? (
                                            <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                                        ) : (
                                            <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
                                        )}
                                        <div className="flex-1">
                                            <AlertDescription className={testResult.success ? "text-green-800" : "text-red-800"}>
                                                <div className="font-medium text-base">{testResult.message}</div>
                                                {testResult.error && (
                                                    <div className="text-sm mt-2 opacity-90">{testResult.error}</div>
                                                )}
                                            </AlertDescription>
                                        </div>
                                    </div>
                                </Alert>
                            )}
                        </div>

                        {/* 使用说明 */}
                        <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
                            <h3 className="text-lg font-medium">使用说明</h3>
                            <div className="text-sm text-muted-foreground space-y-2">
                                <p>• DocChain是一个文档搜索和检索服务，可以帮助您快速找到相关的代码和文档内容</p>
                                <p>• 配置完成后，系统会使用这些设置来访问DocChain服务进行搜索</p>
                                <p>• 请确保提供的服务地址可以正常访问，且API Key具有相应的权限</p>
                                <p>• 如果您不确定这些配置信息，请联系系统管理员</p>
                            </div>
                        </div>
                    </form>
                </Form>
            </main>
        </div>
    );
};

DocChainTab.displayName = "DocChain";
DocChainTab.icon = Server; 