// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { zodResolver } from "@hookform/resolvers/zod";
import { Settings } from "lucide-react";
import { useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Switch } from "~/components/ui/switch";
import type { SettingsData } from "~/core/store/settings-store";

import type { Tab } from "./types";

const generalFormSchema = z.object({
  autoAcceptedPlan: z.boolean(),
  enableBackgroundInvestigation: z.boolean(),
  maxPlanIterations: z.number().min(1, {
    message: "Max plan iterations must be at least 1.",
  }),
  maxStepNum: z.number().min(1, {
    message: "Max step number must be at least 1.",
  }),
  maxSearchResults: z.number().min(1, {
    message: "Max search results must be at least 1.",
  }),
});

export const GeneralTab: Tab = ({
  settings,
  onChange,
}: {
  settings: SettingsData;
  onChange: (changes: Partial<SettingsData>) => void;
}) => {
  const generalSettings = useMemo(() => settings.general, [settings]);
  
  const form = useForm<z.infer<typeof generalFormSchema>>({
    resolver: zodResolver(generalFormSchema, undefined, undefined),
    defaultValues: {
      autoAcceptedPlan: generalSettings.autoAcceptedPlan,
      enableBackgroundInvestigation: generalSettings.enableBackgroundInvestigation,
      maxPlanIterations: generalSettings.maxPlanIterations,
      maxStepNum: generalSettings.maxStepNum,
      maxSearchResults: generalSettings.maxSearchResults,
    },
    mode: "all",
    reValidateMode: "onBlur",
  });

  const currentSettings = form.watch();
  
  useEffect(() => {
    let hasChanges = false;
    for (const key in currentSettings) {
      if (
        currentSettings[key as keyof typeof currentSettings] !==
        generalSettings[key as keyof typeof generalSettings]
      ) {
        hasChanges = true;
        break;
      }
    }
    if (hasChanges) {
      onChange({ 
        general: {
          ...generalSettings,
          ...currentSettings
        }
      });
    }
  }, [currentSettings, onChange, generalSettings]);

  return (
    <div className="flex flex-col gap-6">
      <header>
        <h1 className="text-xl font-semibold">General Settings</h1>
        <p className="text-muted-foreground text-sm mt-1">
          配置研究计划的基本参数和行为设置
        </p>
      </header>
      
      <main>
        <Form {...form}>
          <form className="space-y-6">
            {/* 计划设置 */}
            <div className="space-y-4 p-4 border rounded-lg">
              <h3 className="text-lg font-medium">研究计划设置</h3>
              
              <FormField
                control={form.control}
                name="enableBackgroundInvestigation"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="space-y-1">
                          <Label className="text-sm font-medium" htmlFor="enableBackgroundInvestigation">
                            启用背景调研
                          </Label>
                          <div className="text-xs text-muted-foreground">
                            在制定研究计划前进行背景信息收集
                          </div>
                        </div>
                        <Switch
                          id="enableBackgroundInvestigation"
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="autoAcceptedPlan"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="space-y-1">
                          <Label className="text-sm font-medium" htmlFor="autoAcceptedPlan">
                            自动接受计划
                          </Label>
                          <div className="text-xs text-muted-foreground">
                            跳过计划确认，直接开始执行研究
                          </div>
                        </div>
                        <Switch
                          id="autoAcceptedPlan"
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            {/* 数量限制 */}
            <div className="space-y-4 p-4 border rounded-lg">
              <h3 className="text-lg font-medium">数量限制</h3>
              
              <FormField
                control={form.control}
                name="maxPlanIterations"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>最大计划迭代次数</FormLabel>
                    <FormControl>
                      <Input
                        className="w-32 h-10"
                        type="number"
                        defaultValue={field.value}
                        min={1}
                        onChange={(event) =>
                          field.onChange(parseInt(event.target.value || "1"))
                        }
                      />
                    </FormControl>
                    <FormDescription>
                      设置为1进行单步规划，设置为2或更多启用重新规划
                    </FormDescription>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="maxStepNum"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>研究计划最大步骤数</FormLabel>
                    <FormControl>
                      <Input
                        className="w-32 h-10"
                        type="number"
                        defaultValue={field.value}
                        min={1}
                        onChange={(event) =>
                          field.onChange(parseInt(event.target.value || "3"))
                        }
                      />
                    </FormControl>
                    <FormDescription>
                      默认情况下，每个研究计划包含3个步骤
                    </FormDescription>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="maxSearchResults"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>最大搜索结果数</FormLabel>
                    <FormControl>
                      <Input
                        className="w-32 h-10"
                        type="number"
                        defaultValue={field.value}
                        min={1}
                        onChange={(event) =>
                          field.onChange(parseInt(event.target.value || "3"))
                        }
                      />
                    </FormControl>
                    <FormDescription>
                      默认情况下，每个搜索步骤返回3个结果
                    </FormDescription>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
      </main>
    </div>
  );
};
GeneralTab.displayName = "General";
GeneralTab.icon = Settings;
