// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { Settings, type LucideIcon } from "lucide-react";

import { AboutTab } from "./about-tab";
import { DocChainTab } from "./doc-chain-tab";
import { GeneralTab } from "./general-tab";
import { MCPTab } from "./mcp-tab";
import { ModelTab } from "./model-tab";
import { ToolsTab } from "./tools-tab";
import { TopicTab } from "./topic-tab";

export const SETTINGS_TABS = [ModelTab, GeneralTab, DocChainTab, TopicTab, MCPTab, ToolsTab, AboutTab].map((tab) => {
  const name = tab.displayName ?? tab.name ?? "Unknown";
  return {
    ...tab,
    id: name.replace(/Tab$/, "").toLowerCase(),
    label: name.replace(/Tab$/, ""),
    icon: (tab.icon ?? <Settings />) as LucideIcon,
    component: tab,
  };
});
