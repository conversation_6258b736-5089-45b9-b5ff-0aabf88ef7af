// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import type { LucideIcon } from "lucide-react";
import type { FunctionComponent } from "react";

import type { SettingsData } from "~/core/store/settings-store";

export type Tab = FunctionComponent<{
  settings: SettingsData;
  onChange: (changes: Partial<SettingsData>) => void;
}> & {
  displayName?: string;
  icon?: LucideIcon;
  badge?: string;
};
