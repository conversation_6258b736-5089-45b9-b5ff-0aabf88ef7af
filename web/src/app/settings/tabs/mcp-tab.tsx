// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { Blocks, ChevronDown, ChevronRight, Edit, Info, PencilRuler, Search, Settings, Trash, X } from "lucide-react";
import { memo, startTransition, useCallback, useMemo, useRef, useState } from "react";

import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "~/components/ui/collapsible";
import { Input } from "~/components/ui/input";
import { Switch } from "~/components/ui/switch";
import type { MCPServerMetadata } from "~/core/mcp";
import { cn } from "~/lib/utils";

import { AddMCPServerDialog, MCPServerDialog } from "../dialogs/add-mcp-server-dialog";

import type { Tab } from "./types";

// 全局状态管理工具 - 避免在组件内部管理过多状态
const globalPendingUpdates = new Map<string, Set<string>>();

// 性能优化的工具参数折叠面板组件
const ToolParameters = memo(({ inputSchema }: { inputSchema?: Record<string, unknown> }) => {
  const [isParametersOpen, setIsParametersOpen] = useState(false);

  const parametersInfo = useMemo(() => {
    if (!inputSchema || typeof inputSchema !== 'object') {
      return { hasParameters: false, properties: [], totalCount: 0 };
    }

    const properties = inputSchema?.properties;
    if (!properties || typeof properties !== 'object') {
      return { hasParameters: false, properties: [], totalCount: 0 };
    }

    const paramEntries = Object.entries(properties);
    return {
      hasParameters: true,
      properties: paramEntries,
      totalCount: paramEntries.length
    };
  }, [inputSchema]);

  if (!parametersInfo.hasParameters) {
    return (
      <p className="text-xs text-muted-foreground italic mt-1">
        No parameters
      </p>
    );
  }

  return (
    <div className="mt-2">
      <Collapsible open={isParametersOpen} onOpenChange={setIsParametersOpen}>
        <CollapsibleTrigger asChild>
          <button
            type="button"
            className="flex items-center gap-1 text-xs text-muted-foreground hover:text-foreground transition-colors"
          >
            {isParametersOpen ? <ChevronDown size={12} /> : <ChevronRight size={12} />}
            <span>Parameters ({parametersInfo.totalCount})</span>
          </button>
        </CollapsibleTrigger>
        <CollapsibleContent className="mt-1">
          <div className="space-y-2 pl-3 border-l border-muted max-h-48 overflow-y-auto">
            {parametersInfo.properties.map(([paramName, paramInfo]: [string, unknown]) => {
              const param = paramInfo as Record<string, unknown>;
              return (
                <div key={paramName} className="text-xs">
                  <div className="flex items-start gap-2 flex-wrap">
                    <span className="font-mono text-muted-foreground font-medium break-all">
                      {paramName}
                    </span>
                    <div className="flex items-center gap-1">
                      {typeof param?.type === 'string' && param.type && (
                        <Badge variant="outline" className="text-xs h-4 px-1 whitespace-nowrap">
                          {param.type}
                        </Badge>
                      )}
                      {param?.required !== false && (
                        <span className="text-destructive text-xs">*</span>
                      )}
                    </div>
                  </div>
                  {typeof param?.description === 'string' && param.description && (
                    <p className="text-muted-foreground/70 mt-0.5 leading-relaxed break-words">
                      {param.description}
                    </p>
                  )}
                  {param?.default !== undefined && (
                    <p className="text-muted-foreground/60 mt-0.5 break-all">
                      Default: {JSON.stringify(param.default)}
                    </p>
                  )}
                </div>
              );
            })}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
});

ToolParameters.displayName = "ToolParameters";

// 优化的工具项组件 - 减少重新渲染
const ToolItem = memo(({
  tool,
  isEnabled,
  onToggle,
  serverName,
  searchTerm
}: {
  tool: { name: string; description: string; inputSchema?: Record<string, unknown> };
  isEnabled: boolean;
  onToggle: (checked: boolean) => void;
  serverName: string;
  searchTerm: string;
}) => {
  const isPending = globalPendingUpdates.get(serverName)?.has(tool.name) ?? false;

  // 高亮搜索匹配的文本
  const highlightText = useCallback((text: string, search: string) => {
    if (!search.trim()) return text;

    const regex = new RegExp(`(${search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);

    return parts.map((part, index) =>
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 dark:bg-yellow-800 rounded px-0.5">
          {part}
        </mark>
      ) : part
    );
  }, []);

  return (
    <div className="flex items-start justify-between p-3 bg-muted/30 rounded-lg gap-3 border border-muted hover:bg-muted/50 transition-colors">
      <div className="flex-1 min-w-0 overflow-hidden">
        <div className="flex items-start gap-2 mb-1 flex-wrap">
          <span className="font-mono text-sm font-medium break-all" title={tool.name}>
            {highlightText(tool.name, searchTerm)}
          </span>
          <div className="flex items-center gap-1 flex-wrap">
            {isEnabled && (
              <Badge variant="default" className="text-xs whitespace-nowrap">
                Enabled
              </Badge>
            )}
            {tool.description && (
              <button
                type="button"
                className="shrink-0 text-muted-foreground hover:text-foreground transition-colors"
                title={tool.description}
              >
                <Info size={12} />
              </button>
            )}
          </div>
        </div>
        {tool.description && (
          <p className="text-xs text-muted-foreground leading-relaxed mb-2 break-words" style={{
            display: '-webkit-box',
            WebkitLineClamp: 3,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden'
          }}>
            {highlightText(tool.description, searchTerm)}
          </p>
        )}
        <ToolParameters inputSchema={tool.inputSchema} />
      </div>
      <div className="shrink-0 self-start pt-1">
        <Switch
          checked={isEnabled}
          onCheckedChange={onToggle}
          disabled={isPending}
          className={cn(isPending && "opacity-50")}
        />
      </div>
    </div>
  );
});

ToolItem.displayName = "ToolItem";

// 高性能工具列表组件 - 添加搜索和排序功能
const ToolList = memo(({
  tools,
  enabledTools,
  onToolToggle,
  serverName,
  isExpanded
}: {
  tools: Array<{ name: string; description: string; inputSchema?: Record<string, unknown> }>;
  enabledTools: string[];
  onToolToggle: (toolName: string, checked: boolean) => void;
  serverName: string;
  isExpanded: boolean;
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [visibleCount, setVisibleCount] = useState(10); // 增加初始显示数量
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 防抖搜索
  const handleSearchChange = useCallback((value: string) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      startTransition(() => {
        setSearchTerm(value);
        setVisibleCount(10); // 重置显示数量
      });
    }, 150); // 减少防抖延迟
  }, []);

  // 优化的工具过滤和排序
  const { filteredAndSortedTools, totalCount } = useMemo(() => {
    let filtered = tools;

    // 搜索过滤
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = tools.filter(tool =>
        tool.name.toLowerCase().includes(searchLower) ||
        tool.description.toLowerCase().includes(searchLower)
      );
    }

    // 排序：启用的工具在前，然后按名称排序
    const sorted = filtered.sort((a, b) => {
      const aEnabled = enabledTools.includes(a.name);
      const bEnabled = enabledTools.includes(b.name);

      if (aEnabled && !bEnabled) return -1;
      if (!aEnabled && bEnabled) return 1;
      return a.name.localeCompare(b.name);
    });

    return {
      filteredAndSortedTools: sorted,
      totalCount: filtered.length
    };
  }, [tools, enabledTools, searchTerm]);

  const visibleTools = useMemo(() =>
    filteredAndSortedTools.slice(0, visibleCount),
    [filteredAndSortedTools, visibleCount]
  );

  const handleShowMore = useCallback(() => {
    startTransition(() => {
      setVisibleCount(prev => Math.min(prev + 10, totalCount));
    });
  }, [totalCount]);

  const clearSearch = useCallback(() => {
    setSearchTerm("");
    setVisibleCount(10);
  }, []);

  // 只渲染展开状态下的工具
  if (!isExpanded) return null;

  return (
    <div className="space-y-4">
      {/* 搜索栏 */}
      {tools.length > 5 && (
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={14} />
          <Input
            placeholder={`Search ${tools.length} tools...`}
            className="pl-8 pr-8 h-8 text-sm"
            defaultValue={searchTerm}
            onChange={(e) => handleSearchChange(e.target.value)}
          />
          {searchTerm && (
            <button
              type="button"
              onClick={clearSearch}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
            >
              <X size={14} />
            </button>
          )}
        </div>
      )}

      {/* 工具统计 */}
      <div className="flex items-center justify-between text-xs text-muted-foreground">
        <span>
          {searchTerm ? `${totalCount} of ${tools.length} tools` : `${tools.length} tools`}
          {enabledTools.length > 0 && (
            <span className="ml-2">• {enabledTools.length} enabled</span>
          )}
        </span>
        {enabledTools.length > 0 && totalCount > 0 && (
          <span className="text-primary">
            Enabled tools shown first
          </span>
        )}
      </div>

      {/* 工具列表 */}
      {totalCount === 0 ? (
        <div className="text-center text-muted-foreground text-sm py-8 border border-dashed rounded-lg">
          {searchTerm ? `No tools found matching "${searchTerm}"` : "No tools available from this server"}
        </div>
      ) : (
        <div className="space-y-3">
          {visibleTools.map((tool) => (
            <ToolItem
              key={tool.name}
              tool={tool}
              isEnabled={enabledTools.includes(tool.name)}
              onToggle={(checked) => onToolToggle(tool.name, checked)}
              serverName={serverName}
              searchTerm={searchTerm}
            />
          ))}

          {visibleCount < totalCount && (
            <button
              type="button"
              onClick={handleShowMore}
              className="w-full p-3 text-sm text-muted-foreground hover:text-foreground border border-dashed rounded-lg transition-colors"
            >
              Show {Math.min(10, totalCount - visibleCount)} more tools...
            </button>
          )}
        </div>
      )}
    </div>
  );
});

ToolList.displayName = "ToolList";

// 优化的服务器项组件 - 添加编辑功能
const ServerItem = memo(({
  server,
  isNew,
  isExpanded,
  onToggleServer,
  onDeleteServer,
  onEditServer,
  onToggleExpansion,
  onUpdateEnabledTools
}: {
  server: MCPServerMetadata;
  isNew: boolean;
  isExpanded: boolean;
  onToggleServer: (name: string, enabled: boolean) => void;
  onDeleteServer: (name: string) => void;
  onEditServer: (server: MCPServerMetadata) => void;
  onToggleExpansion: (name: string) => void;
  onUpdateEnabledTools: (serverName: string, enabledTools: string[]) => void;
}) => {
  // 使用useMemo缓存计算结果
  const { enabledTools, enabledCount, totalCount } = useMemo(() => {
    // 如果服务器被禁用，没有工具被启用
    if (!server.enabled) {
      return {
        enabledTools: [],
        enabledCount: 0,
        totalCount: server.tools.length
      };
    }
    
    // 处理启用工具列表
    const enabledToolsStr = server.enabled_tools?.trim();
    const enabled = enabledToolsStr && enabledToolsStr.length > 0
      ? enabledToolsStr.split(',').map(tool => tool.trim()).filter(tool => tool.length > 0)
      : server.tools.map(tool => tool.name); // 默认启用所有工具（仅当服务器启用且未指定工具时）
    
    return {
      enabledTools: enabled,
      enabledCount: enabled.length,
      totalCount: server.tools.length
    };
  }, [server.enabled, server.enabled_tools, server.tools]);

  // 优化的防抖处理 - 使用全局状态减少组件内状态
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    const handleToolToggle = useCallback((toolName: string, checked: boolean) => {
    // 更新全局待处理状态
    const serverPending = globalPendingUpdates.get(server.name) ?? new Set();
    serverPending.add(toolName);
    globalPendingUpdates.set(server.name, serverPending);
    
    const newEnabledTools = checked
      ? [...enabledTools, toolName]
      : enabledTools.filter(t => t !== toolName);
    
    // 清除之前的延迟更新
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
    
    // 使用startTransition来标记非紧急更新
    debounceTimeoutRef.current = setTimeout(() => {
      startTransition(() => {
        // 如果没有启用的工具，禁用整个服务器
        if (newEnabledTools.length === 0) {
          onToggleServer(server.name, false);
        } else {
          onUpdateEnabledTools(server.name, newEnabledTools);
        }
        // 清除待处理状态
        const pending = globalPendingUpdates.get(server.name);
        if (pending) {
          pending.delete(toolName);
          if (pending.size === 0) {
            globalPendingUpdates.delete(server.name);
          }
        }
      });
    }, 100);
  }, [enabledTools, server.name, onUpdateEnabledTools, onToggleServer]);

  return (
    <div
      className={cn(
        "border rounded-lg transition-all duration-200 hover:shadow-sm",
        !server.enabled && "opacity-60",
        isNew && "bg-muted/50 ring-2 ring-primary/20"
      )}
    >
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center gap-3 flex-1 min-w-0">
          <Switch
            checked={server.enabled}
            onCheckedChange={(enabled) => onToggleServer(server.name, enabled)}
          />
          <div
            className={cn(
              "flex flex-col items-start flex-1 min-w-0 overflow-hidden",
              !server.enabled && "text-muted-foreground",
            )}
          >
            <div
              className={cn(
                "mb-1 flex items-center gap-2 flex-wrap w-full",
                !server.enabled && "opacity-70",
              )}
            >
              <div className="font-medium break-all" title={server.name}>
                {server.name}
              </div>
              <div className="flex items-center gap-1 flex-wrap">
                {!server.enabled && (
                  <Badge variant="secondary" className="text-xs whitespace-nowrap">
                    Disabled
                  </Badge>
                )}
                <Badge variant="outline" className="text-xs whitespace-nowrap">
                  {server.transport}
                </Badge>
                {isNew && (
                  <Badge className="text-xs animate-pulse whitespace-nowrap">
                    New
                  </Badge>
                )}
              </div>
            </div>
            <div className={cn(
              "text-sm text-muted-foreground",
              !server.enabled && "opacity-50",
            )}>
              {totalCount} tools
              {enabledCount < totalCount && (
                <span className="ml-1">
                  • {enabledCount} enabled
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-1">
          <MCPServerDialog
            mode="edit"
            serverToEdit={server}
            onEdit={onEditServer}
            trigger={
              <Button
                variant="ghost"
                size="sm"
                className="hover:bg-muted transition-colors"
                title="Edit server configuration"
              >
                <Edit size={14} />
              </Button>
            }
          />
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onToggleExpansion(server.name)}
            className="hover:bg-muted transition-colors"
            title={isExpanded ? "Hide tools" : "Show tools"}
          >
            <Settings size={14} />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDeleteServer(server.name)}
            className="hover:bg-destructive/10 hover:text-destructive transition-colors"
            title="Delete server"
          >
            <Trash size={14} />
          </Button>
        </div>
      </div>

      {isExpanded && (
        <div className="border-t bg-muted/20">
          <div className="p-4">
            <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
              <PencilRuler size={14} />
              Tools ({totalCount})
              {enabledCount < totalCount && (
                <span className="text-muted-foreground">
                  • {enabledCount} enabled
                </span>
              )}
            </h4>
            <ToolList
              tools={server.tools}
              enabledTools={enabledTools}
              onToolToggle={handleToolToggle}
              serverName={server.name}
              isExpanded={isExpanded}
            />
          </div>
        </div>
      )}
    </div>
  );
});

ServerItem.displayName = "ServerItem";

export const MCPTab: Tab = ({ settings, onChange }) => {
  const [newlyAdded, setNewlyAdded] = useState(false);
  const [isExampleOpen, setIsExampleOpen] = useState(false);
  const [expandedServers, setExpandedServers] = useState<Set<string>>(new Set());

  // 使用useMemo缓存服务器列表，避免不必要的重新计算
  const servers = useMemo(() => settings.mcp.servers, [settings.mcp.servers]);

  const handleAddServers = useCallback(
    (newServers: MCPServerMetadata[]) => {
      const merged = mergeServers(servers, newServers);

      // 使用startTransition来优化大量更新
      startTransition(() => {
        onChange({ ...settings, mcp: { ...settings.mcp, servers: merged } });
        setNewlyAdded(true);
        setTimeout(() => setNewlyAdded(false), 3000); // 延长新增效果显示时间
      });

      // 平滑滚动到顶部
      requestAnimationFrame(() => {
        document.getElementById("settings-content-scrollable")?.scrollTo({
          top: 0,
          behavior: "smooth",
        });
      });
    },
    [onChange, settings, servers],
  );

  const handleEditServer = useCallback(
    (updatedServer: MCPServerMetadata) => {
      const merged = servers.map((server) =>
        server.name === updatedServer.name ? updatedServer : server,
      );

      startTransition(() => {
        onChange({ ...settings, mcp: { ...settings.mcp, servers: merged } });
      });
    },
    [onChange, settings, servers],
  );

  const handleDeleteServer = useCallback(
    (name: string) => {
      const merged = servers.filter((server) => server.name !== name);

      startTransition(() => {
        onChange({ ...settings, mcp: { ...settings.mcp, servers: merged } });
      });
    },
    [onChange, settings, servers],
  );

    const handleToggleServer = useCallback(
    (name: string, enabled: boolean) => {
      const merged = servers.map((server) => {
        if (server.name === name) {
          const updatedServer = { ...server, enabled };
          // 如果服务器被启用且没有指定启用工具，则启用所有工具
          if (enabled && (!server.enabled_tools || server.enabled_tools.trim() === '')) {
            updatedServer.enabled_tools = server.tools.map(tool => tool.name).join(',');
          }
          return updatedServer;
        }
        return server;
      });
      
      startTransition(() => {
        onChange({ ...settings, mcp: { ...settings.mcp, servers: merged } });
      });
    },
    [onChange, settings, servers],
  );

  const handleToggleServerExpansion = useCallback(
    (serverName: string) => {
      setExpandedServers(prev => {
        const newSet = new Set(prev);
        if (newSet.has(serverName)) {
          newSet.delete(serverName);
        } else {
          newSet.add(serverName);
        }
        return newSet;
      });
    },
    [],
  );

  // 批量更新优化 - 减少状态更新频率
  const handleUpdateEnabledTools = useCallback(
    (serverName: string, enabledTools: string[]) => {
      const merged = servers.map((server) => {
        if (server.name === serverName) {
          return {
            ...server,
            enabled_tools: enabledTools.join(','),
            tools: server.tools.map(tool => ({
              ...tool,
              enabled: enabledTools.includes(tool.name)
            })),
            updatedAt: Date.now()
          };
        }
        return server;
      });

      // 使用startTransition优化更新
      startTransition(() => {
        onChange({ ...settings, mcp: { ...settings.mcp, servers: merged } });
      });
    },
    [onChange, settings, servers],
  );

  return (
    <div className="flex flex-col gap-6">
      <header>
        <div className="flex items-center justify-between gap-2">
          <h1 className="text-lg font-medium">MCP Servers</h1>
          <AddMCPServerDialog onAdd={handleAddServers} />
        </div>
        <div className="text-muted-foreground text-sm leading-relaxed">
          The Model Context Protocol boosts DeerFlow by integrating external
          tools for tasks like private domain searches, web browsing, food
          ordering, and more.
          <a
            className="ml-1 text-primary hover:underline transition-colors"
            target="_blank"
            href="https://modelcontextprotocol.io/"
          >
            Learn more about MCP.
          </a>
        </div>

        <Collapsible open={isExampleOpen} onOpenChange={setIsExampleOpen}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="mt-3 p-0 h-auto text-sm text-muted-foreground hover:text-foreground transition-colors">
              <span className="flex items-center gap-1">
                {isExampleOpen ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                View configuration example
              </span>
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-3">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Configuration Example</CardTitle>
                <CardDescription>
                  You can specify which tools to enable for each server using the enabled_tools field.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <pre className="text-xs bg-muted p-3 rounded-md overflow-x-auto font-mono whitespace-pre-wrap break-all">
                  {`{
  "mcpServers": {
    "my-server": {
      "enabled_tools": "tool1,tool2,tool3",
      "add_to_agents": "researcher",
      "url": "http://localhost:8080/mcp/sse"
    }
  }
}`}
                </pre>
              </CardContent>
            </Card>
          </CollapsibleContent>
        </Collapsible>
      </header>

      <main>
        {servers.length === 0 ? (
          <div className="bg-muted/50 flex flex-col items-center justify-center rounded-lg p-12 border border-dashed">
            <Blocks className="text-muted-foreground mb-4" size={48} />
            <div className="text-muted-foreground text-center">
              <p className="text-lg font-medium mb-2">No MCP servers configured</p>
              <p className="text-sm">
                Add your first server to get started with external tools and
                services.
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {servers.map((server) => {
              const isNew = newlyAdded && !!server.createdAt && server.createdAt > Date.now() - 4000;
              const isExpanded = expandedServers.has(server.name);

              return (
                <ServerItem
                  key={server.name}
                  server={server}
                  isNew={isNew}
                  isExpanded={isExpanded}
                  onToggleServer={handleToggleServer}
                  onDeleteServer={handleDeleteServer}
                  onEditServer={handleEditServer}
                  onToggleExpansion={handleToggleServerExpansion}
                  onUpdateEnabledTools={handleUpdateEnabledTools}
                />
              );
            })}
          </div>
        )}
      </main>
    </div>
  );
};

MCPTab.displayName = "MCP";
MCPTab.icon = Blocks;
MCPTab.badge = "Beta";

function mergeServers(
  existing: MCPServerMetadata[],
  added: MCPServerMetadata[],
): MCPServerMetadata[] {
  const serverMap = new Map(existing.map((server) => [server.name, server]));

  for (const addedServer of added) {
    addedServer.createdAt = Date.now();
    addedServer.updatedAt = Date.now();
    serverMap.set(addedServer.name, addedServer);
  }

  const result = Array.from(serverMap.values());
  result.sort((a, b) => b.createdAt - a.createdAt);
  return result;
}
