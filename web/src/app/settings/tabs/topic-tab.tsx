// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { Database, RotateCcw } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useState, useEffect } from "react";

import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { getProjectInfo } from "~/core/api/project";

import type { Tab } from "./types";

export const TopicTab: Tab = ({ settings, onChange }) => {
  const searchParams = useSearchParams();
  const project = searchParams.get("project");
  
  const [codeTopicId, setCodeTopicId] = useState("");
  const [docTopicId, setDocTopicId] = useState("");
  const [defaultConfig, setDefaultConfig] = useState<{ codeTopicId: string; docTopicId: string }>({
    codeTopicId: "",
    docTopicId: ""
  });
  const [loading, setLoading] = useState(false);
  const [initialized, setInitialized] = useState(false);

  // 从API获取项目默认配置
  useEffect(() => {
    const fetchProjectConfig = async () => {
      if (!project) return;
      
      setLoading(true);
      try {
        const projectInfo = await getProjectInfo(project);
        setDefaultConfig({
          codeTopicId: projectInfo.code_topic,
          docTopicId: projectInfo.doc_topic
        });
      } catch (error) {
        console.error('Error fetching project config:', error);
      } finally {
        setLoading(false);
      }
    };

    void fetchProjectConfig();
  }, [project]);

  // 初始化项目配置：如果storage中没有配置，则使用默认值并保存
  useEffect(() => {
    if (project && defaultConfig.codeTopicId && !initialized) {
      const currentTopics = settings.topics?.projectTopics?.[project];
      
      if (!currentTopics) {
        // 如果没有配置，使用默认值并保存到storage
        const updatedTopics = {
          ...settings.topics,
          projectTopics: {
            ...settings.topics?.projectTopics,
            [project]: {
              codeTopicId: defaultConfig.codeTopicId,
              docTopicId: defaultConfig.docTopicId,
            },
          },
        };
        onChange({ topics: updatedTopics });
      }
      setInitialized(true);
    }
  }, [defaultConfig, project, settings.topics, onChange, initialized]);

  // 从storage中读取配置并显示在界面上，如果没有storage配置则使用默认配置
  useEffect(() => {
    if (project) {
      const topics = settings.topics?.projectTopics?.[project];
      // 如果storage中有配置就用storage的，否则用默认配置
      setCodeTopicId(topics?.codeTopicId ?? defaultConfig.codeTopicId ?? "");
      setDocTopicId(topics?.docTopicId ?? defaultConfig.docTopicId ?? "");
    }
  }, [project, settings.topics, defaultConfig]);

  // 自动提交变更
  useEffect(() => {
    if (project && initialized) {
      const currentTopics = settings.topics?.projectTopics?.[project];
      const currentCodeTopicId = currentTopics?.codeTopicId ?? "";
      const currentDocTopicId = currentTopics?.docTopicId ?? "";
      
      const hasChanges = 
        currentCodeTopicId !== codeTopicId ||
        currentDocTopicId !== docTopicId;

      if (hasChanges) {
        const updatedTopics = {
          ...settings.topics,
          projectTopics: {
            ...settings.topics?.projectTopics,
            [project]: {
              codeTopicId,
              docTopicId,
            },
          },
        };
        
        onChange({ topics: updatedTopics });
      }
    }
  }, [project, codeTopicId, docTopicId, settings.topics, onChange, initialized]);

  const handleReset = () => {
    if (!defaultConfig.codeTopicId && !defaultConfig.docTopicId) {
      return;
    }
    
    // 重置为默认值
    setCodeTopicId(defaultConfig.codeTopicId);
    setDocTopicId(defaultConfig.docTopicId);
    
    // 立即保存到设置中
    if (project) {
      const updatedTopics = {
        ...settings.topics,
        projectTopics: {
          ...settings.topics?.projectTopics,
          [project]: {
            codeTopicId: defaultConfig.codeTopicId,
            docTopicId: defaultConfig.docTopicId,
          },
        },
      };
      onChange({ topics: updatedTopics });
    }
  };

  if (!project) {
    return (
      <div className="flex flex-col gap-4">
        <header>
          <h1 className="text-lg font-medium">DocChain Topics</h1>
        </header>
        <main>
          <div className="text-sm text-muted-foreground">
            请先通过URL参数选择项目才能配置Topic ID（例如：?project=wfm）
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      <header>
        <h1 className="text-lg font-medium">DocChain Topics</h1>
        <p className="text-sm text-muted-foreground">
          为项目 <strong>{project}</strong> 配置专用的DocChain Topic ID
        </p>
      </header>
      <main className="space-y-6">
        <div className="space-y-4">
          <div className="bg-muted/50 border rounded-lg p-3">
            <h3 className="text-sm font-medium mb-2">项目默认配置</h3>
            {loading ? (
              <div className="text-sm text-muted-foreground">
                正在加载项目配置...
              </div>
            ) : (
              <div className="text-sm text-muted-foreground space-y-1">
                <div>代码Topic ID: <code className="bg-background px-1 rounded">{defaultConfig.codeTopicId}</code></div>
                <div>文档Topic ID: <code className="bg-background px-1 rounded">{defaultConfig.docTopicId}</code></div>
              </div>
            )}
            <p className="text-xs text-muted-foreground mt-2">
              如果不设置自定义配置，将使用上述默认值
            </p>
          </div>

          <div>
            <Label htmlFor="codeTopicId" className="text-sm font-medium">
              代码Topic ID
            </Label>
            <Input
              id="codeTopicId"
              value={codeTopicId}
              onChange={(e) => setCodeTopicId(e.target.value)}
              placeholder="输入代码Topic ID"
              className="mt-1 w-80"
            />
            <p className="text-xs text-muted-foreground mt-1">
              用于搜索代码文件的DocChain Topic ID
            </p>
          </div>

          <div>
            <Label htmlFor="docTopicId" className="text-sm font-medium">
              文档Topic ID
            </Label>
            <Input
              id="docTopicId"
              value={docTopicId}
              onChange={(e) => setDocTopicId(e.target.value)}
              placeholder="输入文档Topic ID"
              className="mt-1 w-80"
            />
            <p className="text-xs text-muted-foreground mt-1">
              用于搜索文档的DocChain Topic ID
            </p>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            配置会自动保存，点击对话框底部的Save按钮来应用所有设置变更。
          </div>
          <Button variant="outline" size="sm" onClick={handleReset} className="flex items-center gap-2">
            <RotateCcw size={14} />
            重置为默认值
          </Button>
        </div>
      </main>
    </div>
  );
};

TopicTab.displayName = "Topic";
TopicTab.icon = Database; 