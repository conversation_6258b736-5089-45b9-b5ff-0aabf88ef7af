// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import "katex/dist/katex.min.css";
import { Check, Copy } from "lucide-react";
import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import ReactMarkdown, {
  type Options as ReactMarkdownOptions,
} from "react-markdown";
import rehypeKatex from "rehype-katex";
import remarkGfm from "remark-gfm";
import remarkMath from "remark-math";

import { Button } from "~/components/ui/button";
import { rehypeSplitWordsIntoSpans } from "~/core/rehype";
import { autoFixMarkdown } from "~/core/utils/markdown";
import { cn } from "~/lib/utils";

import Image from "./image";
import { Link } from "./link";
import { Tooltip } from "./tooltip";

// 优化：预定义插件以避免重复创建
const REMARK_PLUGINS = [remarkGfm, remarkMath];
const REHYPE_PLUGINS_STATIC = [rehypeKatex];
const REHYPE_PLUGINS_ANIMATED = [rehypeKatex, rehypeSplitWordsIntoSpans];

// 优化：大文档的阈值设置，调整为更合理的值
const LARGE_DOCUMENT_THRESHOLD = 20000; // 20KB的字符数，避免小文档被虚拟化
const CHUNK_SIZE = 8000; // 增加块大小以减少块数量，提升性能

// 优化：虚拟块组件，减少重复渲染
const VirtualChunk = memo(function VirtualChunk({
  chunk,
  index,
  isVisible,
  components,
  ...props
}: {
  chunk: string;
  index: number;
  isVisible: boolean;
  components: ReactMarkdownOptions["components"];
} & ReactMarkdownOptions) {
  // 缓存处理后的内容
  const processedChunk = useMemo(() => {
    try {
      const katexProcessed = processKatexInMarkdown(chunk);
      const quotesDropped = dropMarkdownQuote(katexProcessed);
      return autoFixMarkdown(quotesDropped ?? "");
    } catch (error) {
      // 生产环境中避免console输出以提升性能
      if (process.env.NODE_ENV === 'development') {
        console.warn('Error processing chunk:', error);
      }
      return chunk;
    }
  }, [chunk]);

  return (
    <div style={{ minHeight: '200px' }}>
      {isVisible ? (
        <ReactMarkdown
          remarkPlugins={REMARK_PLUGINS}
          rehypePlugins={REHYPE_PLUGINS_STATIC}
          components={components}
          {...props}
        >
          {processedChunk}
        </ReactMarkdown>
      ) : (
        <div className="flex items-center justify-center h-48 text-gray-400">
          <div className="text-sm">Loading chunk {index + 1}...</div>
        </div>
      )}
    </div>
  );
});

// 优化：大文档虚拟化渲染组件
const VirtualizedMarkdown = memo(function VirtualizedMarkdown({
  content,
  className,
  style,
  enableCopy,
  checkLinkCredibility,
  ...props
}: {
  content: string;
  className?: string;
  style?: React.CSSProperties;
  enableCopy?: boolean;
  checkLinkCredibility?: boolean;
} & ReactMarkdownOptions) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [visibleChunks, setVisibleChunks] = useState<Set<number>>(new Set([0]));
  const [isIntersecting, setIsIntersecting] = useState(true);

  // 优化：将内容分块，考虑markdown结构以避免破坏格式
  const chunks = useMemo(() => {
    if (content.length <= CHUNK_SIZE) {
      return [content]; // 小文档不分块
    }
    
    const chunks: string[] = [];
    let currentIndex = 0;
    
    while (currentIndex < content.length) {
      let endIndex = Math.min(currentIndex + CHUNK_SIZE, content.length);
      
      // 尝试在合适的位置分割（避免破坏markdown结构）
      if (endIndex < content.length) {
        // 寻找最近的换行符
        const nearestNewline = content.lastIndexOf('\n', endIndex);
        if (nearestNewline > currentIndex + CHUNK_SIZE * 0.5) {
          endIndex = nearestNewline + 1;
        }
      }
      
      chunks.push(content.slice(currentIndex, endIndex));
      currentIndex = endIndex;
    }
    
    return chunks;
  }, [content]);

  // 交叉观察器设置
  useEffect(() => {
    if (!containerRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        if (entry) {
          setIsIntersecting(entry.isIntersecting);
        }
      },
      { threshold: 0.1 }
    );

    observer.observe(containerRef.current);
    return () => observer.disconnect();
  }, []);

  // 优化：懒加载逻辑，减少频繁的状态更新
  const handleScroll = useCallback(() => {
    if (!containerRef.current || !isIntersecting || chunks.length <= 1) return;

    const container = containerRef.current;
    const scrollTop = container.scrollTop;
    const containerHeight = container.clientHeight;
    const totalHeight = container.scrollHeight;

    // 计算需要显示的块，增加缓冲区
    const viewportRatio = (scrollTop + containerHeight / 2) / totalHeight;
    const centerChunk = Math.floor(viewportRatio * chunks.length);
    const bufferSize = 2; // 增加缓冲区大小
    
    const startChunk = Math.max(0, centerChunk - bufferSize);
    const endChunk = Math.min(chunks.length - 1, centerChunk + bufferSize);

    const newVisibleChunks = new Set<number>();
    for (let i = startChunk; i <= endChunk; i++) {
      newVisibleChunks.add(i);
    }

    // 只有当可见块发生变化时才更新状态
    if (newVisibleChunks.size !== visibleChunks.size || 
        ![...newVisibleChunks].every(chunk => visibleChunks.has(chunk))) {
      setVisibleChunks(newVisibleChunks);
    }
  }, [chunks.length, isIntersecting, visibleChunks]);

  // 优化：使用节流来减少滚动事件的处理频率
  useEffect(() => {
    const container = containerRef.current;
    if (!container || chunks.length <= 1) return;

    let throttleTimer: NodeJS.Timeout | null = null;
    const throttledHandleScroll = () => {
      if (throttleTimer) return;
      throttleTimer = setTimeout(() => {
        handleScroll();
        throttleTimer = null;
      }, 100); // 100ms节流
    };

    container.addEventListener('scroll', throttledHandleScroll, { passive: true });
    return () => {
      container.removeEventListener('scroll', throttledHandleScroll);
      if (throttleTimer) {
        clearTimeout(throttleTimer);
      }
    };
  }, [handleScroll, chunks.length]);

  const components: ReactMarkdownOptions["components"] = useMemo(() => {
    return {
      a: ({ href, children }) => (
                 <Link href={href} checkLinkCredibility={checkLinkCredibility ?? false}>
           {children}
         </Link>
      ),
      img: ({ src, alt }) => (
        <a href={src as string} target="_blank" rel="noopener noreferrer">
          <Image className="rounded" src={src as string} alt={alt ?? ""} />
        </a>
      ),
    };
  }, [checkLinkCredibility]);

  return (
    <div 
      ref={containerRef}
      className={cn(className, "prose dark:prose-invert max-h-[80vh] overflow-y-auto")} 
      style={style}
    >
      {chunks.map((chunk, index) => (
        <VirtualChunk
          key={index}
          chunk={chunk}
          index={index}
          isVisible={visibleChunks.has(index)}
          components={components}
          {...props}
        />
      ))}
      {enableCopy && (
        <div className="flex">
          <CopyButton content={content} />
        </div>
      )}
    </div>
  );
});

// 优化：添加memo以防止不必要的重新渲染
export const Markdown = memo(function Markdown({
  className,
  children,
  style,
  enableCopy,
  animated = false,
  checkLinkCredibility = false,
  ...props
}: ReactMarkdownOptions & {
  className?: string;
  enableCopy?: boolean;
  style?: React.CSSProperties;
  animated?: boolean;
  checkLinkCredibility?: boolean;
}) {
  const content = children ?? "";
  
  // 优化：对于大文档使用虚拟化渲染，同时考虑性能影响
  const isLargeDocument = useMemo(() => {
    if (typeof content !== 'string') return false;
    
    // 不仅考虑长度，还考虑复杂度（如表格、代码块等）
    const hasComplexElements = /```[\s\S]*?```|\|.*?\|/.test(content);
    const effectiveThreshold = hasComplexElements ? 
      LARGE_DOCUMENT_THRESHOLD * 0.5 : LARGE_DOCUMENT_THRESHOLD;
    
    return content.length > effectiveThreshold;
  }, [content]);

  if (isLargeDocument && typeof content === 'string') {
    return (
      <VirtualizedMarkdown
        content={content}
        className={className}
        style={style}
        enableCopy={enableCopy}
        checkLinkCredibility={checkLinkCredibility}
        {...props}
      />
    );
  }

  // 优化：缓存组件配置以避免重复创建
  const components: ReactMarkdownOptions["components"] = useMemo(() => {
    return {
      a: ({ href, children }) => (
        <Link href={href} checkLinkCredibility={checkLinkCredibility ?? false}>
          {children}
        </Link>
      ),
      img: ({ src, alt }) => (
        <a href={src as string} target="_blank" rel="noopener noreferrer">
          <Image className="rounded" src={src as string} alt={alt ?? ""} />
        </a>
      ),
    };
  }, [checkLinkCredibility]);

  // 优化：根据animated状态选择预定义的插件数组
  const rehypePlugins = animated ? REHYPE_PLUGINS_ANIMATED : REHYPE_PLUGINS_STATIC;
  
  // 优化：缓存处理后的markdown内容
  const processedContent = useMemo(() => {
    try {
      if (typeof content !== 'string') {
        return String(content || '');
      }
      
      const katexProcessed = processKatexInMarkdown(content);
      const quotesDropped = dropMarkdownQuote(katexProcessed);
      return autoFixMarkdown(quotesDropped ?? "");
    } catch (error) {
      // 生产环境中避免console输出以提升性能
      if (process.env.NODE_ENV === 'development') {
        console.warn('Error processing markdown content:', error);
      }
      return String(content || '');
    }
  }, [content]);
  
  return (
    <div className={cn(className, "prose dark:prose-invert")} style={style}>
      <ReactMarkdown
        remarkPlugins={REMARK_PLUGINS}
        rehypePlugins={rehypePlugins}
        components={components}
        {...props}
      >
        {processedContent}
      </ReactMarkdown>
      {enableCopy && typeof children === "string" && (
        <div className="flex">
          <CopyButton content={children} />
        </div>
      )}
    </div>
  );
});

// 优化：添加memo以防止不必要的重新渲染
const CopyButton = memo(function CopyButton({ content }: { content: string }) {
  const [copied, setCopied] = useState(false);
  return (
    <Tooltip title="Copy">
      <Button
        variant="outline"
        size="sm"
        className="rounded-full"
        onClick={async () => {
          try {
            await navigator.clipboard.writeText(content);
            setCopied(true);
            setTimeout(() => {
              setCopied(false);
            }, 1000);
          } catch (error) {
            // 生产环境中避免console输出以提升性能
            if (process.env.NODE_ENV === 'development') {
              console.error(error);
            }
          }
        }}
      >
        {copied ? (
          <Check className="h-4 w-4" />
        ) : (
          <Copy className="h-4 w-4" />
        )}{" "}
      </Button>
    </Tooltip>
  );
});

function processKatexInMarkdown(markdown?: string | null) {
  if (!markdown) return markdown;

  try {
    // 更安全的 KaTeX 语法转换
    let result = markdown;
    
    // 处理块级数学公式 (display math)
    result = result
      .replace(/\\\\\[/g, "$$") // Replace '\\[' with '$$'
      .replace(/\\\\\]/g, "$$") // Replace '\\]' with '$$'
      .replace(/\\\[/g, "$$")   // Replace '\[' with '$$'
      .replace(/\\\]/g, "$$");  // Replace '\]' with '$$'
    
    // 处理行内数学公式 (inline math)
    result = result
      .replace(/\\\\\(/g, "$")  // Replace '\\(' with '$'
      .replace(/\\\\\)/g, "$")  // Replace '\\)' with '$'
      .replace(/\\\(/g, "$")    // Replace '\(' with '$'
      .replace(/\\\)/g, "$");   // Replace '\)' with '$'
    
    return result;
  } catch (error) {
    // 生产环境中避免console输出以提升性能
    if (process.env.NODE_ENV === 'development') {
      console.warn('Error processing KaTeX in markdown:', error);
    }
    return markdown; // 返回原始内容以防出错
  }
}

function dropMarkdownQuote(markdown?: string | null) {
  if (!markdown) return markdown;
  
  // 更智能的代码块处理
  let result = markdown;
  
  // 只移除包装整个内容的markdown代码块
  // 检查是否整个内容被包装在一个代码块中
  const trimmed = result.trim();
  
  // 如果整个内容以```markdown开始并以```结束
  if (trimmed.startsWith('```markdown\n') && trimmed.endsWith('\n```')) {
    const inner = trimmed.slice('```markdown\n'.length, -'\n```'.length);
    result = inner;
  }
  // 如果整个内容以```text开始并以```结束  
  else if (trimmed.startsWith('```text\n') && trimmed.endsWith('\n```')) {
    const inner = trimmed.slice('```text\n'.length, -'\n```'.length);
    result = inner;
  }
  // 如果整个内容以```开始并以```结束（通用代码块）
  else if (trimmed.startsWith('```\n') && trimmed.endsWith('\n```')) {
    const inner = trimmed.slice('```\n'.length, -'\n```'.length);
    // 只有当内部内容不包含代码块时才移除外层包装
    if (!inner.includes('```')) {
      result = inner;
    }
  }
  
  return result;
}
