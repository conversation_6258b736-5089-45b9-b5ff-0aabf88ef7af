import { useMemo, useState } from "react";
import { useStore, useToolCalls } from "~/core/store";
import { useSettings, type SettingsData } from "~/core/store/settings-store";
import { Tooltip } from "./tooltip";
import { WarningFilled } from "@ant-design/icons";
import { resolveServiceURL } from "~/core/api/resolve-service-url";

export const Link = ({
  href,
  children,
  checkLinkCredibility = false,
}: {
  href: string | undefined;
  children: React.ReactNode;
  checkLinkCredibility: boolean;
}) => {

  const toolCalls = useToolCalls();
  const responding = useStore((state) => state.responding);
  const docChainSettings = useSettings('docChain') as SettingsData['docChain'];

  const [resolvedHref, setResolvedHref] = useState<string | undefined>(undefined);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // 用于后端 API 解析 DocChain 标识符的正则表达式
  const docchainIdPattern = /(?:^docchain_identifier_|^doc_|(?:^|\?|&)doc_id=)(\d+)(?:&|$)/;

  const handleClick = async (event: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
    if (!href) {
      return;
    }

    const docchainMatch = href.match(docchainIdPattern);
    // 如果是 DocChain 链接且尚未解析
    if (docchainMatch && docchainMatch[1] && resolvedHref === undefined) {
      const docId = docchainMatch[1];
      if (isNaN(Number(docId))) {
        // 如果 docId 不是数字，则允许默认导航发生
        return;
      }

      event.preventDefault(); // 在解析完成之前阻止默认导航
      setIsLoading(true);
      try {
        const docchainBaseUrl = docChainSettings?.baseUrl;
        const docchainApiKey = docChainSettings?.apiKey;

        let url = resolveServiceURL(`/api/resolve_docchain_url?doc_id=${docId}`);
        if (docchainBaseUrl) {
          url += `&docchain_base_url=${encodeURIComponent(docchainBaseUrl)}`;
        }
        if (docchainApiKey) {
          url += `&docchain_api_key=${encodeURIComponent(docchainApiKey)}`;
        }

        const response = await fetch(url);
        if (response.ok) {
          const data = await response.json();
          setResolvedHref(data.resolved_url);
          window.open(data.resolved_url, '_blank'); // 解析后进行导航
        } else {
          console.error(`Failed to resolve DocChain URL for doc_id ${docId}:`, response.status, response.statusText);
          // 如果解析失败，则不设置 resolvedHref，直接导航到原始 href。
          // 这样，在后续点击时，它会再次尝试解析。
          window.open(href, '_blank');
        }
      } catch (error) {
        console.error(`Error fetching DocChain URL for doc_id ${docId}:`, error);
        window.open(href, '_blank');
      } finally {
        setIsLoading(false);
      }
    } else if (resolvedHref) {
        // 如果是已解析的 DocChain 链接，或任何其他已将其 href 设置为 resolvedHref 的链接
        event.preventDefault(); // 确保我们控制导航
        window.open(resolvedHref, '_blank');
    } else {
        // 这是非 DocChain 链接，或 DocChain 链接的 docId 不是数字，或已解析。
        // 让默认的浏览器行为处理它。
    }
  };

  // 如果 resolvedHref 已设置，则 finalDisplayHref 将是 resolvedHref，否则是原始 href。
  const finalDisplayHref = resolvedHref || href;

  const isExternalUrl = (url: string | undefined): boolean => {
    return !!url && (url.startsWith("http://") || url.startsWith("https://"));
  };

  const credibleLinks = useMemo(() => {
    const links = new Set<string>();
    if (!checkLinkCredibility) return links;

    (toolCalls || []).forEach((call) => {
      if (call && call.name === "web_search" && call.result) {
        const result = JSON.parse(call.result) as Array<{ url: string }>;
        result.forEach((r) => {
          links.add(r.url);
        });
      }
    });
    return links;
  }, [toolCalls]);

  const isCredible = useMemo(() => {
    return checkLinkCredibility && finalDisplayHref && !responding
      ? credibleLinks.has(finalDisplayHref)
      : true;
  }, [credibleLinks, finalDisplayHref, responding, checkLinkCredibility]);

  return (
    <span className="inline-flex items-center gap-1.5">
      <a
        href={finalDisplayHref}
        target="_blank"
        rel="noopener noreferrer"
        onClick={handleClick}
      >
        {isLoading ? "Resolving link..." : children}
      </a>
      {!isCredible && (
        <Tooltip
          title="This link might be a hallucination from AI model and may not be reliable."
          delayDuration={300}
        >
          <WarningFilled className="text-sx transition-colors hover:!text-yellow-500" />
        </Tooltip>
      )}
    </span>
  );
};