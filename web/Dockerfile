##### DEPENDENCIES

FROM hub.iwhalecloud.com:5555/hub_docker/library/node:22-alpine AS deps

# 配置 Alpine 镜像源（使用阿里云镜像）
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
    apk add --no-cache libc6-compat openssl

WORKDIR /app

# 配置 npm registry
RUN npm config set registry http://npm.iwhalecloud.com:8081/repository/npm-all/

# Install dependencies based on the preferred package manager
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml\* ./

RUN \
    if [ -f yarn.lock ]; then \
        yarn config set registry http://npm.iwhalecloud.com:8081/repository/npm-all/ && \
        yarn --frozen-lockfile; \
    elif [ -f package-lock.json ]; then \
        npm ci; \
    elif [ -f pnpm-lock.yaml ]; then \
        npm config set registry http://npm.iwhalecloud.com:8081/repository/npm-all/ && \
        npm install -g pnpm@9.15.2 && \
        pnpm config set registry http://npm.iwhalecloud.com:8081/repository/npm-all/ && \
        pnpm i --no-frozen-lockfile; \
    else echo "Lockfile not found." && exit 1; \
    fi

##### BUILDER

FROM hub.iwhalecloud.com:5555/hub_docker/library/node:22-alpine AS builder

# 配置 Alpine 镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

WORKDIR /app
ARG NEXT_PUBLIC_API_URL

# 配置 npm registry
RUN npm config set registry http://npm.iwhalecloud.com:8081/repository/npm-all/

# 增加 Node.js 内存限制
ENV NODE_OPTIONS="--max-old-space-size=8192"

COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED=1

RUN \
    if [ -f yarn.lock ]; then \
        yarn config set registry http://npm.iwhalecloud.com:8081/repository/npm-all/ && \
        SKIP_ENV_VALIDATION=1 yarn build; \
    elif [ -f package-lock.json ]; then \
        SKIP_ENV_VALIDATION=1 npm run build; \
    elif [ -f pnpm-lock.yaml ]; then \
        npm config set registry http://npm.iwhalecloud.com:8081/repository/npm-all/ && \
        npm install -g pnpm@9.15.2 && \
        pnpm config set registry http://npm.iwhalecloud.com:8081/repository/npm-all/ && \
        SKIP_ENV_VALIDATION=1 pnpm run build; \
    else echo "Lockfile not found." && exit 1; \
    fi

##### RUNNER

FROM hub.iwhalecloud.com:5555/hub_docker/library/node:22-alpine AS runner

# 配置 Alpine 镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

WORKDIR /app

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

COPY --from=builder --chown=nextjs:nodejs /app/next.config.js ./
COPY --from=builder --chown=nextjs:nodejs /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./package.json

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

CMD ["node", "server.js"]
